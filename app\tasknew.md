# Organization Management Module for CMSVS

Your task is to build a complete module for managing client organizations and their service applications into our CMSVS system. The goal is to allow storing fixed organization details and linking them to multiple service application forms with file attachments.

## 🎯 Feature Overview
You are building a two-layer archive system that integrates with our existing CMSVS architecture:

1. **Organizations** (with official name + fixed address)
2. **Applications** (submitted under each org, with form data + files)

## 🪜 Implementation Steps

### Step 1: Database Design
Design two SQLAlchemy models that integrate with our existing database:

- **Organization**: name (unique), building, street, block
- **Application**: unique ID, name, benayat, mobile, notes, 4 attachment fields, foreign key to organization

📌 Store uploaded files under our existing structure:
`/uploads/{organization_name}/{application_id}/`

### Step 2: Create Organization Feature
Implement using our FastAPI + HTMX architecture:

- A form page to create new organizations (following our RTL Arabic UI patterns)
- A page to list all organizations as cards or table rows (consistent with our dashboard design)
- Store them in DB with validation (no duplicates)

### Step 3: Application Submission Feature
Implement:

- A dynamic form to create a new Application under a specific organization
- Fields: unique ID, name, benayat, mobile, notes, 4 file inputs
- On submit:
  - Store form data in DB
  - Save each file to a folder path based on org and app ID

### Step 4: File Handling
Ensure secure file storage under structured folders:

- e.g. uploads/Ministry of Health/APP-12345/attachment1.pdf
- Store paths in the database
- Follow our existing file size and type validation from config.py

### Step 5: Application Viewing
Create a detail view for each application (all fields + files)
List all applications for an organization (summary: ID, name, mobile)

### Step 6: Search Function
Add a search bar that looks up organizations by:
- Name
- Building / Street / Block
Show results with links to applications

### Step 7: UI/UX Design
Use our existing HTMX + TailwindCSS architecture with RTL support:
- Organization list: styled as cards or tables (consistent with our design system)
- Application form: grouped inputs + file preview labels
- Submit button with loading state
- Ensure mobile responsiveness following our mobile optimization guidelines

### Step 8: Testing
Test full flow:
- Create org → Add app → Upload files → View all
- Try edge cases (missing fields, empty uploads)
- Ensure compatibility with our mobile app interface

### Step 9: Integration
- Add appropriate routes in app/main.py
- Ensure proper authentication using our existing JWT system
- Add activity logging for all operations
- Implement proper error handling consistent with our system

## 🧠 Remember
- Follow our existing project structure and coding patterns
- Maintain RTL support for Arabic interface
- Ensure mobile responsiveness for all views
- Implement proper error handling and validation
- Use our existing authentication and authorization system