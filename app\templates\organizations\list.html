{% extends "base.html" %}

{% block title %}إدارة المؤسسات - إرشيف{% endblock %}

{% block content %}
<style>
/* Professional Organizations List Styling */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.page-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 8px 0 0 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.btn {
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.search-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-form {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    min-width: 300px;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.organizations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.organization-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
    border: 1px solid #e5e7eb;
}

.organization-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.org-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.org-name {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    flex: 1;
}

.org-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-outline {
    background: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background: #f3f4f6;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.org-details {
    margin-bottom: 16px;
}

.org-detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #6b7280;
}

.org-detail-icon {
    width: 16px;
    color: #9ca3af;
}

.org-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.org-stat {
    text-align: center;
}

.org-stat-number {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    display: block;
}

.org-stat-label {
    font-size: 12px;
    color: #6b7280;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 32px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #374151;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s;
}

.pagination-btn:hover {
    background: #f3f4f6;
}

.pagination-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.empty-state {
    text-align: center;
    padding: 64px 32px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-state-icon {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 16px;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.empty-state-text {
    color: #6b7280;
    margin-bottom: 24px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .page-header {
        padding: 24px;
        flex-direction: column;
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .organizations-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .search-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-input {
        min-width: auto;
    }
    
    .org-header {
        flex-direction: column;
        gap: 12px;
    }
    
    .org-actions {
        justify-content: center;
    }
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h1 class="page-title">إدارة المؤسسات</h1>
            <p class="page-subtitle">إدارة وعرض جميع المؤسسات المسجلة في النظام</p>
        </div>
        <div class="header-actions">
            <a href="/organizations/new" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة مؤسسة جديدة
            </a>
        </div>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <form class="search-form" method="get">
            <input 
                type="text" 
                name="search" 
                class="search-input" 
                placeholder="البحث في المؤسسات (الاسم، المبنى، الشارع، المجمع)..."
                value="{{ search }}"
            >
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-search"></i>
                بحث
            </button>
            {% if search %}
            <a href="/organizations/" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                مسح البحث
            </a>
            {% endif %}
        </form>
    </div>

    <!-- Organizations Grid -->
    {% if organizations %}
    <div class="organizations-grid">
        {% for org in organizations %}
        <div class="organization-card">
            <div class="org-header">
                <h3 class="org-name">{{ org.name }}</h3>
                <div class="org-actions">
                    <a href="/organizations/{{ org.id }}" class="btn btn-sm btn-outline">
                        <i class="fas fa-eye"></i>
                        عرض
                    </a>
                    <a href="/organizations/{{ org.id }}/edit" class="btn btn-sm btn-outline">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <button 
                        onclick="deleteOrganization({{ org.id }}, '{{ org.name }}')" 
                        class="btn btn-sm btn-danger"
                    >
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            </div>
            
            <div class="org-details">
                <div class="org-detail-item">
                    <i class="fas fa-building org-detail-icon"></i>
                    <span>{{ org.building }}</span>
                </div>
                <div class="org-detail-item">
                    <i class="fas fa-road org-detail-icon"></i>
                    <span>{{ org.street }}</span>
                </div>
                <div class="org-detail-item">
                    <i class="fas fa-map-marker-alt org-detail-icon"></i>
                    <span>{{ org.block }}</span>
                </div>
                {% if org.contact_person %}
                <div class="org-detail-item">
                    <i class="fas fa-user org-detail-icon"></i>
                    <span>{{ org.contact_person }}</span>
                </div>
                {% endif %}
                {% if org.contact_phone %}
                <div class="org-detail-item">
                    <i class="fas fa-phone org-detail-icon"></i>
                    <span>{{ org.contact_phone }}</span>
                </div>
                {% endif %}
            </div>
            
            <div class="org-stats">
                <div class="org-stat">
                    <span class="org-stat-number">{{ org.applications_count }}</span>
                    <span class="org-stat-label">الطلبات</span>
                </div>
                <div class="org-stat">
                    <span class="org-stat-number">{{ org.created_at.strftime('%Y-%m-%d') }}</span>
                    <span class="org-stat-label">تاريخ الإنشاء</span>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if total_pages > 1 %}
    <div class="pagination">
        {% if has_prev %}
        <a href="?page={{ page - 1 }}{% if search %}&search={{ search }}{% endif %}" class="pagination-btn">
            <i class="fas fa-chevron-right"></i>
            السابق
        </a>
        {% endif %}
        
        {% for p in range(1, total_pages + 1) %}
            {% if p == page %}
            <span class="pagination-btn active">{{ p }}</span>
            {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
            <a href="?page={{ p }}{% if search %}&search={{ search }}{% endif %}" class="pagination-btn">{{ p }}</a>
            {% elif p == 4 and page > 5 %}
            <span class="pagination-btn">...</span>
            {% elif p == total_pages - 3 and page < total_pages - 4 %}
            <span class="pagination-btn">...</span>
            {% endif %}
        {% endfor %}
        
        {% if has_next %}
        <a href="?page={{ page + 1 }}{% if search %}&search={{ search }}{% endif %}" class="pagination-btn">
            التالي
            <i class="fas fa-chevron-left"></i>
        </a>
        {% endif %}
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-building"></i>
        </div>
        <h3 class="empty-state-title">
            {% if search %}
            لا توجد نتائج للبحث
            {% else %}
            لا توجد مؤسسات مسجلة
            {% endif %}
        </h3>
        <p class="empty-state-text">
            {% if search %}
            لم يتم العثور على مؤسسات تطابق معايير البحث "{{ search }}"
            {% else %}
            ابدأ بإضافة مؤسسة جديدة لإدارة الطلبات والملفات
            {% endif %}
        </p>
        {% if not search %}
        <a href="/organizations/new" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            إضافة مؤسسة جديدة
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>

<script>
function deleteOrganization(orgId, orgName) {
    if (confirm(`هل أنت متأكد من حذف المؤسسة "${orgName}"؟\n\nلن يمكن التراجع عن هذا الإجراء.`)) {
        fetch(`/organizations/${orgId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف المؤسسة');
        });
    }
}
</script>
{% endblock %}
