#!/usr/bin/env python3
"""
Test script to verify title processing specifically
"""

import sys

def test_title_processing():
    """Test the exact title processing logic used in PDF generation"""
    print("Testing title processing for PDF generation...")
    
    # Test both report titles
    test_titles = [
        "تقرير نشاط المستخدمين - 3 أشهر",
        "تقرير أنشطة الطلبات - 6 أشهر",
    ]
    
    for i, title_raw in enumerate(test_titles, 1):
        print(f"\nTest {i}: Processing title")
        print(f"Original: '{title_raw}'")
        
        try:
            from arabic_reshaper import reshape
            from bidi.algorithm import get_display
            
            # Explicitly reshape and process the title (same logic as in PDF)
            title_reshaped = reshape(title_raw)
            title_processed = get_display(title_reshaped)
            
            print(f"Reshaped: '{title_reshaped}'")
            print(f"Final:    '{title_processed}'")
            print("✓ Title processing successful")
            
        except ImportError as e:
            print(f"✗ Import error: {e}")
        except Exception as e:
            print(f"✗ Processing error: {e}")
    
    # Test with Arabic numbers conversion
    print(f"\n=== Testing with Arabic number conversion ===")
    
    def convert_to_arabic_numbers(text):
        """Convert English numbers to Arabic-Indic numbers"""
        english_to_arabic = {
            '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
            '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
        }
        
        if isinstance(text, (int, float)):
            text = str(text)
        
        for eng, ara in english_to_arabic.items():
            text = text.replace(eng, ara)
        
        return text
    
    periods = [3, 6, 12]
    for period in periods:
        title_raw = f"تقرير أنشطة الطلبات - {convert_to_arabic_numbers(str(period))} أشهر"
        print(f"\nPeriod {period}:")
        print(f"With Arabic numbers: '{title_raw}'")
        
        try:
            from arabic_reshaper import reshape
            from bidi.algorithm import get_display
            
            title_reshaped = reshape(title_raw)
            title_processed = get_display(title_reshaped)
            
            print(f"Final processed:     '{title_processed}'")
            print("✓ Success")
            
        except Exception as e:
            print(f"✗ Error: {e}")
    
    return True

if __name__ == "__main__":
    try:
        success = test_title_processing()
        print("\nTitle processing test completed!")
        sys.exit(0)
    except Exception as e:
        print(f"Title processing test failed: {e}")
        sys.exit(1)
