{% extends "base.html" %}

{% block title %}الإشعارات - إرشيف{% endblock %}

{% block head %}
<style>
    .notification-item {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-right: 4px solid transparent;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(226, 232, 240, 0.8);
        overflow: hidden;
        position: relative;
    }

    .notification-item::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        background: transparent;
        transition: all 0.3s ease;
    }

    .notification-item.unread {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-color: rgba(59, 130, 246, 0.2);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(59, 130, 246, 0.04);
    }

    .notification-item.unread::before {
        background: linear-gradient(180deg, #3b82f6 0%, #2563eb 100%);
    }

    .notification-item:hover {
        transform: translateY(-2px) translateX(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);
        border-color: rgba(59, 130, 246, 0.3);
    }

    .notification-priority-high::before {
        background: linear-gradient(180deg, #ef4444 0%, #dc2626 100%) !important;
    }

    .notification-priority-urgent {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
        border-color: rgba(239, 68, 68, 0.2) !important;
    }

    .notification-priority-urgent::before {
        background: linear-gradient(180deg, #dc2626 0%, #b91c1c 100%) !important;
    }
    
    .notification-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.1);
        border: 3px solid rgba(255, 255, 255, 0.9);
        position: relative;
        flex-shrink: 0;
    }

    .notification-icon::before {
        content: '';
        position: absolute;
        inset: -2px;
        border-radius: 50%;
        padding: 2px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
    }

    .notification-type-request_status_changed {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    }
    .notification-type-request_created {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    .notification-type-request_updated {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    .notification-type-admin_message {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
    .notification-type-system_announcement {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    .notification-time {
        font-size: 0.875rem;
        color: #6b7280;
        display: flex;
        align-items: center;
        gap: 6px;
        background: rgba(107, 114, 128, 0.1);
        padding: 4px 8px;
        border-radius: 6px;
        width: fit-content;
        margin-top: 8px;
    }

    .notification-actions {
        opacity: 0;
        transition: all 0.3s ease;
        transform: translateY(4px);
    }

    .notification-item:hover .notification-actions {
        opacity: 1;
        transform: translateY(0);
    }

    /* Enhanced notification content styling */
    .notification-content {
        flex: 1;
        min-width: 0;
    }

    .notification-title {
        font-size: 1.125rem;
        font-weight: 700;
        color: #1f2937;
        line-height: 1.4;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .notification-message {
        font-size: 0.95rem;
        color: #4b5563;
        line-height: 1.5;
        margin-bottom: 12px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .notification-unread-indicator {
        position: absolute;
        top: 16px;
        left: 16px;
        width: 8px;
        height: 8px;
        background: #3b82f6;
        border-radius: 50%;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    .filter-tabs {
        border-bottom: 2px solid #e5e7eb;
        margin-bottom: 1.5rem;
    }
    
    .filter-tab {
        padding: 0.75rem 1.5rem;
        border-bottom: 2px solid transparent;
        color: #6b7280;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .filter-tab.active {
        color: #3b82f6;
        border-bottom-color: #3b82f6;
    }
    
    .filter-tab:hover {
        color: #1d4ed8;
    }
    
    .notification-badge {
        background-color: #ef4444;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        margin-right: 0.5rem;
    }

    /* Page container and header styles - matching admin pages */
    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 24px;
        background: #f8fafc;
        min-height: 100vh;
    }

    .page-header {
        text-align: right;
        padding: 32px 24px;
        margin-bottom: 32px;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    /* Mobile Optimizations - Only for screens 768px and below */
    @media (max-width: 768px) {
        /* Page container mobile optimization */
        .page-container {
            padding: 12px !important;
        }

        /* Page header mobile optimization - matching admin pages pattern */
        .page-header {
            padding: 16px !important;
            margin-bottom: 20px !important;
            border-radius: 8px !important;
        }

        .page-header .flex {
            flex-direction: column !important;
            gap: 16px !important;
            align-items: stretch !important;
        }

        .page-header h1 {
            font-size: 1.5rem !important;
            margin-bottom: 0 !important;
            text-align: center !important;
        }

        /* Action buttons mobile styling */
        .page-header .flex.items-center.space-x-4 {
            flex-direction: column !important;
            gap: 12px !important;
            width: 100% !important;
        }

        .page-header .flex.items-center.space-x-4 button,
        .page-header .flex.items-center.space-x-4 a {
            width: 100% !important;
            max-width: 280px !important;
            margin: 0 auto !important;
            min-height: 44px !important;
            padding: 12px 16px !important;
            font-size: 0.875rem !important;
            justify-content: center !important;
        }

        /* Filter tabs mobile optimization */
        .filter-tabs .flex {
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-tab {
            padding: 0.75rem 1.5rem !important;
            font-size: 0.875rem !important;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.5rem;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .filter-tab.active {
            background-color: #3b82f6;
            color: white !important;
            border-color: #3b82f6;
        }

        .filter-tab:hover {
            background-color: #e2e8f0;
        }

        .filter-tab.active:hover {
            background-color: #2563eb;
        }

        /* Filter tabs container mobile spacing */
        .filter-tabs {
            margin-bottom: 1rem !important;
        }

        /* Enhanced mobile notification items */
        .notification-item {
            padding: 20px !important;
            margin-bottom: 16px !important;
            border-radius: 16px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04) !important;
            border: 1px solid rgba(226, 232, 240, 0.6) !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
        }

        .notification-item.unread {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
            border-color: rgba(59, 130, 246, 0.3) !important;
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.12), 0 3px 8px rgba(59, 130, 246, 0.06) !important;
        }

        .notification-item .flex.items-start.space-x-4 {
            flex-direction: row !important;
            gap: 16px !important;
            align-items: flex-start !important;
        }

        /* Enhanced mobile notification icon */
        .notification-icon {
            width: 44px !important;
            height: 44px !important;
            font-size: 18px !important;
            flex-shrink: 0 !important;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12), 0 1px 4px rgba(0, 0, 0, 0.08) !important;
            border: 2px solid rgba(255, 255, 255, 0.9) !important;
        }

        /* Mobile notification content layout */
        .notification-content {
            flex: 1 !important;
            min-width: 0 !important;
        }

        .notification-title {
            font-size: 1.1rem !important;
            line-height: 1.4 !important;
            margin-bottom: 8px !important;
            font-weight: 700 !important;
            color: #1f2937 !important;
        }

        .notification-message {
            font-size: 0.9rem !important;
            line-height: 1.5 !important;
            margin-bottom: 12px !important;
            color: #4b5563 !important;
        }

        .notification-time {
            font-size: 0.8rem !important;
            padding: 6px 10px !important;
            border-radius: 8px !important;
            background: rgba(107, 114, 128, 0.1) !important;
            color: #6b7280 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 6px !important;
        }

        /* Enhanced mobile notification actions */
        .notification-actions {
            opacity: 1 !important;
            transform: translateY(0) !important;
            display: flex !important;
            justify-content: flex-end !important;
            gap: 8px !important;
            margin-top: 12px !important;
            padding-top: 12px !important;
            border-top: 1px solid rgba(226, 232, 240, 0.6) !important;
        }

        .notification-actions button {
            min-height: 40px !important;
            min-width: 40px !important;
            padding: 8px !important;
            border-radius: 10px !important;
            background: rgba(59, 130, 246, 0.1) !important;
            border: 1px solid rgba(59, 130, 246, 0.2) !important;
            color: #3b82f6 !important;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            backdrop-filter: blur(10px) !important;
        }

        .notification-actions button:hover {
            background: rgba(59, 130, 246, 0.15) !important;
            border-color: rgba(59, 130, 246, 0.3) !important;
            transform: translateY(-1px) !important;
        }

        .notification-actions button:active {
            transform: scale(0.95) !important;
        }

        .notification-actions button i {
            font-size: 14px !important;
        }

        /* Special styling for different action types */
        .notification-actions button[title*="مقروء"] {
            background: rgba(16, 185, 129, 0.1) !important;
            border-color: rgba(16, 185, 129, 0.2) !important;
            color: #10b981 !important;
        }

        .notification-actions button[title*="التفاصيل"] {
            background: rgba(139, 92, 246, 0.1) !important;
            border-color: rgba(139, 92, 246, 0.2) !important;
            color: #8b5cf6 !important;
        }

        /* Enhanced touch feedback and animations */
        .notification-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center;
        }

        .notification-item:active {
            transform: scale(0.98) translateY(1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
        }

        /* Improved visual hierarchy */
        .notification-item.unread .notification-title {
            color: #1e40af !important;
            font-weight: 700 !important;
        }

        .notification-item.unread .notification-message {
            color: #1f2937 !important;
        }

        /* Enhanced priority styling */
        .notification-priority-high .notification-title {
            font-weight: 700 !important;
        }

        .notification-priority-urgent .notification-title {
            font-weight: 700 !important;
        }

        .notification-priority-urgent .notification-unread-indicator {
            background: #dc2626 !important;
            box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2) !important;
        }

        /* Remove tap highlight */
        * {
            -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
        }

        /* Smooth scrolling for notification list */
        .space-y-4 {
            scroll-behavior: smooth;
        }

        /* Pagination mobile optimization */
        .mt-8.flex.justify-center nav {
            gap: 0.5rem;
        }

        .mt-8.flex.justify-center nav a,
        .mt-8.flex.justify-center nav span {
            min-height: 44px !important;
            padding: 0.75rem 1rem !important;
            font-size: 0.875rem !important;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Empty state mobile optimization */
        .text-center.py-12 {
            padding: 2rem 1rem !important;
        }

        .text-center.py-12 .mx-auto.h-24.w-24 {
            height: 4rem !important;
            width: 4rem !important;
            margin-bottom: 1rem !important;
        }

        .text-center.py-12 .mx-auto.h-24.w-24 i {
            font-size: 2.5rem !important;
        }

        .text-center.py-12 h3 {
            font-size: 1.125rem !important;
            margin-bottom: 0.5rem !important;
        }

        .text-center.py-12 p {
            font-size: 0.875rem !important;
        }

        /* Notification badge mobile styling */
        .notification-badge {
            font-size: 0.75rem !important;
            padding: 0.25rem 0.5rem !important;
            margin-right: 0.5rem !important;
        }

        /* Remove tap highlight */
        * {
            -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
        }



    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="page-container">
        <!-- Page Header - Following admin pages pattern -->
        <header class="page-header bg-white shadow-sm border border-gray-200 rounded-lg">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900" style="font-family: 'IBM Plex Sans Arabic', sans-serif;">
                        الإشعارات
                    </h1>
                    {% if unread_count > 0 %}
                    <span class="notification-badge">{{ unread_count }}</span>
                    {% endif %}
                </div>

                <!-- Desktop actions -->
                <div class="hidden md:flex items-center space-x-4 space-x-reverse">
                    <button
                        onclick="markAllAsRead()"
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                        style="font-family: 'IBM Plex Sans Arabic', sans-serif;"
                    >
                        تحديد الكل كمقروء
                    </button>

                    <a
                        href="/notifications/preferences"
                        class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                        style="font-family: 'IBM Plex Sans Arabic', sans-serif;"
                    >
                        إعدادات الإشعارات
                    </a>
                </div>

                <!-- Mobile actions -->
                <div class="md:hidden flex items-center space-x-4 space-x-reverse">
                    <button
                        onclick="markAllAsRead()"
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                        style="font-family: 'IBM Plex Sans Arabic', sans-serif;"
                    >
                        تحديد الكل كمقروء
                    </button>

                    <a
                        href="/notifications/preferences"
                        class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                        style="font-family: 'IBM Plex Sans Arabic', sans-serif;"
                    >
                        إعدادات الإشعارات
                    </a>
                </div>
            </div>
        </header>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="flex space-x-8 space-x-reverse">
                <a
                    href="/notifications?unread_only=false"
                    class="filter-tab {% if not unread_only %}active{% endif %}"
                    style="font-family: 'IBM Plex Sans Arabic', sans-serif;"
                >
                    جميع الإشعارات
                </a>
                <a
                    href="/notifications?unread_only=true"
                    class="filter-tab {% if unread_only %}active{% endif %}"
                    style="font-family: 'IBM Plex Sans Arabic', sans-serif;"
                >
                    غير المقروءة
                    {% if unread_count > 0 %}
                    <span class="notification-badge">{{ unread_count }}</span>
                    {% endif %}
                </a>
            </div>
        </div>

        <!-- Notifications List -->
        {% if notifications %}
        <div class="space-y-4">
            {% for notification in notifications %}
            <div
                class="notification-item {% if not notification.is_read %}unread{% endif %} {% if notification.priority.value == 'high' %}notification-priority-high{% elif notification.priority.value == 'urgent' %}notification-priority-urgent{% endif %} bg-white rounded-lg shadow-sm p-6 cursor-pointer"
                onclick="handleNotificationClick({{ notification.id }}, '{{ notification.action_url or '' }}')"
            >
                <!-- Unread indicator -->
                {% if not notification.is_read %}
                <div class="notification-unread-indicator"></div>
                {% endif %}

                <div class="flex items-start space-x-4 space-x-reverse">
                    <!-- Notification Icon -->
                    <div class="notification-icon notification-type-{{ notification.type.value }}">
                        {% if notification.type.value == 'request_status_changed' %}
                            <i class="fas fa-sync-alt"></i>
                        {% elif notification.type.value == 'request_created' %}
                            <i class="fas fa-plus-circle"></i>
                        {% elif notification.type.value == 'request_updated' %}
                            <i class="fas fa-edit"></i>
                        {% elif notification.type.value == 'admin_message' %}
                            <i class="fas fa-user-shield"></i>
                        {% elif notification.type.value == 'system_announcement' %}
                            <i class="fas fa-bullhorn"></i>
                        {% else %}
                            <i class="fas fa-bell"></i>
                        {% endif %}
                    </div>

                    <!-- Notification Content -->
                    <div class="notification-content">
                        <div class="notification-title" style="font-family: 'IBM Plex Sans Arabic', sans-serif;">
                            {{ notification.title }}
                        </div>
                        <div class="notification-message" style="font-family: 'IBM Plex Sans Arabic', sans-serif;">
                            {{ notification.message }}
                        </div>
                        <div class="notification-time">
                            <i class="fas fa-clock"></i>
                            <span>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else '' }}</span>
                        </div>

                        <!-- Notification Actions -->
                        <div class="notification-actions">
                            {% if not notification.is_read %}
                            <button
                                onclick="markAsRead({{ notification.id }}, event)"
                                title="تحديد كمقروء"
                            >
                                <i class="fas fa-check"></i>
                            </button>
                            {% endif %}

                            <button
                                onclick="event.stopPropagation(); window.location.href='/notifications/{{ notification.id }}'"
                                title="عرض التفاصيل"
                            >
                                <i class="fas fa-eye"></i>
                            </button>

                            {% if notification.action_url %}
                            <button
                                onclick="event.stopPropagation(); window.location.href='{{ notification.action_url }}'"
                                title="عرض المحتوى المرتبط"
                            >
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if total_pages > 1 %}
        <div class="mt-8 flex justify-center">
            <nav class="flex items-center space-x-2 space-x-reverse">
                {% if page > 1 %}
                <a 
                    href="/notifications?page={{ page - 1 }}&unread_only={{ unread_only }}" 
                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                    السابق
                </a>
                {% endif %}
                
                <span class="px-3 py-2 text-sm font-medium text-gray-700">
                    صفحة {{ page }} من {{ total_pages }}
                </span>
                
                {% if page < total_pages %}
                <a 
                    href="/notifications?page={{ page + 1 }}&unread_only={{ unread_only }}" 
                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                    التالي
                </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
                <i class="fas fa-bell text-6xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2" style="font-family: 'IBM Plex Sans Arabic', sans-serif;">
                {% if unread_only %}
                لا توجد إشعارات غير مقروءة
                {% else %}
                لا توجد إشعارات
                {% endif %}
            </h3>
            <p class="text-gray-500" style="font-family: 'IBM Plex Sans Arabic', sans-serif;">
                {% if unread_only %}
                جميع إشعاراتك مقروءة!
                {% else %}
                ستظهر إشعاراتك هنا عند وصولها
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript -->
<script>
    function handleNotificationClick(notificationId, actionUrl) {
        // Mark as read first
        markAsRead(notificationId);

        // Navigate to action URL if available, otherwise show notification detail
        if (actionUrl && actionUrl.trim() !== '') {
            setTimeout(() => {
                window.location.href = actionUrl;
            }, 200);
        } else {
            // Navigate to notification detail page
            setTimeout(() => {
                window.location.href = `/notifications/${notificationId}`;
            }, 200);
        }
    }
    
    function markAsRead(notificationId, event = null) {
        if (event) {
            event.stopPropagation();
        }
        
        fetch(`/api/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update UI
                const notificationElement = document.querySelector(`[onclick*="${notificationId}"]`);
                if (notificationElement) {
                    notificationElement.classList.remove('unread');
                    const actionsElement = notificationElement.querySelector('.notification-actions');
                    if (actionsElement) {
                        const readButton = actionsElement.querySelector('button[onclick*="markAsRead"]');
                        if (readButton) {
                            readButton.remove();
                        }
                    }
                }
                
                // Update badge count
                updateNotificationBadge();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
    }
    
    function markAllAsRead() {
        fetch('/api/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to update UI
                window.location.reload();
            }
        })
        .catch(error => {
            console.error('Error marking all notifications as read:', error);
        });
    }
    
    function updateNotificationBadge() {
        fetch('/api/notifications/unread-count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const badges = document.querySelectorAll('.notification-badge');
                badges.forEach(badge => {
                    if (data.unread_count > 0) {
                        badge.textContent = data.unread_count;
                        badge.style.display = 'inline-block';
                    } else {
                        badge.style.display = 'none';
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error updating notification badge:', error);
        });
    }
</script>
{% endblock %}
