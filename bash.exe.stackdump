Stack trace:
Frame         Function      Args
0007FFFFAF00  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFAF00, 0007FFFF9E00) msys-2.0.dll+0x1FEBA
0007FFFFAF00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB1D8) msys-2.0.dll+0x67F9
0007FFFFAF00  000210046832 (000210285FF9, 0007FFFFADB8, 0007FFFFAF00, 000000000000) msys-2.0.dll+0x6832
0007FFFFAF00  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAF00  0002100690B4 (0007FFFFAF10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFB1E0  00021006A49D (0007FFFFAF10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA33940000 ntdll.dll
7FFA32BC0000 KERNEL32.DLL
7FFA30DF0000 KERNELBASE.dll
7FFA32170000 USER32.dll
7FFA30BD0000 win32u.dll
7FFA32C90000 GDI32.dll
7FFA311E0000 gdi32full.dll
7FFA30B20000 msvcp_win.dll
7FFA30C00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA32CC0000 advapi32.dll
7FFA335F0000 msvcrt.dll
7FFA32DA0000 sechost.dll
7FFA32460000 RPCRT4.dll
7FFA2FFA0000 CRYPTBASE.DLL
7FFA30D50000 bcryptPrimitives.dll
7FFA33340000 IMM32.DLL
