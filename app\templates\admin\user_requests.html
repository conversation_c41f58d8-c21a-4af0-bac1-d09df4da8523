{% extends "base.html" %}

{% block title %}طلبات {{ target_user.full_name or target_user.username }} - لوحة التحكم{% endblock %}

{% block head %}
<style>
/* Professional Admin User Requests Styling */
body {
    background-color: #f8fafc;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-outline {
    background-color: transparent;
    color: #374151;
    border-color: #d1d5db;
}

.btn-outline:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
}

.section {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: 1px solid #e5e7eb;
}

.stats-grid {
    display: grid;
    gap: 1.5rem;
}

.stat-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.stat-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.role-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.role-badge.admin {
    background-color: #fef3c7;
    color: #92400e;
}

.role-badge.manager {
    background-color: #dbeafe;
    color: #1e40af;
}

.role-badge.user {
    background-color: #f3f4f6;
    color: #374151;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-sm i {
    font-size: 11px;
}

/* Enhanced table container */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e5e7eb;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    max-width: 100%;
    width: 100%;
}

/* Professional table styling */
.requests-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    min-width: 1200px;
}

/* Enhanced table header */
.table-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.table-header-cell {
    padding: 16px 12px;
    text-align: right;
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border-bottom: 1px solid #e5e7eb;
}

.table-header-cell.text-center {
    text-align: center;
}

/* Enhanced table body */
.table-body {
    background: white;
}

.table-row {
    transition: all 0.15s ease;
    border-bottom: 1px solid #f1f5f9;
}

.table-row:hover {
    background-color: #f8fafc;
}

.table-row:last-child {
    border-bottom: none;
}

.table-cell {
    padding: 16px 12px;
    font-size: 14px;
    color: #1f2937;
    vertical-align: middle;
    border-bottom: 1px solid #f1f5f9;
}

.table-cell:last-child {
    border-bottom: none;
}

/* Responsive table adjustments */
@media (min-width: 768px) {
    .table-header-cell {
        padding: 18px 16px;
        font-size: 13px;
    }

    .table-cell {
        padding: 18px 16px;
        font-size: 14px;
    }
}

/* Simplified status badges */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.status-badge.pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-badge.in_progress {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-badge.completed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-badge.rejected {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Professional cell content styling */
.cell-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cell-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.cell-text {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.cell-primary {
    font-weight: 700;
    font-size: 15px;
    color: #1f2937;
    line-height: 1.2;
}

.cell-secondary {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
    line-height: 1.2;
}

/* Action button enhancements */
.action-btn-group {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    min-width: 80px;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
    transform: translateY(0);
}

/* Loading state for buttons */
.action-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.action-btn.loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

        /* Enhanced profile avatar */
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            background-color: #f3f4f6;
            border: 4px solid #ffffff;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            flex-shrink: 0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
        }



        /* Role badges */
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 14px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .role-badge.admin {
            background-color: #fef3c7;
            color: #92400e;
            border: 2px solid #fbbf24;
        }

        .role-badge.manager {
            background-color: #dbeafe;
            color: #1e40af;
            border: 2px solid #3b82f6;
        }

        .role-badge.user {
            background-color: #f3e8ff;
            color: #7c3aed;
            border: 2px solid #8b5cf6;
        }

        /* Enhanced buttons */
        .primary-btn {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
        }

        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
        }

        .secondary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
        }

        /* Enhanced form styling */
        .form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .form-section {
            padding: 2rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        /* Header styling */
        .page-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            border-radius: 20px;
            margin-bottom: 2rem;
            padding: 2.5rem;
            box-shadow: 0 15px 35px rgba(6, 182, 212, 0.3);
        }

        /* Stats cards */
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        /* Table styling */
        .requests-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
        }

        .requests-table th {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 1rem;
            font-weight: 700;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }

        .requests-table td {
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .requests-table tr:hover {
            background-color: #f8fafc;
        }

        /* Progress bars */
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Success/Error messages */
        .alert {
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-success {
            background-color: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background-color: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert-info {
            background-color: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        /* Loading animation */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Enhanced tooltips */
        [title] {
            position: relative;
            cursor: help;
        }

        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: pre-line;
            z-index: 1000;
            max-width: 300px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        [title]:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(100%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1001;
        }

        /* Enhanced file preview styles */
        .file-item {
            transition: all 0.3s ease;
        }

        .file-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Modal animations */
        #filesModal, #pdfViewerModal {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Mobile-first responsive design */
        @media (max-width: 768px) {
            .form-section {
                padding: 1rem;
            }

            .page-header {
                padding: 1.5rem;
            }

            .stat-card {
                padding: 1rem;
            }

            #filesModal .bg-white, #pdfViewerModal .bg-white {
                margin: 1rem;
                max-height: calc(100vh - 2rem);
            }

            /* Hide desktop table on mobile */
            .desktop-table {
                display: none;
            }

            /* Show mobile cards */
            .mobile-cards {
                display: block;
            }

            /* Mobile header adjustments */
            .page-header .flex {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }

            /* Mobile filter form */
            .filter-form .grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            /* Mobile statistics grid */
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            /* Mobile analytics grid */
            .analytics-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            /* Mobile container improvements */
            .container {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            /* Section mobile adjustments */
            .section {
                margin-left: 0.5rem;
                margin-right: 0.5rem;
                margin-bottom: 1rem;
            }

            .section .p-6 {
                padding: 1rem !important;
            }

            /* Search and filter mobile improvements */
            .grid.grid-cols-1.md\\:grid-cols-3 {
                grid-template-columns: 1fr !important;
                gap: 1rem;
            }

            /* Mobile button stack improvements */
            .flex.flex-col.sm\\:flex-row {
                flex-direction: column;
                gap: 0.75rem;
            }

            .flex.flex-col.sm\\:flex-row .flex.flex-col.sm\\:flex-row {
                flex-direction: column;
                gap: 0.5rem;
            }

            /* Prevent horizontal overflow */
            body {
                overflow-x: hidden;
            }

            /* Ensure all containers fit within viewport */
            .table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                margin-left: -0.5rem;
                margin-right: -0.5rem;
            }

            /* Mobile modal improvements */
            #filesModal .bg-white, #pdfViewerModal .bg-white {
                margin: 0.5rem !important;
                max-height: calc(100vh - 1rem) !important;
                width: calc(100vw - 1rem) !important;
                max-width: none !important;
            }

            /* Mobile text adjustments */
            .text-lg {
                font-size: 1rem;
            }

            /* Mobile spacing improvements */
            .mb-6 {
                margin-bottom: 1rem;
            }

            .mb-8 {
                margin-bottom: 1.5rem;
            }
        }

        /* Desktop styles */
        @media (min-width: 769px) {
            /* Show desktop table */
            .desktop-table {
                display: block;
            }

            /* Hide mobile cards */
            .mobile-cards {
                display: none;
            }
        }

        /* Mobile request cards */
        .mobile-request-card {
            background: white;
            border-radius: 12px;
            padding: 1.25rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .mobile-request-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .mobile-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-card-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .mobile-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.25rem 0;
        }

        .mobile-field-label {
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
        }

        .mobile-field-value {
            color: #6b7280;
            font-size: 0.875rem;
            text-align: left;
        }

        .mobile-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
            padding-top: 0.75rem;
            border-top: 1px solid #f3f4f6;
        }

        .mobile-action-btn {
            flex: 1;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .mobile-action-view {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .mobile-action-view:hover {
            background: #bfdbfe;
        }

        .mobile-action-edit {
            background: #dcfce7;
            color: #166534;
        }

        .mobile-action-edit:hover {
            background: #bbf7d0;
        }

        .mobile-action-complete {
            background: #fef3c7;
            color: #92400e;
        }

        .mobile-action-complete:hover {
            background: #fde68a;
        }

        /* Touch-friendly improvements */
        @media (max-width: 768px) {
            /* Larger touch targets */
            .mobile-action-btn {
                min-height: 44px;
                padding: 0.75rem;
            }

            /* Better spacing for touch */
            .mobile-actions {
                gap: 0.75rem;
            }

            /* Improved form inputs */
            input, select, button {
                min-height: 44px;
            }

            /* Better modal sizing */
            #filesModal .bg-white {
                width: calc(100vw - 2rem);
                max-width: none;
            }

            /* Mobile status badges */
            .status-badge {
                font-size: 11px;
                padding: 3px 6px;
            }

            /* Better card spacing */
            .mobile-request-card {
                margin-bottom: 1.5rem;
            }

            /* Improved header spacing */
            .page-header {
                margin-bottom: 1.5rem;
            }

            /* Better filter form spacing */
            .filter-form {
                padding: 1rem;
            }

            /* Improved export menu positioning */
            #exportMenu {
                left: auto;
                right: 0;
                width: 100%;
                max-width: 200px;
            }

            /* Additional mobile improvements */
            input[type="text"], select {
                font-size: 16px; /* Prevents zoom on iOS */
                -webkit-appearance: none;
            }

            /* Search and filter mobile specific */
            .grid.grid-cols-1.md\\:grid-cols-3 .md\\:col-span-2 {
                grid-column: span 1;
            }

            /* Button improvements for mobile */
            button[type="submit"], .px-4.py-2 {
                width: 100%;
                min-height: 44px;
                padding: 0.75rem 1rem;
                font-size: 16px;
                touch-action: manipulation;
            }

            /* Prevent text overflow in mobile fields */
            .mobile-field-value {
                overflow-wrap: break-word;
                word-break: break-word;
                max-width: 150px;
            }

            /* Better card margins */
            .mobile-request-card {
                margin-left: 0.5rem;
                margin-right: 0.5rem;
            }

            /* Improved modal sizing */
            #filesModal .bg-white, #pdfViewerModal .bg-white {
                width: calc(100vw - 1rem) !important;
                margin: 0.5rem !important;
                max-height: calc(100vh - 1rem) !important;
            }
        }

        /* Tablet improvements */
        @media (min-width: 769px) and (max-width: 1024px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .analytics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-white border-b border-gray-200 mb-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
            <div class="flex-1">
                <div class="flex items-center space-x-3 space-x-reverse mb-2">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-user-circle text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">طلبات {{ target_user.full_name or target_user.username }}</h1>
                        <p class="text-sm text-gray-600">إدارة ومراقبة طلبات المستخدم</p>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                    <a href="/admin/dashboard" class="hover:text-blue-600 transition-colors">لوحة التحكم</a>
                    <i class="fas fa-chevron-left text-xs"></i>
                    <a href="/admin/users" class="hover:text-blue-600 transition-colors">إدارة المستخدمين</a>
                    <i class="fas fa-chevron-left text-xs"></i>
                    <span class="text-gray-900 font-medium">طلبات المستخدم</span>
                </nav>
            </div>
            <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                <a href="/admin/users" class="btn btn-secondary">
                    <i class="fas fa-arrow-right mr-2"></i>
                    العودة للمستخدمين
                </a>
                <a href="/admin/users/{{ target_user.id }}/edit" class="btn btn-outline">
                    <i class="fas fa-user-edit ml-2"></i>
                    تعديل المستخدم
                </a>
            </div>
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
    <!-- User Profile Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="p-6">
            <div class="flex items-center space-x-6 space-x-reverse">
                <div class="profile-avatar" style="background-image: url('{{ target_user_avatar_url }}');"></div>
                <div class="flex-1">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ target_user.full_name or target_user.username }}</h2>
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-envelope text-gray-400"></i>
                            <span>{{ target_user.email }}</span>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-user-tag text-gray-400"></i>
                            <span class="role-badge {{ target_user.role.value }}">
                                {% if target_user.role.value == 'admin' %}
                                    <i class="fas fa-crown mr-1"></i>
                                    مدير النظام
                                {% elif target_user.role.value == 'manager' %}
                                    <i class="fas fa-user-tie mr-1"></i>
                                    مدير المشاريع
                                {% else %}
                                    <i class="fas fa-user mr-1"></i>
                                    عضو الفريق
                                {% endif %}
                            </span>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-calendar text-gray-400"></i>
                            <span>انضم في {{ target_user.created_at.strftime('%Y-%m-%d') if target_user.created_at else 'غير محدد' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="section mb-6">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">إحصائيات الطلبات</h3>
        <div class="stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                        <p class="text-3xl font-bold text-blue-600">{{ request_stats.total_requests }}</p>
                        <p class="text-xs text-gray-500 mt-1">جميع الطلبات المسجلة</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-file-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Completed Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">الطلبات المكتملة</p>
                        <p class="text-3xl font-bold text-green-600">{{ request_stats.status_distribution.completed or 0 }}</p>
                        <p class="text-xs text-gray-500 mt-1">تم إنجازها بنجاح</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Pending Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">قيد المعالجة</p>
                        <p class="text-3xl font-bold text-yellow-600">{{ (request_stats.status_distribution.pending or 0) + (request_stats.status_distribution.in_progress or 0) }}</p>
                        <p class="text-xs text-gray-500 mt-1">تحتاج متابعة</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Completion Rate -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">معدل الإنجاز</p>
                        <p class="text-3xl font-bold text-purple-600">{{ request_stats.overall_completion_rate }}%</p>
                        <p class="text-xs text-gray-500 mt-1">الأداء العام</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-chart-pie text-white text-xl"></i>
                    </div>
                </div>
                <!-- Progress Bar -->
                <div class="mt-4">
                    <div class="progress-bar">
                        <div class="progress-fill bg-gradient-to-r from-purple-500 to-purple-600" style="width: {{ request_stats.overall_completion_rate }}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Time-based Analytics -->
        <div class="analytics-grid grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Daily Performance -->
            <div class="form-container">
                <div class="form-section">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-day text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">الأداء اليومي</h3>
                                <p class="text-sm text-gray-600">آخر 24 ساعة</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">إجمالي الطلبات</span>
                            <span class="text-lg font-bold text-gray-900">{{ request_stats.daily_stats.requests }}</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">المكتملة</span>
                            <span class="text-lg font-bold text-green-600">{{ request_stats.daily_stats.completed }}</span>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
                            <p class="text-sm text-gray-600 mb-2">معدل الإنجاز اليومي</p>
                            <p class="text-3xl font-bold text-orange-600">{{ request_stats.daily_stats.completion_rate }}%</p>
                            <div class="mt-3">
                                <div class="progress-bar">
                                    <div class="progress-fill bg-gradient-to-r from-orange-500 to-red-600" style="width: {{ request_stats.daily_stats.completion_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Weekly Performance -->
            <div class="form-container">
                <div class="form-section">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-business-time text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">الأداء الأسبوعي</h3>
                                <p class="text-sm text-gray-600">آخر 5 أيام عمل</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">إجمالي الطلبات</span>
                            <span class="text-lg font-bold text-gray-900">{{ request_stats.weekly_stats.requests }}</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">المكتملة</span>
                            <span class="text-lg font-bold text-green-600">{{ request_stats.weekly_stats.completed }}</span>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-200">
                            <p class="text-sm text-gray-600 mb-2">معدل الإنجاز الأسبوعي</p>
                            <p class="text-3xl font-bold text-indigo-600">{{ request_stats.weekly_stats.completion_rate }}%</p>
                            <div class="mt-3">
                                <div class="progress-bar">
                                    <div class="progress-fill bg-gradient-to-r from-indigo-500 to-purple-600" style="width: {{ request_stats.weekly_stats.completion_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Performance -->
            <div class="form-container">
                <div class="form-section">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">الأداء الشهري</h3>
                                <p class="text-sm text-gray-600">آخر 30 يوم</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">إجمالي الطلبات</span>
                            <span class="text-lg font-bold text-gray-900">{{ request_stats.monthly_stats.requests }}</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">المكتملة</span>
                            <span class="text-lg font-bold text-green-600">{{ request_stats.monthly_stats.completed }}</span>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-lg border border-teal-200">
                            <p class="text-sm text-gray-600 mb-2">معدل الإنجاز الشهري</p>
                            <p class="text-3xl font-bold text-teal-600">{{ request_stats.monthly_stats.completion_rate }}%</p>
                            <div class="mt-3">
                                <div class="progress-bar">
                                    <div class="progress-fill bg-gradient-to-r from-teal-500 to-cyan-600" style="width: {{ request_stats.monthly_stats.completion_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="section mb-6">
            <div class="p-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
                    <h3 class="text-lg font-medium text-gray-900">البحث والفلترة</h3>
                    <span class="text-sm text-gray-500">{{ user_requests|length }} نتيجة</span>
                </div>

                <form method="GET" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Search Input -->
                        <div class="md:col-span-2">
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
                                البحث في الطلبات
                            </label>
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="{{ current_search or '' }}"
                                   placeholder="ابحث برقم الطلب، الاسم، أو رقم الإجازة..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                                تصفية بالحالة
                            </label>
                            <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">جميع الحالات</option>
                                {% for status in statuses %}
                                <option value="{{ status }}" {% if current_status == status %}selected{% endif %}>
                                    {% if status == 'pending' %}قيد الانتظار
                                    {% elif status == 'in_progress' %}قيد المعالجة
                                    {% elif status == 'completed' %}مكتمل
                                    {% elif status == 'rejected' %}مرفوض
                                    {% else %}{{ status }}
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center pt-3 border-t border-gray-200 space-y-3 sm:space-y-0 gap-3">
                        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse w-full sm:w-auto">
                            <button type="submit" class="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                                بحث
                            </button>
                            <a href="/admin/users/{{ target_user.id }}/requests" class="w-full sm:w-auto px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-center text-sm font-medium">
                                مسح الفلاتر
                            </a>
                        </div>

                        <!-- Export Options -->
                        <div class="relative w-full sm:w-auto">
                            <button type="button" id="exportBtn" class="w-full sm:w-auto px-4 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm font-medium">
                                تصدير البيانات
                            </button>
                            <div id="exportMenu" class="hidden absolute left-0 sm:left-0 mt-1 w-full sm:w-40 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                                <a href="/admin/users/{{ target_user.id }}/requests/export?format=csv{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}"
                                   class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    تصدير CSV
                                </a>
                                <a href="/admin/users/{{ target_user.id }}/requests/export?format=json{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}"
                                   class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    تصدير JSON
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Search Results Info -->
                    {% if current_search or current_status %}
                    <div class="bg-gray-50 border border-gray-200 rounded-md p-3 mt-3">
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">نتائج البحث:</span>
                            {% if current_search %}
                            <span>البحث: "{{ current_search }}"</span>
                            {% endif %}
                            {% if current_status %}
                            <span>{% if current_search %} • {% endif %}الحالة:
                                {% if current_status == 'pending' %}قيد الانتظار
                                {% elif current_status == 'in_progress' %}قيد المعالجة
                                {% elif current_status == 'completed' %}مكتمل
                                {% elif current_status == 'rejected' %}مرفوض
                                {% endif %}
                            </span>
                            {% endif %}
                            <span> • {{ user_requests|length }} نتيجة</span>
                        </div>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- Enhanced Requests Table -->
        {% if user_requests %}
        <div class="section">
            <div class="p-6 border-b border-gray-100">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-list text-white text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-1">قائمة الطلبات</h2>
                            <p class="text-gray-600 text-sm">جميع طلبات {{ target_user.full_name or target_user.username }}</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between sm:justify-end gap-4">
                        <div class="flex items-center space-x-2 space-x-reverse bg-gray-50 px-4 py-2 rounded-lg">
                            <i class="fas fa-info-circle text-blue-500"></i>
                            <span class="text-sm font-medium text-gray-700">{{ user_requests|length }} طلب</span>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="exportRequests()" class="btn btn-outline btn-sm">
                                <i class="fas fa-download ml-1"></i>
                                تصدير
                            </button>
                            <button onclick="printRequests()" class="btn btn-outline btn-sm">
                                <i class="fas fa-print ml-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Desktop Table View -->
            <div class="desktop-table">
                <div class="table-container">
                    <table class="requests-table">
                        <thead class="table-header">
                            <tr>
                                <th class="table-header-cell" style="width: 140px;">رقم الطلب</th>
                                <th class="table-header-cell" style="width: 180px;">الاسم الكامل</th>
                                <th class="table-header-cell" style="width: 140px;">الرقم الشخصي</th>
                                <th class="table-header-cell" style="width: 160px;">المبنى</th>
                                <th class="table-header-cell text-center" style="width: 120px;">الحالة</th>
                                <th class="table-header-cell text-center" style="width: 140px;">المرفقات</th>
                                <th class="table-header-cell text-center" style="width: 140px;">تاريخ الإنشاء</th>
                                <th class="table-header-cell text-center" style="width: 180px;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                            {% for request in user_requests %}
                            <tr class="table-row" data-request-id="{{ request.id }}" data-status="{{ request.status.value }}">
                                <td class="table-cell">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-hashtag text-blue-500 text-sm"></i>
                                        <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono font-semibold"
                                              title="الرمز التعريفي: {{ request.unique_code }}">{{ request.request_number }}</code>
                                    </div>
                                </td>
                                <td class="table-cell">
                                    <div class="flex flex-col">
                                        <span class="font-semibold text-gray-900">{{ request.full_name or request.request_name or 'غير محدد' }}</span>
                                        {% if request.phone_number %}
                                        <span class="text-xs text-gray-500">{{ request.phone_number }}</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="table-cell">
                                    <span class="font-mono text-gray-700">{{ request.personal_number or 'غير محدد' }}</span>
                                </td>
                                <td class="table-cell">
                                    <div class="flex flex-col">
                                        <span class="font-medium text-gray-900"
                                              title="المبنى: {{ request.building_name or 'غير محدد' }}&#10;الطريق: {{ request.road_name or 'غير محدد' }}&#10;رقم المجمع: {{ request.building_number or 'غير محدد' }}&#10;رقم إجازة البناء: {{ request.building_permit_number or 'غير محدد' }}">
                                            {{ request.building_name or 'غير محدد' }}
                                        </span>
                                        {% if request.building_permit_number %}
                                        <span class="text-xs text-gray-500">{{ request.building_permit_number }}</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="table-cell text-center">
                                    <span class="status-badge {{ request.status.value }}">
                                        {% if request.status.value == 'pending' %}
                                            انتظار
                                        {% elif request.status.value == 'in_progress' %}
                                            معالجة
                                        {% elif request.status.value == 'completed' %}
                                            مكتمل
                                        {% elif request.status.value == 'rejected' %}
                                            مرفوض
                                        {% endif %}
                                    </span>
                                </td>
                                <td class="table-cell text-center">
                                    {% if request.files|length > 0 %}
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-semibold">
                                            {{ request.files|length }}
                                        </span>
                                        <button onclick="showRequestFiles({{ request.id }}, '{{ request.request_number }}')"
                                                class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs"
                                                title="عرض الملفات">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    {% else %}
                                    <span class="text-gray-400 text-sm">-</span>
                                    {% endif %}
                                </td>
                                <td class="table-cell text-center">
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900">{{ request.created_at.strftime('%Y-%m-%d') }}</div>
                                        <div class="text-xs text-gray-500">{{ request.created_at.strftime('%H:%M') }}</div>
                                    </div>
                                </td>
                                <td class="table-cell text-center">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <!-- View Button -->
                                        <a href="/admin/requests/{{ request.id }}/view"
                                           class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors text-sm"
                                           title="عرض تفاصيل الطلب">
                                            <i class="fas fa-eye text-xs"></i>
                                        </a>

                                        <!-- Edit Button -->
                                        <a href="/admin/requests/{{ request.id }}/edit"
                                           class="inline-flex items-center px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors text-sm"
                                           title="تعديل الطلب">
                                            <i class="fas fa-edit text-xs"></i>
                                        </a>

                                        <!-- Status Update Button -->
                                        {% if request.status.value in ['pending', 'in_progress'] %}
                                        <form method="post" action="/admin/requests/{{ request.id }}/update-status" class="inline">
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit"
                                                    class="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition-colors text-sm"
                                                    onclick="return confirm('هل أنت متأكد من تحديد هذا الطلب كمكتمل؟')"
                                                    title="تحديد الطلب كمكتمل">
                                                <i class="fas fa-check text-xs"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </td>
                        </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Mobile Cards View -->
            <div class="mobile-cards">
                {% for request in user_requests %}
                <div class="mobile-request-card">
                    <!-- Card Header -->
                    <div class="mobile-card-header">
                        <div>
                            <div class="flex items-center space-x-2 space-x-reverse mb-1">
                                <i class="fas fa-hashtag text-gray-400"></i>
                                <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">{{ request.request_number }}</code>
                            </div>
                            <div class="text-xs text-gray-500">{{ request.unique_code }}</div>
                        </div>
                        <span class="status-badge {{ request.status.value }}">
                            {% if request.status.value == 'pending' %}
                                <i class="fas fa-clock mr-1"></i>
                                قيد الانتظار
                            {% elif request.status.value == 'in_progress' %}
                                <i class="fas fa-cog mr-1"></i>
                                قيد المعالجة
                            {% elif request.status.value == 'completed' %}
                                <i class="fas fa-check mr-1"></i>
                                مكتمل
                            {% elif request.status.value == 'rejected' %}
                                <i class="fas fa-times mr-1"></i>
                                مرفوض
                            {% endif %}
                        </span>
                    </div>

                    <!-- Card Content -->
                    <div class="mobile-card-content">
                        <div class="mobile-field">
                            <span class="mobile-field-label">الاسم الكامل:</span>
                            <span class="mobile-field-value">{{ request.full_name or request.request_name or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-field">
                            <span class="mobile-field-label">الرقم الشخصي:</span>
                            <span class="mobile-field-value">{{ request.personal_number or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-field">
                            <span class="mobile-field-label">المبنى:</span>
                            <span class="mobile-field-value">{{ request.building_name or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-field">
                            <span class="mobile-field-label">المرفقات:</span>
                            <span class="mobile-field-value">
                                {% if request.files|length > 0 %}
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-semibold">
                                            {{ request.files|length }}
                                        </div>
                                        <button onclick="showRequestFiles({{ request.id }}, '{{ request.request_number }}')"
                                                class="text-blue-600 text-xs underline">
                                            عرض الملفات
                                        </button>
                                    </div>
                                {% else %}
                                    <span class="text-gray-400 text-xs">لا توجد ملفات</span>
                                {% endif %}
                            </span>
                        </div>

                        <div class="mobile-field">
                            <span class="mobile-field-label">تاريخ الإنشاء:</span>
                            <span class="mobile-field-value">
                                <div class="text-sm">
                                    <div>{{ request.created_at.strftime('%Y-%m-%d') }}</div>
                                    <div class="text-xs text-gray-400">{{ request.created_at.strftime('%H:%M') }}</div>
                                </div>
                            </span>
                        </div>
                    </div>

                    <!-- Card Actions -->
                    <div class="mobile-actions">
                        <a href="/admin/requests/{{ request.id }}/view" class="mobile-action-btn mobile-action-view">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>
                        <a href="/admin/requests/{{ request.id }}/edit" class="mobile-action-btn mobile-action-edit">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                        {% if request.status.value != 'completed' %}
                        <form method="POST" action="/admin/requests/{{ request.id }}/complete" style="flex: 1;">
                            <button type="submit" class="mobile-action-btn mobile-action-complete w-full"
                                    onclick="return confirm('هل أنت متأكد من تحديد هذا الطلب كمكتمل؟')">
                                <i class="fas fa-check"></i>
                                إكمال
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        {% else %}
        <!-- Empty State -->
        <div class="form-container">
            <div class="form-section text-center py-8 sm:py-12">
                <div class="w-20 h-20 sm:w-24 sm:h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                    <i class="fas fa-file-alt text-3xl sm:text-4xl text-gray-400"></i>
                </div>

                <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">
                    {% if current_search or current_status %}
                        لا توجد طلبات تطابق البحث
                    {% else %}
                        لا توجد طلبات لهذا المستخدم
                    {% endif %}
                </h3>

                <p class="text-gray-600 mb-6 sm:mb-8 max-w-md mx-auto text-sm sm:text-base">
                    {% if current_search or current_status %}
                        لا توجد طلبات تطابق معايير البحث المحددة. جرب تعديل الفلاتر أو البحث بكلمات مختلفة.
                    {% else %}
                        لم يقم {{ target_user.full_name or target_user.username }} بإنشاء أي طلبات حتى الآن.
                    {% endif %}
                </p>

                <div class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
                    {% if current_search or current_status %}
                    <a href="/admin/users/{{ target_user.id }}/requests" class="secondary-btn w-full sm:w-auto">
                        <i class="fas fa-list"></i>
                        عرض جميع الطلبات
                    </a>
                    {% endif %}

                    <a href="/admin/users/{{ target_user.id }}/edit" class="primary-btn w-full sm:w-auto">
                        <i class="fas fa-user-edit"></i>
                        تعديل المستخدم
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Files Modal -->
    <div id="filesModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-2xl font-bold">ملفات الطلب</h3>
                            <p id="modalRequestNumber" class="text-blue-100 mt-1"></p>
                        </div>
                        <button onclick="closeFilesModal()" class="text-white hover:text-gray-200 transition-colors">
                            <i class="fas fa-times text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Modal Body -->
                <div class="p-6 max-h-[70vh] overflow-y-auto">
                    <div id="filesContainer" class="space-y-4">
                        <!-- Files will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF Viewer Modal -->
    <div id="pdfViewerModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-60">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[95vh] overflow-hidden">
                <!-- PDF Viewer Header -->
                <div class="bg-gradient-to-r from-red-600 to-pink-600 text-white p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <i class="fas fa-file-pdf text-2xl"></i>
                            <div>
                                <h3 id="pdfFileName" class="text-xl font-bold"></h3>
                                <p class="text-red-100 text-sm">استعراض ملف PDF</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button id="downloadPdfBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-2 rounded-lg transition-colors">
                                <i class="fas fa-download mr-1"></i>
                                تحميل
                            </button>
                            <button onclick="closePdfViewer()" class="text-white hover:text-gray-200 transition-colors">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- PDF Viewer Body -->
                <div class="bg-gray-100 h-[80vh]">
                    <iframe id="pdfFrame" class="w-full h-full border-0" src=""></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Export menu toggle
            const exportBtn = document.getElementById('exportBtn');
            const exportMenu = document.getElementById('exportMenu');

            if (exportBtn && exportMenu) {
                exportBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    exportMenu.classList.toggle('hidden');
                });

                // Close export menu when clicking outside
                document.addEventListener('click', function() {
                    exportMenu.classList.add('hidden');
                });

                exportMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showAlert(`تم نسخ الرمز: ${text}`, 'success');
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert(`تم نسخ الرمز: ${text}`, 'success');
            });
        }

        // Alert function
        function showAlert(message, type = 'info') {
            // Remove existing alerts
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;

            const icon = type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle';

            alertDiv.innerHTML = `
                <i class="fas ${icon}"></i>
                <span>${message}</span>
            `;

            // Insert at the top of the page
            const container = document.querySelector('.max-w-7xl');
            if (container) {
                container.insertBefore(alertDiv, container.firstChild);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }

        // Show request files modal
        async function showRequestFiles(requestId, requestNumber) {
            document.getElementById('modalRequestNumber').textContent = `الطلب رقم: ${requestNumber}`;

            try {
                const response = await fetch(`/admin/api/requests/${requestId}/files`);
                const data = await response.json();

                const filesContainer = document.getElementById('filesContainer');

                if (data.files && data.files.length > 0) {
                    filesContainer.innerHTML = data.files.map(file => `
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-12 h-12 bg-gradient-to-br ${getFileIconColor(file.file_type)} rounded-lg flex items-center justify-center">
                                        <i class="fas ${getFileIcon(file.file_type)} text-white text-lg"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-lg font-semibold text-gray-900 truncate">${file.original_filename}</h4>
                                        <div class="flex items-center space-x-4 space-x-reverse text-sm text-gray-500 mt-1">
                                            <span><i class="fas fa-tag mr-1"></i>${getCategoryName(file.file_category)}</span>
                                            <span><i class="fas fa-hdd mr-1"></i>${formatFileSize(file.file_size)}</span>
                                            <span><i class="fas fa-calendar mr-1"></i>${formatDate(file.uploaded_at)}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    ${file.file_type === 'pdf' ? `
                                    <button onclick="viewPDF(${file.id}, '${file.original_filename}')"
                                            class="bg-red-100 text-red-700 hover:bg-red-200 px-3 py-2 rounded-lg transition-colors text-sm font-medium">
                                        <i class="fas fa-eye mr-1"></i>
                                        استعراض PDF
                                    </button>
                                    ` : ''}
                                    <a href="/files/view/${file.id}" target="_blank"
                                       class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-2 rounded-lg transition-colors text-sm font-medium">
                                        <i class="fas fa-external-link-alt mr-1"></i>
                                        فتح
                                    </a>
                                    <a href="/files/download/${file.id}"
                                       class="bg-green-100 text-green-700 hover:bg-green-200 px-3 py-2 rounded-lg transition-colors text-sm font-medium">
                                        <i class="fas fa-download mr-1"></i>
                                        تحميل
                                    </a>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    filesContainer.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-folder-open text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500">لا توجد ملفات مرفقة بهذا الطلب</p>
                        </div>
                    `;
                }

                document.getElementById('filesModal').classList.remove('hidden');
            } catch (error) {
                console.error('Error loading files:', error);
                showAlert('حدث خطأ في تحميل الملفات', 'error');
            }
        }

        // Close files modal
        function closeFilesModal() {
            document.getElementById('filesModal').classList.add('hidden');
        }

        // View PDF in modal
        function viewPDF(fileId, fileName) {
            document.getElementById('pdfFileName').textContent = fileName;
            document.getElementById('pdfFrame').src = `/files/view/${fileId}`;
            document.getElementById('downloadPdfBtn').onclick = () => window.open(`/files/download/${fileId}`, '_blank');
            document.getElementById('pdfViewerModal').classList.remove('hidden');
        }

        // Close PDF viewer
        function closePdfViewer() {
            document.getElementById('pdfViewerModal').classList.add('hidden');
            document.getElementById('pdfFrame').src = '';
        }

        // Helper functions
        function getFileIcon(fileType) {
            const icons = {
                'pdf': 'fa-file-pdf',
                'doc': 'fa-file-word',
                'docx': 'fa-file-word',
                'txt': 'fa-file-alt',
                'jpg': 'fa-file-image',
                'jpeg': 'fa-file-image',
                'png': 'fa-file-image',
                'gif': 'fa-file-image'
            };
            return icons[fileType.toLowerCase()] || 'fa-file';
        }

        function getFileIconColor(fileType) {
            const colors = {
                'pdf': 'from-red-500 to-red-600',
                'doc': 'from-blue-500 to-blue-600',
                'docx': 'from-blue-500 to-blue-600',
                'txt': 'from-gray-500 to-gray-600',
                'jpg': 'from-green-500 to-green-600',
                'jpeg': 'from-green-500 to-green-600',
                'png': 'from-green-500 to-green-600',
                'gif': 'from-green-500 to-green-600'
            };
            return colors[fileType.toLowerCase()] || 'from-gray-500 to-gray-600';
        }

        function getCategoryName(category) {
            const categories = {
                'architectural_plans': 'مخططات معمارية',
                'electrical_mechanical_plans': 'مخططات كهربائية وميكانيكية',
                'inspection_department': 'قسم التفتيش',
                'fire_equipment_files': 'معدات الحريق',
                'commercial_records_files': 'السجلات التجارية',
                'engineering_offices_files': 'المكاتب الهندسية',
                'hazardous_materials_files': 'المواد الخطرة',
                'general': 'عام'
            };
            return categories[category] || category;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.id === 'filesModal') {
                closeFilesModal();
            }
            if (e.target.id === 'pdfViewerModal') {
                closePdfViewer();
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFilesModal();
                closePdfViewer();
            }
        });

        // Mobile-specific enhancements
        function initMobileEnhancements() {
            // Add touch feedback for mobile action buttons
            const mobileButtons = document.querySelectorAll('.mobile-action-btn');
            mobileButtons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });
                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Improve modal sizing on mobile
            const modals = document.querySelectorAll('#filesModal, #pdfViewerModal');
            modals.forEach(modal => {
                modal.addEventListener('touchstart', function(e) {
                    if (e.target === this) {
                        this.classList.add('hidden');
                    }
                });
            });

            // Add pull-to-refresh hint for mobile
            if (window.innerWidth <= 768) {
                const mobileCards = document.querySelector('.mobile-cards');
                if (mobileCards && mobileCards.children.length > 0) {
                    const refreshHint = document.createElement('div');
                    refreshHint.className = 'text-center text-gray-400 text-sm py-2 border-b border-gray-100';
                    refreshHint.innerHTML = '<i class="fas fa-arrow-down mr-1"></i> اسحب لأسفل للتحديث';
                    mobileCards.insertBefore(refreshHint, mobileCards.firstChild);
                }
            }

            // Optimize scroll performance on mobile
            let ticking = false;
            function updateScrollPosition() {
                // Add scroll-based optimizations here if needed
                ticking = false;
            }

            window.addEventListener('scroll', function() {
                if (!ticking) {
                    requestAnimationFrame(updateScrollPosition);
                    ticking = true;
                }
            });
        }

        // Export and Print Functions
        function exportRequests() {
            const table = document.querySelector('.requests-table');
            if (!table) return;

            // Create CSV content
            let csv = 'رقم الطلب,الاسم الكامل,الرقم الشخصي,المبنى,الحالة,تاريخ الإنشاء\n';

            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    cells[0]?.textContent?.trim() || '',
                    cells[1]?.textContent?.trim() || '',
                    cells[2]?.textContent?.trim() || '',
                    cells[3]?.textContent?.trim() || '',
                    cells[4]?.textContent?.trim() || '',
                    cells[6]?.textContent?.trim() || ''
                ];
                csv += rowData.map(cell => `"${cell}"`).join(',') + '\n';
            });

            // Download CSV
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `طلبات_{{ target_user.username }}_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        function printRequests() {
            const printContent = document.querySelector('.requests-table').outerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>طلبات {{ target_user.full_name or target_user.username }}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f5f5f5; }
                        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 11px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <h2>طلبات {{ target_user.full_name or target_user.username }}</h2>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                    ${printContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // Check for URL parameters and show messages
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('success')) {
                showAlert('تم تنفيذ العملية بنجاح', 'success');
            }
            if (urlParams.get('error')) {
                showAlert('حدث خطأ أثناء تنفيذ العملية', 'error');
            }
            if (urlParams.get('updated')) {
                showAlert('تم تحديث حالة الطلب بنجاح', 'success');
            }

            // Initialize mobile enhancements
            initMobileEnhancements();
        });
    </script>
</div>
{% endblock %}
