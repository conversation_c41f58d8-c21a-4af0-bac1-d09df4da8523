<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}إرشيف الدفاع المدني{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/static/favicon-32x32.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/static/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/static/android-chrome-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/static/android-chrome-512x512.png">
    <link rel="manifest" href="/static/site.webmanifest">

    <!-- Tailwind CSS -->
    <link href="/static/css/style.css?v={{ cache_bust }}" rel="stylesheet">

    <!-- Form Components CSS -->
    <link href="/static/css/form-components.css?v={{ cache_bust }}" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Global Event Listener Manager -->
    <script>
    // Global Event Listener Manager to prevent conflicts and memory leaks
    window.EventListenerManager = (function() {
        const listeners = new Map();
        let listenerIdCounter = 0;

        function addListener(element, event, handler, options = false) {
            const listenerId = ++listenerIdCounter;
            const listenerInfo = {
                element: element,
                event: event,
                handler: handler,
                options: options
            };

            listeners.set(listenerId, listenerInfo);
            element.addEventListener(event, handler, options);

            return listenerId;
        }

        function removeListener(listenerId) {
            const listenerInfo = listeners.get(listenerId);
            if (listenerInfo) {
                listenerInfo.element.removeEventListener(
                    listenerInfo.event,
                    listenerInfo.handler,
                    listenerInfo.options
                );
                listeners.delete(listenerId);
                return true;
            }
            return false;
        }

        function removeAllListeners() {
            listeners.forEach((listenerInfo, listenerId) => {
                listenerInfo.element.removeEventListener(
                    listenerInfo.event,
                    listenerInfo.handler,
                    listenerInfo.options
                );
            });
            listeners.clear();
        }

        function getActiveListenersCount() {
            return listeners.size;
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', removeAllListeners);

        return {
            add: addListener,
            remove: removeListener,
            removeAll: removeAllListeners,
            getCount: getActiveListenersCount
        };
    })();

    // Global dropdown state manager
    window.DropdownStateManager = (function() {
        const activeDropdowns = new Set();

        function addActiveDropdown(dropdownId) {
            activeDropdowns.add(dropdownId);
        }

        function removeActiveDropdown(dropdownId) {
            activeDropdowns.delete(dropdownId);
        }

        function closeAllDropdowns() {
            activeDropdowns.forEach(dropdownId => {
                const dropdown = document.getElementById(dropdownId);
                if (dropdown) {
                    dropdown.classList.add('hidden');

                    // Remove dropdown-open class from table if exists
                    const table = dropdown.closest('table');
                    if (table) {
                        table.classList.remove('dropdown-open');
                    }
                }
            });
            activeDropdowns.clear();
        }

        function getActiveDropdowns() {
            return Array.from(activeDropdowns);
        }

        return {
            add: addActiveDropdown,
            remove: removeActiveDropdown,
            closeAll: closeAllDropdowns,
            getActive: getActiveDropdowns
        };
    })();
    </script>

    <!-- Arabic Font Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- IBM Plex Sans Arabic Font for App Title -->
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&family=Mirza:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Enhanced Sidebar Styles -->
    <style>
        /* Mobile Notification Dropdown Responsive Design */
        @media (max-width: 640px) {
            #mobileNotificationDropdown {
                /* Responsive width with proper positioning */
                width: calc(100vw - 1rem) !important;
                max-width: 320px !important;
                /* Position from right edge */
                right: 0.5rem !important;
                left: auto !important;
                /* Prevent horizontal scrolling */
                overflow-x: hidden;
                /* Ensure proper z-index for mobile */
                z-index: 9999 !important;
            }

            #mobileDropdownNotificationsList {
                /* Ensure content doesn't overflow */
                overflow-x: hidden;
                word-wrap: break-word;
                /* Responsive height for mobile screens */
                max-height: 60vh;
                overflow-y: auto;
            }

            .mobile-dropdown-notification-item {
                /* Prevent text overflow */
                word-wrap: break-word;
                overflow-wrap: break-word;
                /* Responsive padding for mobile */
                padding: 0.75rem 1rem;
                /* Ensure proper touch targets */
                min-height: 3rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .mobile-dropdown-notification-item .truncate {
                /* Better text truncation on mobile */
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100%;
            }

            /* Ensure notification content fits */
            .mobile-dropdown-notification-item p {
                max-width: 100%;
                word-break: break-word;
                margin: 0;
                line-height: 1.4;
            }

            /* Improve notification time display on mobile */
            .mobile-dropdown-notification-item .text-xs {
                font-size: 0.75rem;
                margin-top: 0.25rem;
            }
        }

        /* Extra small mobile screens */
        @media (max-width: 480px) {
            #mobileNotificationDropdown {
                width: calc(100vw - 0.5rem) !important;
                right: 0.25rem !important;
                max-width: none !important;
            }
        }

        /* Desktop Notification Dropdown */
        @media (min-width: 641px) {
            #notificationDropdown {
                /* Ensure desktop dropdown doesn't overflow */
                max-width: calc(100vw - 2rem);
            }
        }
        /* IBM Plex Sans Arabic Font Classes */
        .ibm-plex-arabic-thin {
            font-family: "IBM Plex Sans Arabic", sans-serif;
            font-weight: 100;
            font-style: normal;
        }

        .ibm-plex-arabic-extralight {
            font-family: "IBM Plex Sans Arabic", sans-serif;
            font-weight: 200;
            font-style: normal;
        }

        .ibm-plex-arabic-light {
            font-family: "IBM Plex Sans Arabic", sans-serif;
            font-weight: 300;
            font-style: normal;
        }

        .ibm-plex-arabic-regular {
            font-family: "IBM Plex Sans Arabic", sans-serif;
            font-weight: 400;
            font-style: normal;
        }

        .ibm-plex-arabic-medium {
            font-family: "IBM Plex Sans Arabic", sans-serif;
            font-weight: 500;
            font-style: normal;
        }

        .ibm-plex-arabic-semibold {
            font-family: "IBM Plex Sans Arabic", sans-serif;
            font-weight: 600;
            font-style: normal;
        }

        .ibm-plex-arabic-bold {
            font-family: "IBM Plex Sans Arabic", sans-serif;
            font-weight: 700;
            font-style: normal;
        }
        .enhanced-nav-link {
            display: block;
            padding: 12px 16px;
            margin: 4px 0;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            position: relative;
            overflow: hidden;
            font-family: "IBM Plex Sans Arabic", sans-serif;
        }

        .enhanced-nav-link:hover {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            color: #1f2937;
            transform: translateX(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .enhanced-nav-link:active {
            transform: translateX(0);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .enhanced-nav-link.active {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border-right: 3px solid #3b82f6;
        }

        .enhanced-nav-link.active i {
            color: #3b82f6 !important;
        }

        .enhanced-nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1));
            transition: width 0.3s ease;
        }

        .enhanced-nav-link:hover::before {
            width: 100%;
        }

        .enhanced-nav-link i {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        /* Sidebar enhancements */
        .sidebar-section {
            margin-bottom: 24px;
        }

        .sidebar-title {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 8px;
            padding: 0 16px;
        }

        /* Active state detection */
        .enhanced-nav-link[href*="/admin/dashboard"].active,
        .enhanced-nav-link[href*="/admin/users"].active,
        .enhanced-nav-link[href*="/admin/requests"].active,
        .enhanced-nav-link[href*="/admin/activities"].active,
        .enhanced-nav-link[href*="/admin/requests-records"].active,
        .enhanced-nav-link[href*="/dashboard"].active,
        .enhanced-nav-link[href*="/requests"].active,
        .enhanced-nav-link[href*="/messages"].active,
        .enhanced-nav-link[href*="/profile"].active,
        .enhanced-nav-link[href*="/achievements"].active {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border-right: 3px solid #3b82f6;
        }

        /* Mobile navigation styles */
        .mobile-nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #374151;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
            font-family: "IBM Plex Sans Arabic", sans-serif;
        }

        .mobile-nav-link:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }

        .mobile-nav-link i {
            width: 20px;
            color: #6b7280;
        }

        /* Mobile menu animations */
        .mobile-menu-open #mobile-menu-panel {
            transform: translateX(0);
        }

        /* Touch-friendly mobile menu */
        @media (max-width: 768px) {
            .mobile-nav-link {
                min-height: 44px; /* iOS recommended touch target size */
                padding: 12px 16px;
            }

            .mobile-toggle {
                min-height: 44px;
                min-width: 44px;
            }
        }

        /* Mark All Read Button Styling */
        #markAllReadBtn, #mobileMarkAllReadBtn {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            background: rgba(59, 130, 246, 0.05);
        }

        #markAllReadBtn:hover, #mobileMarkAllReadBtn:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
        }

        #markAllReadBtn:active, #mobileMarkAllReadBtn:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
        }

        #markAllReadBtn:disabled, #mobileMarkAllReadBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Mobile-specific adjustments */
        @media (max-width: 640px) {
            #markAllReadBtn, #mobileMarkAllReadBtn {
                min-width: 44px;
                min-height: 44px;
                padding: 8px 12px;
            }
        }

        /* Mobile-optimized notification styles */
        @media (max-width: 768px) {
            .success-notification {
                bottom: 16px !important;
                padding: 12px 16px !important;
                min-width: auto !important;
                width: calc(100% - 32px) !important;
                max-width: 400px !important;
                border-radius: 10px !important;
            }
        }

        @media (max-width: 480px) {
            .success-notification {
                bottom: 12px !important;
                padding: 10px 14px !important;
                width: calc(100% - 24px) !important;
                max-width: none !important;
                border-radius: 8px !important;
            }
        }

        /* Responsive sidebar */
        @media (max-width: 768px) {
            .enhanced-nav-link {
                padding: 10px 12px;
                font-size: 13px;
            }

            .enhanced-nav-link i {
                font-size: 14px;
            }
        }
    </style>

    <!-- Additional CSS from child templates -->
    {% block extra_css %}{% endblock %}

    <!-- Additional head content from child templates -->
    {% block head %}{% endblock %}
</head>
<body class="bg-gray-50 font-arabic">
    <!-- Enhanced Header Component -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <img src="/static/logo_cmsvs.png" alt="إرشيف" class="logo-image h-16 w-auto ml-3">
                    <div class="text-right">
                        <h1 class="logo-title text-xl font-bold text-gray-900 ibm-plex-arabic-bold">إرشيف</h1>
                        <p class="logo-subtitle text-sm text-gray-600 ibm-plex-arabic-medium">الدفاع المدني</p>
                    </div>
                </div>

                {% if current_user %}
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-8 space-x-reverse">
                    {% if current_user.role.value == 'admin' %}
                    <a href="/admin/dashboard" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium ibm-plex-arabic-medium">لوحة التحكم</a>
                    <a href="/admin/users" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium ibm-plex-arabic-medium">المستخدمون</a>
                    <a href="/admin/requests" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium ibm-plex-arabic-medium">الطلبات</a>
                    {% else %}
                    <a href="/requests" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium ibm-plex-arabic-medium">طلباتي</a>
                    <a href="/requests/create" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium ibm-plex-arabic-medium">طلب جديد</a>
                    {% endif %}
                    <a href="/profile" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium ibm-plex-arabic-medium">الملف الشخصي</a>
                    <!-- Notification Bell Dropdown -->
                    <div class="relative">
                        <button id="notificationBell" class="text-gray-700 hover:text-blue-600 p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 relative">
                            <i class="fas fa-bell text-lg"></i>
                            <span id="headerNotificationBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="notificationDropdown" class="hidden absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <!-- Dropdown Header -->
                            <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                                <h3 class="text-sm font-medium text-gray-900 ibm-plex-arabic-medium">الإشعارات</h3>
                                <button
                                    id="markAllReadBtn"
                                    onclick="markAllNotificationsAsRead()"
                                    class="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-all duration-200 ibm-plex-arabic-medium"
                                    title="تحديد الكل كمقروء"
                                >
                                    <i class="fas fa-check-double"></i>
                                    <span class="hidden sm:inline">تحديد الكل كمقروء</span>
                                </button>
                            </div>

                            <!-- Notifications List -->
                            <div id="dropdownNotificationsList" class="max-h-64 overflow-y-auto">
                                <!-- Loading state -->
                                <div id="notificationsLoading" class="px-4 py-3 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    جاري التحميل...
                                </div>

                                <!-- Empty state -->
                                <div id="notificationsEmpty" class="hidden px-4 py-6 text-center text-gray-500">
                                    <i class="fas fa-bell-slash text-2xl mb-2"></i>
                                    <p class="text-sm">لا توجد إشعارات جديدة</p>
                                </div>

                                <!-- Notifications will be loaded here -->
                            </div>

                            <!-- Dropdown Footer -->
                            <div class="px-4 py-3 border-t border-gray-200">
                                <a href="/notifications" class="block text-center text-blue-600 hover:text-blue-800 text-sm font-medium ibm-plex-arabic-medium">
                                    جميع الإشعارات
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="/auth/logout" class="text-red-600 hover:text-red-800 px-3 py-2 rounded-md text-sm font-medium ibm-plex-arabic-medium">تسجيل الخروج</a>
                </nav>

                <!-- Notifications Bell (Mobile) -->
                <div class="md:hidden flex items-center space-x-2 space-x-reverse">
                    <div class="relative">
                        <button id="mobileNotificationBell" class="text-gray-600 hover:text-gray-900 focus:outline-none relative p-2">
                            <i class="fas fa-bell text-xl"></i>
                            <span id="mobileNotificationBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- Mobile Dropdown Menu -->
                        <div id="mobileNotificationDropdown" class="hidden absolute right-0 mt-2 w-80 max-w-[calc(100vw-1rem)] bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <!-- Dropdown Header -->
                            <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                                <h3 class="text-sm font-medium text-gray-900 ibm-plex-arabic-medium">الإشعارات</h3>
                                <button
                                    id="mobileMarkAllReadBtn"
                                    onclick="markAllNotificationsAsRead()"
                                    class="flex items-center gap-2 px-3 py-2 text-xs font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-all duration-200 ibm-plex-arabic-medium min-h-[44px]"
                                    title="تحديد الكل كمقروء"
                                >
                                    <i class="fas fa-check-double"></i>
                                    <span class="hidden xs:inline">تحديد الكل كمقروء</span>
                                </button>
                            </div>

                            <!-- Notifications List -->
                            <div id="mobileDropdownNotificationsList" class="max-h-64 overflow-y-auto">
                                <!-- Loading state -->
                                <div id="mobileNotificationsLoading" class="px-4 py-3 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    جاري التحميل...
                                </div>

                                <!-- Empty state -->
                                <div id="mobileNotificationsEmpty" class="hidden px-4 py-6 text-center text-gray-500">
                                    <i class="fas fa-bell-slash text-2xl mb-2"></i>
                                    <p class="text-sm">لا توجد إشعارات جديدة</p>
                                </div>

                                <!-- Notifications will be loaded here -->
                            </div>

                            <!-- Dropdown Footer -->
                            <div class="px-4 py-3 border-t border-gray-200">
                                <a href="/notifications" class="block text-center text-blue-600 hover:text-blue-800 text-sm font-medium ibm-plex-arabic-medium">
                                    جميع الإشعارات
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Menu Toggle Button -->
                    <button onclick="toggleMobileMenu()" class="mobile-toggle p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500" aria-label="Toggle mobile menu">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="flex min-h-screen">
        {% if current_user %}
        <!-- Enhanced Sidebar - Hidden on mobile -->
        <div id="sidebar" class="hidden md:block w-64 bg-gradient-to-b from-gray-50 to-white shadow-lg border-l border-gray-200">
            <div class="p-6">
                {% block sidebar %}
                <nav class="space-y-1">
                    {% if current_user.role.value == 'admin' %}
                    <a href="/admin/dashboard" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-tachometer-alt text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>لوحة التحكم</span>
                        </div>
                    </a>
                    <a href="/admin/users" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-users text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>المستخدمون</span>
                        </div>
                    </a>
                    <a href="/admin/requests" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-file-alt text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>الطلبات</span>
                        </div>
                    </a>
                    <a href="/admin/activities" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>النشاطات</span>
                        </div>
                    </a>
                    <a href="/admin/requests-records" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-clipboard-list text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>سجل الطلبات</span>
                        </div>
                    </a>
                    <a href="/admin/user-activity-report" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-chart-bar text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>حركة المستخدمين</span>
                        </div>
                    </a>

                    {% else %}
                    <a href="/dashboard" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-home text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>الرئيسية</span>
                        </div>
                    </a>
                    <a href="/dashboard/bento" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-th-large text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>لوحة التحكم التفاعلية</span>
                        </div>
                    </a>
                    <a href="/requests" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-folder text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>طلباتي</span>
                        </div>
                    </a>
                    <a href="/requests/new" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-plus-circle text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>طلب جديد</span>
                        </div>
                    </a>
                    <a href="/achievements" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-trophy text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>الإنجازات والمسابقات</span>
                        </div>
                    </a>
                    <a href="/profile" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-user text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>الملف الشخصي</span>
                        </div>
                    </a>
                    <a href="/my-profile" class="enhanced-nav-link group">
                        <div class="flex items-center">
                            <i class="fas fa-id-card text-gray-500 group-hover:text-blue-600 transition-colors duration-200 ml-3"></i>
                            <span>ملفي الشخصي</span>
                        </div>
                    </a>
                    <a href="/users/table/user-view" class="nav-link">
                        <span>ملفي الشخصي - جدول</span>
                    </a>
                    {% endif %}
                </nav>
                {% endblock %}
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobile-menu" class="fixed inset-0 z-50 md:hidden hidden">
            <!-- Backdrop -->
            <div class="fixed inset-0 bg-black bg-opacity-50" onclick="toggleMobileMenu()"></div>

            <!-- Menu Panel -->
            <div class="fixed inset-y-0 right-0 w-64 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out" id="mobile-menu-panel">
                <div class="flex items-center justify-between p-4 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">القائمة</h2>
                    <button onclick="toggleMobileMenu()" class="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <nav class="p-4 space-y-1">
                    {% if current_user.role.value == 'admin' %}
                    <a href="/admin/dashboard" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-tachometer-alt ml-3"></i>
                        <span>لوحة التحكم</span>
                    </a>
                    <a href="/admin/users" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-users ml-3"></i>
                        <span>المستخدمون</span>
                    </a>
                    <a href="/admin/requests" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-file-alt ml-3"></i>
                        <span>الطلبات</span>
                    </a>
                    <a href="/admin/requests-records" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-archive ml-3"></i>
                        <span>سجل الطلبات</span>
                    </a>
                    <a href="/admin/user-activity-report" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-chart-bar ml-3"></i>
                        <span>حركة المستخدمين</span>
                    </a>

                    <!-- User Profile Link for Admin -->
                    <a href="/profile" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-user ml-3"></i>
                        <span>الملف الشخصي</span>
                    </a>

                    <!-- Logout Button for Admin -->
                    <form method="post" action="/logout" class="block">
                        <button type="submit" class="mobile-nav-link w-full text-right" onclick="toggleMobileMenu()">
                            <i class="fas fa-sign-out-alt ml-3"></i>
                            <span>تسجيل الخروج</span>
                        </button>
                    </form>
                    {% else %}
                    <a href="/dashboard" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-home ml-3"></i>
                        <span>الرئيسية</span>
                    </a>
                    <a href="/requests" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-file-alt ml-3"></i>
                        <span>طلباتي</span>
                    </a>
                    <a href="/requests/new" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-plus ml-3"></i>
                        <span>طلب جديد</span>
                    </a>
                    <a href="/messages/inbox" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-envelope ml-3"></i>
                        <span>الرسائل</span>
                    </a>
                    <a href="/achievements" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-trophy ml-3"></i>
                        <span>الإنجازات والمسابقات</span>
                    </a>
                    <a href="/profile" class="mobile-nav-link" onclick="toggleMobileMenu()">
                        <i class="fas fa-user ml-3"></i>
                        <span>الملف الشخصي</span>
                    </a>

                    <!-- Logout Button -->
                    <form method="post" action="/logout" class="block">
                        <button type="submit" class="mobile-nav-link w-full text-right" onclick="toggleMobileMenu()">
                            <i class="fas fa-sign-out-alt ml-3"></i>
                            <span>تسجيل الخروج</span>
                        </button>
                    </form>
                    {% endif %}
                </nav>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 w-full md:w-auto">
            <div class="p-4 md:p-6">
        {% else %}
        <!-- Full width for non-authenticated users -->
        <div class="w-full">
            <div class="p-4 md:p-6">
        {% endif %}

                <!-- Alerts -->
                {% if error %}
                <div class="alert-danger mb-6">
                    {{ error }}
                </div>
                {% endif %}

                {% if success %}
                <div class="alert-success mb-6">
                    {{ success }}
                </div>
                {% endif %}

                <!-- Page Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Mobile Menu JavaScript -->
    <script>
        // Mobile menu toggle function
        function toggleMobileMenu() {
            console.log('Mobile menu toggle clicked'); // Debug log
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuPanel = document.getElementById('mobile-menu-panel');

            if (!mobileMenu || !mobileMenuPanel) {
                console.error('Mobile menu elements not found');
                return;
            }

            if (mobileMenu.classList.contains('hidden')) {
                // Show menu
                console.log('Showing mobile menu');
                mobileMenu.classList.remove('hidden');
                setTimeout(() => {
                    mobileMenuPanel.classList.remove('translate-x-full');
                }, 10);
                document.body.style.overflow = 'hidden';
            } else {
                // Hide menu
                console.log('Hiding mobile menu');
                mobileMenuPanel.classList.add('translate-x-full');
                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                    document.body.style.overflow = '';
                }, 300);
            }
        }

        // Add keyboard support for mobile menu
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const mobileMenu = document.getElementById('mobile-menu');
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    toggleMobileMenu();
                }
            }
        });
    </script>

    <!-- Pull to Refresh JavaScript -->
    <script>
        /**
         * CMSVS Pull to Refresh Feature
         * Implements native-like pull-to-refresh functionality for mobile devices
         */
        class PullToRefresh {
            constructor() {
                this.isEnabled = this.isMobileDevice();
                this.isRefreshing = false;
                this.startY = 0;
                this.currentY = 0;
                this.pullDistance = 0;
                this.threshold = 80; // Minimum pull distance to trigger refresh
                this.maxPullDistance = 120; // Maximum visual pull distance
                this.refreshIndicator = null;
                this.isPulling = false;

                if (this.isEnabled) {
                    this.init();
                }
            }

            isMobileDevice() {
                // More robust mobile detection - only real mobile devices
                const userAgent = navigator.userAgent.toLowerCase();
                const isMobileUA = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
                const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
                const isSmallScreen = window.innerWidth <= 768;

                // Must be a mobile user agent AND have touch support
                return isMobileUA && hasTouchSupport;
            }

            init() {
                // Only attach event listeners, create indicator when needed
                this.attachEventListeners();
            }

            createRefreshIndicator() {
                // Create a hidden indicator for functionality only
                if (this.refreshIndicator) return;

                this.refreshIndicator = document.createElement('div');
                this.refreshIndicator.id = 'pull-refresh-indicator';
                this.refreshIndicator.style.display = 'none'; // Completely hidden

                // Add minimal hidden element to body
                document.body.appendChild(this.refreshIndicator);
            }

            attachEventListeners() {
                // Only touch events for mobile devices - no mouse events
                document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
                document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
                document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
            }

            handleTouchStart(e) {
                // Only start if at top of page and not already refreshing
                if (this.isRefreshing || window.scrollY > 0) return;

                this.startY = e.touches[0].clientY;
                this.currentY = this.startY;
                this.isPulling = false;
            }

            handleTouchMove(e) {
                // Only proceed if conditions are met
                if (this.isRefreshing || window.scrollY > 0 || !this.startY) return;

                this.currentY = e.touches[0].clientY;
                this.pullDistance = Math.max(0, this.currentY - this.startY);

                // Track pulling without showing visual indicator
                if (this.pullDistance > 10) { // Small threshold to avoid accidental triggers
                    if (!this.isPulling) {
                        this.isPulling = true;
                        this.createRefreshIndicator(); // Create hidden indicator for functionality
                    }

                    e.preventDefault(); // Prevent page scroll
                    // No visual update needed since indicator is hidden
                } else if (this.isPulling && this.pullDistance <= 0) {
                    // User stopped pulling
                    this.isPulling = false;
                }
            }

            handleTouchEnd(e) {
                if (this.isRefreshing || !this.startY) return;

                if (this.isPulling) {
                    if (this.pullDistance >= this.threshold) {
                        this.triggerRefresh();
                    }
                    // No need to hide indicator since it's already hidden
                }

                // Reset state
                this.startY = 0;
                this.currentY = 0;
                this.pullDistance = 0;
                this.isPulling = false;
            }

            hideIndicator() {
                // No visual hiding needed since indicator is already hidden
                if (this.refreshIndicator && !this.isRefreshing) {
                    this.refreshIndicator.remove();
                    this.refreshIndicator = null;
                }
            }

            updateIndicator() {
                // No visual updates needed since indicator is hidden
                // This method is kept for compatibility but does nothing
                return;
            }

            triggerRefresh() {
                if (this.isRefreshing) return;

                this.isRefreshing = true;

                // No visual feedback, just trigger the refresh silently
                this.performRefresh().then(() => {
                    this.completeRefresh();
                });
            }

            async performRefresh() {
                // Add a minimum delay for better UX
                const minDelay = new Promise(resolve => setTimeout(resolve, 1000));

                try {
                    // Check if there's a custom refresh function defined on the page
                    if (typeof window.customRefresh === 'function') {
                        await Promise.all([minDelay, window.customRefresh()]);
                    } else {
                        // Default refresh behavior - reload the page
                        await minDelay;
                        window.location.reload();
                    }
                } catch (error) {
                    console.error('Refresh failed:', error);
                    await minDelay;
                    // Fallback to page reload
                    window.location.reload();
                }
            }

            completeRefresh() {
                // No visual feedback needed, just reset state
                this.isRefreshing = false;
                this.hideIndicator();
            }

            resetIndicator() {
                // This method is now replaced by hideIndicator()
                this.hideIndicator();
            }
        }

        // Initialize pull to refresh when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            new PullToRefresh();
        });
    </script>

    <!-- Push Notifications JavaScript -->
    <script>
        /**
         * CMSVS Push Notifications System
         * Handles browser push notifications for real-time updates
         */
        class PushNotificationManager {
            constructor() {
                this.isSupported = 'serviceWorker' in navigator && 'PushManager' in window;
                this.isSubscribed = false;
                this.registration = null;
                this.permissionState = 'default';
                this.notificationBanner = null;

                if (this.isSupported) {
                    this.init();
                }
            }

            async init() {
                try {
                    // Register service worker
                    this.registration = await navigator.serviceWorker.register('/static/js/sw.js');
                    console.log('Service Worker registered successfully');

                    // Check current permission state
                    this.permissionState = Notification.permission;

                    // Check if already subscribed
                    const subscription = await this.registration.pushManager.getSubscription();
                    this.isSubscribed = !(subscription === null);

                    if (this.isSubscribed) {
                        console.log('User is already subscribed to push notifications');
                    } else {
                        // Handle permission state gracefully
                        await this.handlePermissionState();
                    }

                } catch (error) {
                    console.error('Error initializing push notifications:', error);
                }
            }

            async handlePermissionState() {
                switch (this.permissionState) {
                    case 'granted':
                        await this.subscribeUser();
                        break;
                    case 'denied':
                        this.showPermissionBlockedMessage();
                        break;
                    case 'default':
                        // Don't automatically request permission, wait for user interaction
                        this.showNotificationPrompt();
                        break;
                }
            }

            showNotificationPrompt() {
                // Only show prompt if user hasn't seen it recently
                const lastPromptTime = localStorage.getItem('notificationPromptTime');
                const now = Date.now();
                const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

                if (lastPromptTime && (now - parseInt(lastPromptTime)) < oneDay) {
                    return; // Don't show prompt again within 24 hours
                }

                this.createNotificationBanner(
                    'تفعيل الإشعارات',
                    'هل تريد تفعيل الإشعارات لتلقي تحديثات الطلبات؟',
                    [
                        {
                            text: 'تفعيل',
                            action: () => this.requestPermissionAndSubscribe(),
                            primary: true
                        },
                        {
                            text: 'لاحقاً',
                            action: () => this.dismissNotificationBanner(),
                            primary: false
                        }
                    ]
                );
            }

            showPermissionBlockedMessage() {
                this.createNotificationBanner(
                    'الإشعارات محجوبة',
                    'تم حجب الإشعارات. لتفعيلها، انقر على أيقونة القفل بجانب عنوان الموقع واختر "السماح" للإشعارات.',
                    [
                        {
                            text: 'فهمت',
                            action: () => this.dismissNotificationBanner(),
                            primary: true
                        },
                        {
                            text: 'كيفية التفعيل',
                            action: () => this.showPermissionInstructions(),
                            primary: false
                        }
                    ]
                );
            }

            async requestPermissionAndSubscribe() {
                try {
                    // Store the time when we requested permission
                    localStorage.setItem('notificationPromptTime', Date.now().toString());

                    const permission = await Notification.requestPermission();
                    this.permissionState = permission;

                    if (permission === 'granted') {
                        await this.subscribeUser();
                        this.dismissNotificationBanner();
                        this.showSuccessMessage('تم تفعيل الإشعارات بنجاح!');
                    } else if (permission === 'denied') {
                        this.dismissNotificationBanner();
                        this.showPermissionBlockedMessage();
                    } else {
                        this.dismissNotificationBanner();
                        console.log('Push notification permission dismissed');
                    }
                } catch (error) {
                    console.error('Error requesting permission:', error);
                    this.dismissNotificationBanner();
                }
            }

            async subscribeUser() {
                try {
                    const applicationServerKey = this.urlB64ToUint8Array(
                        'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9LdNnC_AQPV4dQFSWVuYQkOiMXqr5t1I-0HcmUcf3ckMiXv1pLVs'
                    );

                    const subscription = await this.registration.pushManager.subscribe({
                        userVisibleOnly: true,
                        applicationServerKey: applicationServerKey
                    });

                    // Send subscription to server
                    await this.sendSubscriptionToServer(subscription);

                    this.isSubscribed = true;
                    console.log('User subscribed to push notifications');

                } catch (error) {
                    console.error('Error subscribing user:', error);
                }
            }

            async sendSubscriptionToServer(subscription) {
                try {
                    const subscriptionData = {
                        endpoint: subscription.endpoint,
                        p256dh: btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('p256dh')))),
                        auth: btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('auth')))),
                        device_name: this.getDeviceName()
                    };

                    const response = await fetch('/api/push/subscribe', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(subscriptionData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        console.log('Subscription sent to server successfully');
                    } else {
                        console.error('Failed to send subscription to server:', result.error);
                    }

                } catch (error) {
                    console.error('Error sending subscription to server:', error);
                }
            }

            getDeviceName() {
                const userAgent = navigator.userAgent;

                if (/Android/i.test(userAgent)) {
                    return 'Android Device';
                } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
                    return 'iOS Device';
                } else if (/Windows/i.test(userAgent)) {
                    return 'Windows Device';
                } else if (/Mac/i.test(userAgent)) {
                    return 'Mac Device';
                } else if (/Linux/i.test(userAgent)) {
                    return 'Linux Device';
                } else {
                    return 'Unknown Device';
                }
            }

            urlB64ToUint8Array(base64String) {
                const padding = '='.repeat((4 - base64String.length % 4) % 4);
                const base64 = (base64String + padding)
                    .replace(/\-/g, '+')
                    .replace(/_/g, '/');

                const rawData = window.atob(base64);
                const outputArray = new Uint8Array(rawData.length);

                for (let i = 0; i < rawData.length; ++i) {
                    outputArray[i] = rawData.charCodeAt(i);
                }
                return outputArray;
            }

            async unsubscribe() {
                try {
                    const subscription = await this.registration.pushManager.getSubscription();

                    if (subscription) {
                        await subscription.unsubscribe();

                        // Notify server
                        await fetch('/api/push/unsubscribe', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                endpoint: subscription.endpoint
                            })
                        });

                        this.isSubscribed = false;
                        console.log('User unsubscribed from push notifications');
                    }

                } catch (error) {
                    console.error('Error unsubscribing:', error);
                }
            }

            createNotificationBanner(title, message, buttons) {
                // Remove existing banner if any
                this.dismissNotificationBanner();

                // Create banner element
                this.notificationBanner = document.createElement('div');
                this.notificationBanner.className = 'notification-permission-banner';
                this.notificationBanner.innerHTML = `
                    <div class="banner-content">
                        <div class="banner-icon">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                            </svg>
                        </div>
                        <div class="banner-text">
                            <h4 class="banner-title">${title}</h4>
                            <p class="banner-message">${message}</p>
                        </div>
                        <div class="banner-actions">
                            ${buttons.map(button => `
                                <button class="banner-btn ${button.primary ? 'primary' : 'secondary'}"
                                        data-action="${button.text}">
                                    ${button.text}
                                </button>
                            `).join('')}
                        </div>
                    </div>
                `;

                // Add styles
                const style = document.createElement('style');
                style.textContent = `
                    .notification-permission-banner {
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        z-index: 10000;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        animation: slideDown 0.3s ease-out;
                    }
                    .banner-content {
                        display: flex;
                        align-items: center;
                        padding: 12px 20px;
                        max-width: 1200px;
                        margin: 0 auto;
                        gap: 15px;
                    }
                    .banner-icon {
                        flex-shrink: 0;
                        width: 24px;
                        height: 24px;
                    }
                    .banner-text {
                        flex: 1;
                        min-width: 0;
                    }
                    .banner-title {
                        font-weight: 600;
                        margin: 0 0 4px 0;
                        font-size: 14px;
                    }
                    .banner-message {
                        margin: 0;
                        font-size: 13px;
                        opacity: 0.9;
                        line-height: 1.4;
                    }
                    .banner-actions {
                        display: flex;
                        gap: 8px;
                        flex-shrink: 0;
                    }
                    .banner-btn {
                        padding: 6px 16px;
                        border: none;
                        border-radius: 4px;
                        font-size: 13px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s;
                    }
                    .banner-btn.primary {
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: 1px solid rgba(255,255,255,0.3);
                    }
                    .banner-btn.primary:hover {
                        background: rgba(255,255,255,0.3);
                    }
                    .banner-btn.secondary {
                        background: transparent;
                        color: rgba(255,255,255,0.8);
                        border: 1px solid rgba(255,255,255,0.2);
                    }
                    .banner-btn.secondary:hover {
                        background: rgba(255,255,255,0.1);
                        color: white;
                    }
                    @keyframes slideDown {
                        from { transform: translateY(-100%); }
                        to { transform: translateY(0); }
                    }
                    @keyframes slideUp {
                        from { transform: translateY(0); }
                        to { transform: translateY(-100%); }
                    }
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOutRight {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                    @media (max-width: 768px) {
                        .banner-content {
                            flex-direction: column;
                            text-align: center;
                            gap: 10px;
                        }
                        .banner-actions {
                            width: 100%;
                            justify-content: center;
                        }
                    }
                `;
                document.head.appendChild(style);

                // Add event listeners
                buttons.forEach((button, index) => {
                    const btnElement = this.notificationBanner.querySelector(`[data-action="${button.text}"]`);
                    if (btnElement) {
                        btnElement.addEventListener('click', button.action);
                    }
                });

                // Add to page
                document.body.insertBefore(this.notificationBanner, document.body.firstChild);

                // Adjust body padding to account for banner
                document.body.style.paddingTop = this.notificationBanner.offsetHeight + 'px';
            }

            dismissNotificationBanner() {
                if (this.notificationBanner) {
                    this.notificationBanner.style.animation = 'slideUp 0.3s ease-out';
                    setTimeout(() => {
                        if (this.notificationBanner && this.notificationBanner.parentNode) {
                            this.notificationBanner.parentNode.removeChild(this.notificationBanner);
                            document.body.style.paddingTop = '';
                        }
                        this.notificationBanner = null;
                    }, 300);
                }
            }

            showPermissionInstructions() {
                const instructions = `
                    <div style="text-align: right; line-height: 1.6;">
                        <h3>كيفية تفعيل الإشعارات:</h3>
                        <ol style="margin: 10px 0; padding-right: 20px;">
                            <li>انقر على أيقونة القفل 🔒 أو المعلومات ℹ️ في شريط العنوان</li>
                            <li>ابحث عن "الإشعارات" أو "Notifications"</li>
                            <li>اختر "السماح" أو "Allow"</li>
                            <li>أعد تحميل الصفحة</li>
                        </ol>
                        <p><strong>أو:</strong> امسح بيانات الموقع من إعدادات المتصفح وأعد المحاولة</p>
                    </div>
                `;

                // Create modal or alert
                if (confirm('هل تريد عرض تعليمات تفعيل الإشعارات؟')) {
                    const instructionWindow = window.open('', '_blank', 'width=500,height=400');
                    instructionWindow.document.write(`
                        <html dir="rtl">
                        <head>
                            <title>تعليمات تفعيل الإشعارات</title>
                            <style>
                                body { font-family: Arial, sans-serif; padding: 20px; }
                                h3 { color: #333; }
                                ol { color: #555; }
                                p { color: #666; }
                            </style>
                        </head>
                        <body>${instructions}</body>
                        </html>
                    `);
                }
            }

            showSuccessMessage(message) {
                // Create a mobile-optimized success notification
                const successDiv = document.createElement('div');
                successDiv.className = 'success-notification';

                // Create notification content with icon
                successDiv.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <i class="fas fa-check-circle" style="font-size: 18px; flex-shrink: 0;"></i>
                        <span style="flex: 1; text-align: right; word-wrap: break-word;">${message}</span>
                    </div>
                `;

                successDiv.style.cssText = `
                    position: fixed;
                    bottom: 24px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    padding: 14px 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
                    z-index: 10001;
                    font-family: 'IBM Plex Sans Arabic', sans-serif;
                    max-width: calc(100% - 32px);
                    min-width: 280px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    opacity: 0;
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                `;

                document.body.appendChild(successDiv);

                // Trigger show animation
                requestAnimationFrame(() => {
                    successDiv.style.opacity = '1';
                    successDiv.style.transform = 'translateX(-50%) translateY(0)';
                });

                // Remove after 4 seconds
                setTimeout(() => {
                    successDiv.style.opacity = '0';
                    successDiv.style.transform = 'translateX(-50%) translateY(20px)';
                    setTimeout(() => {
                        if (successDiv.parentNode) {
                            successDiv.parentNode.removeChild(successDiv);
                        }
                    }, 400);
                }, 4000);
            }
        }

        // Initialize push notifications when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            if ('serviceWorker' in navigator && 'PushManager' in window) {
                window.pushManager = new PushNotificationManager();
            }

            // Initialize notification badge updates
            updateNotificationBadges();

            // Update badges every 30 seconds
            setInterval(updateNotificationBadges, 30000);

            // Initialize notification dropdown
            initializeNotificationDropdown();
        });

        // Function to update notification badges
        function updateNotificationBadges() {
            fetch('/api/notifications/unread-count')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    const count = data.unread_count || 0;
                    const badges = [
                        'headerNotificationBadge',
                        'mobileNotificationBadge'
                    ];

                    badges.forEach(badgeId => {
                        const badge = document.getElementById(badgeId);
                        if (badge) {
                            if (count > 0) {
                                badge.textContent = count > 99 ? '99+' : count;
                                badge.classList.remove('hidden');
                            } else {
                                badge.classList.add('hidden');
                            }
                        }
                    });
                }
            })
            .catch(error => {
                // Silently handle errors to avoid console spam
                // Only log if it's not a common authentication error
                if (!error.message.includes('403') && !error.message.includes('401')) {
                    console.error('Error updating notification badges:', error);
                }
            });
        }

        // Mark All Notifications as Read Function
        function markAllNotificationsAsRead() {
            const markAllBtns = [
                document.getElementById('markAllReadBtn'),
                document.getElementById('mobileMarkAllReadBtn')
            ];

            // Show loading state
            markAllBtns.forEach(btn => {
                if (btn) {
                    btn.disabled = true;
                    const icon = btn.querySelector('i');
                    const text = btn.querySelector('span');

                    if (icon) {
                        icon.className = 'fas fa-spinner fa-spin';
                    }
                    if (text) {
                        text.textContent = 'جاري التحديث...';
                    }
                }
            });

            // Send request to mark all as read
            fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Update notification badges
                    updateNotificationBadges();

                    // Clear dropdown notifications
                    const dropdownLists = [
                        document.getElementById('dropdownNotificationsList'),
                        document.getElementById('mobileDropdownNotificationsList')
                    ];

                    dropdownLists.forEach(list => {
                        if (list) {
                            const emptyState = list.querySelector('[id$="Empty"]');
                            if (emptyState) {
                                emptyState.classList.remove('hidden');
                            }
                            // Remove all notification items
                            const notifications = list.querySelectorAll('.dropdown-notification-item');
                            notifications.forEach(notification => notification.remove());
                        }
                    });

                    // Show success notification
                    if (typeof showSuccessNotification === 'function') {
                        showSuccessNotification('تم تحديد جميع الإشعارات كمقروءة بنجاح');
                    } else if (typeof showNotification === 'function') {
                        showNotification('تم تحديد جميع الإشعارات كمقروءة بنجاح', null, 'success');
                    }
                } else {
                    throw new Error(data.message || 'فشل في تحديد الإشعارات');
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);

                // Show error notification
                if (typeof showErrorNotification === 'function') {
                    showErrorNotification('حدث خطأ أثناء تحديد الإشعارات');
                } else if (typeof showNotification === 'function') {
                    showNotification('حدث خطأ أثناء تحديد الإشعارات', null, 'error');
                }
            })
            .finally(() => {
                // Reset button states
                markAllBtns.forEach(btn => {
                    if (btn) {
                        btn.disabled = false;
                        const icon = btn.querySelector('i');
                        const text = btn.querySelector('span');

                        if (icon) {
                            icon.className = 'fas fa-check-double';
                        }
                        if (text) {
                            text.textContent = 'تحديد الكل كمقروء';
                        }
                    }
                });
            });
        }

        // Notification Dropdown Functions
        function initializeNotificationDropdown() {
            // Desktop notification bell
            const bell = document.getElementById('notificationBell');
            const dropdown = document.getElementById('notificationDropdown');

            // Mobile notification bell
            const mobileBell = document.getElementById('mobileNotificationBell');
            const mobileDropdown = document.getElementById('mobileNotificationDropdown');

            // Initialize desktop dropdown
            if (bell && dropdown) {
                // Toggle dropdown on bell click
                bell.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close mobile dropdown if open
                    if (mobileDropdown && !mobileDropdown.classList.contains('hidden')) {
                        hideMobileNotificationDropdown();
                    }

                    if (dropdown.classList.contains('hidden')) {
                        showNotificationDropdown();
                    } else {
                        hideNotificationDropdown();
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!bell.contains(e.target) && !dropdown.contains(e.target)) {
                        hideNotificationDropdown();
                    }
                });
            }

            // Initialize mobile dropdown
            if (mobileBell && mobileDropdown) {
                // Toggle dropdown on mobile bell click/touch
                mobileBell.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close desktop dropdown if open
                    if (dropdown && !dropdown.classList.contains('hidden')) {
                        hideNotificationDropdown();
                    }

                    if (mobileDropdown.classList.contains('hidden')) {
                        showMobileNotificationDropdown();
                    } else {
                        hideMobileNotificationDropdown();
                    }
                });

                // Add touch event for better mobile responsiveness
                mobileBell.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Trigger click event
                    mobileBell.click();
                });

                // Close dropdown when clicking/touching outside
                document.addEventListener('click', function(e) {
                    if (!mobileBell.contains(e.target) && !mobileDropdown.contains(e.target)) {
                        hideMobileNotificationDropdown();
                    }
                });

                document.addEventListener('touchend', function(e) {
                    if (!mobileBell.contains(e.target) && !mobileDropdown.contains(e.target)) {
                        hideMobileNotificationDropdown();
                    }
                });

                // Reposition dropdown on window resize/orientation change
                window.addEventListener('resize', function() {
                    if (!mobileDropdown.classList.contains('hidden')) {
                        positionMobileNotificationDropdown(mobileBell, mobileDropdown);
                    }
                });

                window.addEventListener('orientationchange', function() {
                    setTimeout(function() {
                        if (!mobileDropdown.classList.contains('hidden')) {
                            positionMobileNotificationDropdown(mobileBell, mobileDropdown);
                        }
                    }, 100);
                });
            }

            // Close dropdowns on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    hideNotificationDropdown();
                    hideMobileNotificationDropdown();
                }
            });
        }

        function showNotificationDropdown() {
            const dropdown = document.getElementById('notificationDropdown');
            dropdown.classList.remove('hidden');
            loadDropdownNotifications();
        }

        function hideNotificationDropdown() {
            const dropdown = document.getElementById('notificationDropdown');
            dropdown.classList.add('hidden');
        }

        // Mobile Notification Dropdown Functions
        function showMobileNotificationDropdown() {
            const dropdown = document.getElementById('mobileNotificationDropdown');
            const bell = document.getElementById('mobileNotificationBell');

            // Position dropdown properly on mobile
            positionMobileNotificationDropdown(bell, dropdown);

            dropdown.classList.remove('hidden');
            loadMobileDropdownNotifications();
        }

        function hideMobileNotificationDropdown() {
            const dropdown = document.getElementById('mobileNotificationDropdown');
            dropdown.classList.add('hidden');
        }

        function positionMobileNotificationDropdown(bell, dropdown) {
            if (!bell || !dropdown) return;

            // Get viewport dimensions
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const bellRect = bell.getBoundingClientRect();

            // Reset positioning
            dropdown.style.position = 'fixed';
            dropdown.style.top = '';
            dropdown.style.bottom = '';
            dropdown.style.left = '';
            dropdown.style.right = '';

            // Calculate positioning
            const dropdownWidth = Math.min(320, viewportWidth - 16); // Max 320px or viewport - 16px margin
            const margin = 8;

            // Position horizontally (align to right edge of bell)
            let rightPosition = viewportWidth - bellRect.right;

            // Ensure dropdown doesn't go off-screen
            if (rightPosition + dropdownWidth > viewportWidth - margin) {
                rightPosition = margin;
            }

            dropdown.style.right = rightPosition + 'px';
            dropdown.style.width = dropdownWidth + 'px';

            // Position vertically (below bell with margin)
            const topPosition = bellRect.bottom + 8;
            const maxHeight = viewportHeight - topPosition - margin;

            dropdown.style.top = topPosition + 'px';
            dropdown.style.maxHeight = Math.min(maxHeight, viewportHeight * 0.6) + 'px';

            // Update the notifications list max height
            const notificationsList = document.getElementById('mobileDropdownNotificationsList');
            if (notificationsList) {
                notificationsList.style.maxHeight = (Math.min(maxHeight, viewportHeight * 0.6) - 120) + 'px'; // Account for header/footer
            }
        }

        function loadDropdownNotifications() {
            const loadingEl = document.getElementById('notificationsLoading');
            const emptyEl = document.getElementById('notificationsEmpty');
            const listEl = document.getElementById('dropdownNotificationsList');

            // Show loading state
            loadingEl.classList.remove('hidden');
            emptyEl.classList.add('hidden');

            // Clear existing notifications (except loading and empty states)
            const existingNotifications = listEl.querySelectorAll('.dropdown-notification-item');
            existingNotifications.forEach(item => item.remove());

            // Fetch recent notifications
            fetch('/api/notifications/recent?limit=5')
                .then(response => response.json())
                .then(data => {
                    loadingEl.classList.add('hidden');

                    if (data.success && data.notifications && data.notifications.length > 0) {
                        data.notifications.forEach(notification => {
                            const notificationEl = createDropdownNotificationItem(notification);
                            listEl.appendChild(notificationEl);
                        });
                    } else {
                        emptyEl.classList.remove('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    loadingEl.classList.add('hidden');
                    emptyEl.classList.remove('hidden');
                });
        }

        function loadMobileDropdownNotifications() {
            const loadingEl = document.getElementById('mobileNotificationsLoading');
            const emptyEl = document.getElementById('mobileNotificationsEmpty');
            const listEl = document.getElementById('mobileDropdownNotificationsList');

            // Show loading state
            loadingEl.classList.remove('hidden');
            emptyEl.classList.add('hidden');

            // Clear existing notifications (except loading and empty states)
            const existingNotifications = listEl.querySelectorAll('.mobile-dropdown-notification-item');
            existingNotifications.forEach(item => item.remove());

            // Fetch recent notifications
            fetch('/api/notifications/recent?limit=5')
                .then(response => response.json())
                .then(data => {
                    loadingEl.classList.add('hidden');

                    if (data.success && data.notifications && data.notifications.length > 0) {
                        data.notifications.forEach(notification => {
                            const notificationEl = createMobileDropdownNotificationItem(notification);
                            listEl.appendChild(notificationEl);
                        });
                    } else {
                        emptyEl.classList.remove('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error loading mobile notifications:', error);
                    loadingEl.classList.add('hidden');
                    emptyEl.classList.remove('hidden');
                });
        }

        function createMobileDropdownNotificationItem(notification) {
            const item = document.createElement('div');
            item.className = `mobile-dropdown-notification-item px-4 py-3 border-b border-gray-100 hover:bg-gray-50 active:bg-gray-100 cursor-pointer transition-colors duration-150 ${!notification.is_read ? 'bg-blue-50' : ''}`;

            const timeAgo = getTimeAgo(notification.created_at);
            const isUnread = !notification.is_read;

            item.innerHTML = `
                <div class="flex items-start space-x-3 space-x-reverse">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <i class="fas fa-bell text-blue-600 text-sm"></i>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 ${isUnread ? 'font-semibold' : ''}" style="line-height: 1.4; word-break: break-word;">
                            ${notification.title}
                        </p>
                        <p class="text-xs text-gray-500 mt-1" style="line-height: 1.3; word-break: break-word; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                            ${notification.message}
                        </p>
                        <p class="text-xs text-gray-400 mt-2">
                            ${timeAgo}
                        </p>
                    </div>
                    ${isUnread ? '<div class="flex-shrink-0 mt-1"><div class="w-3 h-3 bg-blue-600 rounded-full"></div></div>' : ''}
                </div>
            `;

            // Add touch-friendly click handler
            item.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Add visual feedback
                item.style.backgroundColor = '#e5e7eb';

                setTimeout(() => {
                    // Close mobile dropdown
                    hideMobileNotificationDropdown();

                    if (notification.action_url) {
                        window.location.href = notification.action_url;
                    } else {
                        window.location.href = `/notifications/${notification.id}`;
                    }
                }, 150);
            });

            // Add touch event for better mobile responsiveness
            item.addEventListener('touchstart', function(e) {
                item.style.backgroundColor = '#f3f4f6';
            });

            item.addEventListener('touchend', function(e) {
                setTimeout(() => {
                    if (item.style.backgroundColor === 'rgb(243, 244, 246)') {
                        item.style.backgroundColor = '';
                    }
                }, 100);
            });

            return item;
        }

        function createDropdownNotificationItem(notification) {
            const item = document.createElement('div');
            item.className = `dropdown-notification-item px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${!notification.is_read ? 'bg-blue-50' : ''}`;

            const timeAgo = getTimeAgo(notification.created_at);
            const isUnread = !notification.is_read;

            item.innerHTML = `
                <div class="flex items-start space-x-3 space-x-reverse">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                            <i class="fas fa-bell text-blue-600 text-sm"></i>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate ${isUnread ? 'font-semibold' : ''}">
                            ${notification.title}
                        </p>
                        <p class="text-xs text-gray-500 truncate">
                            ${notification.message}
                        </p>
                        <p class="text-xs text-gray-400 mt-1">
                            ${timeAgo}
                        </p>
                    </div>
                    ${isUnread ? '<div class="flex-shrink-0"><div class="w-2 h-2 bg-blue-600 rounded-full"></div></div>' : ''}
                </div>
            `;

            // Add click handler
            item.addEventListener('click', function() {
                if (notification.action_url) {
                    window.location.href = notification.action_url;
                } else {
                    window.location.href = `/notifications/${notification.id}`;
                }
            });

            return item;
        }

        function getTimeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) {
                return 'الآن';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `منذ ${minutes} دقيقة`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `منذ ${hours} ساعة`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `منذ ${days} يوم`;
            }
        }
    </script>

    <!-- Enhanced Sidebar JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get current page URL
            const currentPath = window.location.pathname;

            // Get all navigation links
            const navLinks = document.querySelectorAll('.enhanced-nav-link');

            // Remove active class from all links
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            // Add active class to current page link
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && (currentPath === href || (href !== '/' && currentPath.startsWith(href)))) {
                    link.classList.add('active');
                }
            });

            // Special handling for exact matches
            if (currentPath === '/admin/dashboard') {
                document.querySelector('a[href="/admin/dashboard"]')?.classList.add('active');
            } else if (currentPath === '/dashboard') {
                document.querySelector('a[href="/dashboard"]')?.classList.add('active');
            }

            // Add hover effects and animations
            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateX(-2px)';
                    }
                });

                link.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateX(0)';
                    }
                });
            });
        });
    </script>

    <!-- Password Toggle Functionality -->
    <script src="{{ url_for('static', path='js/password-toggle.js') }}"></script>

    <!-- Additional JavaScript from child templates -->
    {% block extra_js %}{% endblock %}
</body>
</html>