from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, or_, and_
from app.models.organization import Organization, Application
from app.models.activity import Activity, ActivityType
from app.services.cache import cached, cache
from fastapi import HTTPException, UploadFile
import logging
import os
import shutil
from pathlib import Path
from app.config import settings


logger = logging.getLogger(__name__)


class OrganizationService:
    """Service for organization operations with caching support"""

    @staticmethod
    def create_organization(
        db: Session,
        name: str,
        building: str,
        street: str,
        block: str,
        description: Optional[str] = None,
        contact_person: Optional[str] = None,
        contact_phone: Optional[str] = None,
        contact_email: Optional[str] = None
    ) -> Organization:
        """Create a new organization"""
        # Check if organization name already exists
        existing_org = db.query(Organization).filter(Organization.name == name).first()
        
        if existing_org:
            raise HTTPException(status_code=400, detail="Organization name already exists")
        
        # Create new organization
        organization = Organization(
            name=name.strip(),
            building=building.strip(),
            street=street.strip(),
            block=block.strip(),
            description=description.strip() if description else None,
            contact_person=contact_person.strip() if contact_person else None,
            contact_phone=contact_phone.strip() if contact_phone else None,
            contact_email=contact_email.strip() if contact_email else None
        )
        
        db.add(organization)
        db.commit()
        db.refresh(organization)
        
        logger.info(f"Created organization: {organization.name}")
        return organization

    @staticmethod
    def get_organization_by_id(db: Session, organization_id: int) -> Optional[Organization]:
        """Get organization by ID"""
        return db.query(Organization).filter(Organization.id == organization_id).first()

    @staticmethod
    def get_organization_by_name(db: Session, name: str) -> Optional[Organization]:
        """Get organization by name"""
        return db.query(Organization).filter(Organization.name == name).first()

    @staticmethod
    def get_all_organizations(db: Session, skip: int = 0, limit: int = 100, active_only: bool = True) -> List[Organization]:
        """Get all organizations with pagination"""
        query = db.query(Organization)
        
        if active_only:
            query = query.filter(Organization.is_active == True)
        
        return query.order_by(desc(Organization.created_at)).offset(skip).limit(limit).all()

    @staticmethod
    def search_organizations(
        db: Session, 
        search_term: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Organization]:
        """Search organizations by name, building, street, or block"""
        search_pattern = f"%{search_term.strip()}%"
        
        return db.query(Organization).filter(
            and_(
                Organization.is_active == True,
                or_(
                    Organization.name.ilike(search_pattern),
                    Organization.building.ilike(search_pattern),
                    Organization.street.ilike(search_pattern),
                    Organization.block.ilike(search_pattern)
                )
            )
        ).order_by(desc(Organization.created_at)).offset(skip).limit(limit).all()

    @staticmethod
    def update_organization(
        db: Session,
        organization_id: int,
        **kwargs
    ) -> Optional[Organization]:
        """Update organization details"""
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Check if name is being changed and if it conflicts
        if 'name' in kwargs and kwargs['name'] != organization.name:
            existing_org = db.query(Organization).filter(
                and_(
                    Organization.name == kwargs['name'],
                    Organization.id != organization_id
                )
            ).first()
            
            if existing_org:
                raise HTTPException(status_code=400, detail="Organization name already exists")
        
        # Update fields
        for key, value in kwargs.items():
            if hasattr(organization, key) and value is not None:
                setattr(organization, key, value.strip() if isinstance(value, str) else value)
        
        db.commit()
        db.refresh(organization)
        
        logger.info(f"Updated organization: {organization.name}")
        return organization

    @staticmethod
    def delete_organization(db: Session, organization_id: int) -> bool:
        """Soft delete organization (set is_active to False)"""
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Check if organization has applications
        if organization.applications_count > 0:
            raise HTTPException(
                status_code=400, 
                detail="Cannot delete organization with existing applications"
            )
        
        organization.is_active = False
        db.commit()
        
        logger.info(f"Deleted organization: {organization.name}")
        return True


class ApplicationService:
    """Service for application operations"""

    @staticmethod
    def create_application(
        db: Session,
        organization_id: int,
        name: str,
        mobile: str,
        benayat: Optional[str] = None,
        notes: Optional[str] = None
    ) -> Application:
        """Create a new application"""
        # Verify organization exists
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Generate unique application ID
        application_id = Application.generate_application_id()
        
        # Ensure application ID is unique
        while db.query(Application).filter(Application.application_id == application_id).first():
            application_id = Application.generate_application_id()
        
        # Create new application
        application = Application(
            application_id=application_id,
            organization_id=organization_id,
            name=name.strip(),
            benayat=benayat.strip() if benayat else None,
            mobile=mobile.strip(),
            notes=notes.strip() if notes else None
        )
        
        db.add(application)
        db.commit()
        db.refresh(application)
        
        logger.info(f"Created application: {application.application_id} for organization: {organization.name}")
        return application

    @staticmethod
    def get_application_by_id(db: Session, application_id: int) -> Optional[Application]:
        """Get application by ID"""
        return db.query(Application).filter(Application.id == application_id).first()

    @staticmethod
    def get_application_by_application_id(db: Session, application_id: str) -> Optional[Application]:
        """Get application by application_id string"""
        return db.query(Application).filter(Application.application_id == application_id).first()

    @staticmethod
    def get_applications_by_organization(
        db: Session, 
        organization_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Application]:
        """Get all applications for an organization"""
        return db.query(Application).filter(
            and_(
                Application.organization_id == organization_id,
                Application.is_active == True
            )
        ).order_by(desc(Application.created_at)).offset(skip).limit(limit).all()

    @staticmethod
    def get_all_applications(db: Session, skip: int = 0, limit: int = 100) -> List[Application]:
        """Get all applications with pagination"""
        return db.query(Application).filter(
            Application.is_active == True
        ).order_by(desc(Application.created_at)).offset(skip).limit(limit).all()

    @staticmethod
    def update_application(
        db: Session,
        application_id: int,
        **kwargs
    ) -> Optional[Application]:
        """Update application details"""
        application = ApplicationService.get_application_by_id(db, application_id)
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Update fields
        for key, value in kwargs.items():
            if hasattr(application, key) and value is not None:
                setattr(application, key, value.strip() if isinstance(value, str) else value)
        
        db.commit()
        db.refresh(application)
        
        logger.info(f"Updated application: {application.application_id}")
        return application

    @staticmethod
    def delete_application(db: Session, application_id: int) -> bool:
        """Soft delete application (set is_active to False)"""
        application = ApplicationService.get_application_by_id(db, application_id)
        
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        
        application.is_active = False
        db.commit()
        
        logger.info(f"Deleted application: {application.application_id}")
        return True

    @staticmethod
    def get_upload_directory(organization_name: str, application_id: str) -> str:
        """Get the upload directory path for an application"""
        # Sanitize organization name for filesystem
        safe_org_name = "".join(c for c in organization_name if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_org_name = safe_org_name.replace(' ', '_')

        upload_path = os.path.join(settings.upload_directory, safe_org_name, application_id)

        # Create directory if it doesn't exist
        os.makedirs(upload_path, exist_ok=True)

        return upload_path

    @staticmethod
    def validate_file(file: UploadFile) -> bool:
        """Validate uploaded file"""
        if not file.filename:
            return False

        # Check file size
        if file.size and file.size > settings.max_file_size:
            return False

        # Check file extension
        file_ext = file.filename.split('.')[-1].lower()
        if file_ext not in settings.allowed_file_types_list:
            return False

        return True

    @staticmethod
    def save_application_file(
        db: Session,
        application_id: int,
        attachment_index: int,
        file: UploadFile
    ) -> bool:
        """Save uploaded file for an application"""
        if not ApplicationService.validate_file(file):
            raise HTTPException(status_code=400, detail="Invalid file type or size")

        application = ApplicationService.get_application_by_id(db, application_id)
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")

        if attachment_index < 1 or attachment_index > 4:
            raise HTTPException(status_code=400, detail="Invalid attachment index")

        # Get upload directory
        upload_dir = ApplicationService.get_upload_directory(
            application.organization.name,
            application.application_id
        )

        # Generate safe filename
        file_ext = file.filename.split('.')[-1].lower()
        safe_filename = f"attachment_{attachment_index}.{file_ext}"
        file_path = os.path.join(upload_dir, safe_filename)

        try:
            # Save file
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            # Update application record
            path_field = f"attachment{attachment_index}_path"
            filename_field = f"attachment{attachment_index}_filename"

            setattr(application, path_field, file_path)
            setattr(application, filename_field, file.filename)

            db.commit()
            db.refresh(application)

            logger.info(f"Saved file {file.filename} for application {application.application_id}")
            return True

        except Exception as e:
            logger.error(f"Error saving file: {str(e)}")
            # Clean up file if it was created
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=500, detail="Error saving file")

    @staticmethod
    def delete_application_file(
        db: Session,
        application_id: int,
        attachment_index: int
    ) -> bool:
        """Delete an attachment file from an application"""
        application = ApplicationService.get_application_by_id(db, application_id)
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")

        if attachment_index < 1 or attachment_index > 4:
            raise HTTPException(status_code=400, detail="Invalid attachment index")

        path_field = f"attachment{attachment_index}_path"
        filename_field = f"attachment{attachment_index}_filename"

        file_path = getattr(application, path_field)

        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"Deleted file {file_path}")
            except Exception as e:
                logger.error(f"Error deleting file {file_path}: {str(e)}")

        # Clear database fields
        setattr(application, path_field, None)
        setattr(application, filename_field, None)

        db.commit()
        db.refresh(application)

        return True

    @staticmethod
    def get_file_path(db: Session, application_id: int, attachment_index: int) -> Optional[str]:
        """Get file path for an attachment"""
        application = ApplicationService.get_application_by_id(db, application_id)
        if not application:
            return None

        if attachment_index < 1 or attachment_index > 4:
            return None

        path_field = f"attachment{attachment_index}_path"
        return getattr(application, path_field)
