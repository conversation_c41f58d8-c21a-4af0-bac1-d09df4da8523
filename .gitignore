# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Environment files
.env.local
.env.development
.env.test

# Secrets
secrets/
*.key
*.crt
*.pem

# Backups
backups/
*.sql
*.sql.gz

# Uploads (in production, you might want to backup these separately)
uploads/request_*/

# Node modules
node_modules/

# Mobile build artifacts
android/build/
android/app/build/
ios/build/
ios/App/build/
www/

# Capacitor
.capacitor/

# Build outputs
app/static/css/output.css

# Test results
test-results/
coverage/
.coverage
htmlcov/

# Temporary files
*.tmp
*.temp
*.bak
