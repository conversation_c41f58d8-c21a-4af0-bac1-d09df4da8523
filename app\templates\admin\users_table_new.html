<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جدول المستخدمين - لوحة التحكم</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Modern Arabic font stack */
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif, 'Arabic UI Text', 'Geeza Pro', 'Traditional Arabic', 'Simplified Arabic';
        }

        /* Enhanced profile avatar */
        .profile-avatar {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            background-color: #f3f4f6;
            border: 3px solid #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced status badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.2s ease;
        }

        .status-badge.active {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
        }

        .status-badge.inactive {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        /* Role badges */
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .role-badge.admin {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fbbf24;
        }

        .role-badge.manager {
            background-color: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }

        .role-badge.user {
            background-color: #f0f9ff;
            color: #0369a1;
            border: 1px solid #0ea5e9;
        }

        /* Enhanced table styling */
        .table-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .table-header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-bottom: 2px solid #e5e7eb;
        }

        .table-row {
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f5f9;
        }

        .table-row:hover {
            background-color: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .table-row:last-child {
            border-bottom: none;
        }

        /* Action buttons */
        .action-btn {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            font-size: 11px;
            font-weight: 500;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            min-width: 80px;
            justify-content: center;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .action-btn.edit {
            background-color: #eff6ff;
            color: #1d4ed8;
            border-color: #bfdbfe;
        }

        .action-btn.edit:hover {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .action-btn.activities {
            background-color: #ecfdf5;
            color: #059669;
            border-color: #a7f3d0;
        }

        .action-btn.activities:hover {
            background-color: #d1fae5;
            color: #047857;
        }

        .action-btn.upload {
            background-color: #fed7aa;
            color: #ea580c;
            border-color: #fdba74;
        }

        .action-btn.upload:hover {
            background-color: #ffedd5;
            color: #c2410c;
        }

        .action-btn.profile {
            background-color: #ecfdf5;
            color: #059669;
            border-color: #a7f3d0;
        }

        .action-btn.profile:hover {
            background-color: #d1fae5;
            color: #047857;
        }

        .action-btn.requests {
            background-color: #fef3c7;
            color: #d97706;
            border-color: #fde68a;
        }

        .action-btn.requests:hover {
            background-color: #fef08a;
            color: #b45309;
        }

        /* Header styling */
        .page-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            border-radius: 16px;
            margin-bottom: 2rem;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(6, 182, 212, 0.3);
        }

        .add-user-btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .add-user-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
        }

        /* Search and filter styling */
        .search-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
        }

        .search-input {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.2s ease;
            width: 100%;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .profile-avatar {
                width: 48px;
                height: 48px;
            }

            .action-btn {
                padding: 4px 8px;
                font-size: 10px;
                min-width: 60px;
            }
        }

        /* Loading animation */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Enhanced tooltips */
        [title] {
            position: relative;
            cursor: help;
        }

        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: pre-line;
            z-index: 1000;
            max-width: 300px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        [title]:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(100%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1001;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen" dir="rtl">

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Enhanced Header Section -->
        <div class="page-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="p-3 bg-white bg-opacity-20 rounded-full">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold mb-2">إدارة المستخدمين</h1>
                        <p class="text-blue-100 text-lg">قائمة شاملة بجميع المستخدمين مع إمكانيات الإدارة والمتابعة</p>
                        <div class="flex items-center space-x-4 space-x-reverse mt-3">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-user-check text-green-300"></i>
                                <span class="text-sm">{{ active_users }} نشط</span>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-users text-blue-300"></i>
                                <span class="text-sm">{{ total_users }} إجمالي</span>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-file-alt text-purple-300"></i>
                                <span class="text-sm">{{ total_requests }} طلب</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="/admin/dashboard" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all duration-200">
                        <i class="fas fa-arrow-right mr-2"></i>
                        العودة للوحة التحكم
                    </a>
                    <button onclick="window.location.href='/admin/users/new'" class="add-user-btn">
                        <i class="fas fa-plus"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="search-container">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input type="text"
                               id="searchInput"
                               class="search-input pr-10"
                               placeholder="البحث عن مستخدم بالاسم أو البريد الإلكتروني...">
                    </div>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <div class="relative">
                        <select id="roleFilter" class="search-input max-w-48">
                            <option value="">جميع الأدوار</option>
                            <option value="admin" data-count="0">مدير النظام</option>
                            <option value="manager" data-count="0">مدير المشاريع</option>
                            <option value="user" data-count="0">عضو الفريق</option>
                        </select>
                        <div id="roleFilterIndicator" class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full hidden"></div>
                    </div>
                    <div class="relative">
                        <select id="statusFilter" class="search-input max-w-32">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                        <div id="statusFilterIndicator" class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full hidden"></div>
                    </div>
                    <button id="clearFiltersBtn" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors hidden">
                        <i class="fas fa-times mr-2"></i>
                        مسح الفلاتر
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Users Table -->
        <div class="table-container">
            <!-- Table Header -->
            <div class="table-header">
                <div class="grid grid-cols-12 gap-4 py-6 px-6">
                    <div class="col-span-4">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-user text-gray-600"></i>
                            <h3 class="text-sm font-bold text-gray-800 uppercase tracking-wide">معلومات المستخدم</h3>
                        </div>
                    </div>
                    <div class="col-span-2">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-briefcase text-gray-600"></i>
                            <h3 class="text-sm font-bold text-gray-800 uppercase tracking-wide">الدور</h3>
                        </div>
                    </div>
                    <div class="col-span-2">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-toggle-on text-gray-600"></i>
                            <h3 class="text-sm font-bold text-gray-800 uppercase tracking-wide">الحالة</h3>
                        </div>
                    </div>
                    <div class="col-span-2">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-chart-line text-gray-600"></i>
                            <h3 class="text-sm font-bold text-gray-800 uppercase tracking-wide">الإحصائيات</h3>
                        </div>
                    </div>
                    <div class="col-span-2">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-cogs text-gray-600"></i>
                            <h3 class="text-sm font-bold text-gray-800 uppercase tracking-wide">الإجراءات</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Table Rows -->
            <div class="divide-y divide-gray-100" id="usersTableBody">
                {% for user in users %}
                <div class="grid grid-cols-12 gap-4 py-6 px-6 items-center table-row user-row"
                     data-user-name="{{ (user.full_name or user.username)|lower }}"
                     data-user-email="{{ user.email|lower }}"
                     data-user-role="{{ user.role.value }}"
                     data-user-status="{{ 'active' if user.is_active else 'inactive' }}">

                    <!-- User Information -->
                    <div class="col-span-4">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="profile-avatar" style="background-image: url('{{ user.avatar_url }}');"></div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 space-x-reverse mb-1">
                                    <h4 class="text-base font-semibold text-gray-900">{{ user.full_name or user.username }}</h4>
                                    {% if user.id == current_user.id %}
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                        <i class="fas fa-crown mr-1"></i>
                                        أنت
                                    </span>
                                    {% endif %}
                                </div>
                                <p class="text-sm text-gray-600 mb-1">{{ user.email }}</p>
                                <div class="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>انضم في {{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'غير محدد' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Role -->
                    <div class="col-span-2">
                        <span class="role-badge {% if user.role.value == 'admin' %}admin{% elif user.role.value == 'manager' %}manager{% else %}user{% endif %}">
                            {% if user.role.value == 'admin' %}
                                <i class="fas fa-crown mr-1"></i>
                                مدير النظام
                            {% elif user.role.value == 'manager' %}
                                <i class="fas fa-user-tie mr-1"></i>
                                مدير المشاريع
                            {% else %}
                                <i class="fas fa-user mr-1"></i>
                                مستخدم عادي
                            {% endif %}
                        </span>
                    </div>

                    <!-- Status -->
                    <div class="col-span-2">
                        <div class="space-y-2">
                            <span class="status-badge {{ 'active' if user.is_active else 'inactive' }}">
                                <i class="fas fa-circle mr-1 text-xs"></i>
                                {{ 'نشط' if user.is_active else 'غير نشط' }}
                            </span>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="col-span-2">
                        <div class="space-y-1">
                            <div class="flex items-center space-x-2 space-x-reverse text-sm"
                                 title="إجمالي الطلبات: {{ user.total_requests }}&#10;معلق: {{ user.pending_requests }}&#10;قيد المعالجة: {{ user.in_progress_requests }}&#10;مكتمل: {{ user.completed_requests }}&#10;مرفوض: {{ user.rejected_requests }}">
                                <i class="fas fa-file-alt text-blue-500"></i>
                                {% if user.total_requests > 0 %}
                                <a href="/admin/users/{{ user.id }}/requests"
                                   class="font-medium text-blue-600 hover:text-blue-800 transition-colors">{{ user.total_requests }}</a>
                                {% else %}
                                <span class="font-medium text-gray-900">{{ user.total_requests }}</span>
                                {% endif %}
                                <span class="text-gray-500 text-xs">طلب</span>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse text-sm">
                                <i class="fas fa-clock text-gray-400"></i>
                                <span class="text-xs text-gray-500" title="{% if user.last_activity_time %}{{ user.last_activity_time.strftime('%Y-%m-%d %H:%M') }}{% endif %}">
                                    آخر نشاط: {{ user.last_activity }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- Actions -->
                    <div class="col-span-2">
                        {% if user.id != current_user.id %}
                        <div class="grid grid-cols-2 gap-2">
                            <!-- تعديل المستخدم -->
                            <a href="/admin/users/{{ user.id }}/edit" class="action-btn edit">
                                <i class="fas fa-edit mr-1"></i>
                                تعديل
                            </a>

                            <!-- سجل الأنشطة -->
                            <a href="/admin/users/{{ user.id }}/activities" class="action-btn activities">
                                <i class="fas fa-history mr-1"></i>
                                الأنشطة
                            </a>

                            <!-- رفع طلب جديد -->
                            <a href="/admin/users/{{ user.id }}/upload-request" class="action-btn upload">
                                <i class="fas fa-upload mr-1"></i>
                                رفع طلب
                            </a>

                            <!-- الملف الشخصي -->
                            <a href="/profile/{{ user.id }}" class="action-btn profile">
                                <i class="fas fa-user mr-1"></i>
                                الملف الشخصي
                            </a>

                            <!-- الطلبات -->
                            <a href="/admin/users/{{ user.id }}/requests" class="action-btn requests">
                                <i class="fas fa-file-alt mr-1"></i>
                                الطلبات
                            </a>
                        </div>
                        {% else %}
                        <div class="grid grid-cols-2 gap-2">
                            <!-- ملفي الشخصي -->
                            <a href="/profile" class="action-btn profile">
                                <i class="fas fa-user-circle mr-1"></i>
                                ملفي الشخصي
                            </a>

                            <!-- طلباتي -->
                            <a href="/admin/users/{{ user.id }}/requests" class="action-btn requests">
                                <i class="fas fa-clipboard-list mr-1"></i>
                                طلباتي
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Empty State -->
        <div id="emptyState" class="text-center py-12 hidden">
            <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد نتائج</h3>
            <p class="text-gray-500">لم يتم العثور على مستخدمين يطابقون معايير البحث.</p>
            <button onclick="clearFilters()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                إزالة جميع الفلاتر
            </button>
        </div>
    </div>

    <!-- Footer -->
    <div class="mt-8 text-center text-sm text-gray-500">
        <p>© 2024 نظام إدارة المستخدمين. جميع الحقوق محفوظة.</p>
    </div>
</div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Search and Filter functionality
            const searchInput = document.getElementById('searchInput');
            const roleFilter = document.getElementById('roleFilter');
            const statusFilter = document.getElementById('statusFilter');
            const userRows = document.querySelectorAll('.user-row');

            // Make functions globally accessible
            window.filterUsers = function() {
                const searchTerm = searchInput.value.toLowerCase();
                const selectedRole = roleFilter.value;
                const selectedStatus = statusFilter.value;

                let visibleCount = 0;

                userRows.forEach(row => {
                    const userName = row.dataset.userName;
                    const userEmail = row.dataset.userEmail;
                    const userRole = row.dataset.userRole;
                    const userStatus = row.dataset.userStatus;

                    const matchesSearch = !searchTerm ||
                        userName.includes(searchTerm) ||
                        userEmail.includes(searchTerm);

                    const matchesRole = !selectedRole || userRole === selectedRole;
                    const matchesStatus = !selectedStatus || userStatus === selectedStatus;

                    if (matchesSearch && matchesRole && matchesStatus) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Update results count
                updateResultsCount(visibleCount);

                // Show/hide empty state
                const emptyState = document.getElementById('emptyState');
                const tableContainer = document.querySelector('.table-container');

                if (visibleCount === 0) {
                    emptyState.classList.remove('hidden');
                    tableContainer.style.opacity = '0.5';
                } else {
                    emptyState.classList.add('hidden');
                    tableContainer.style.opacity = '1';
                }

                // Update filter indicators
                updateFilterIndicators();
            };

            window.updateFilterIndicators = function() {
                const roleFilterIndicator = document.getElementById('roleFilterIndicator');
                const statusFilterIndicator = document.getElementById('statusFilterIndicator');
                const clearFiltersBtn = document.getElementById('clearFiltersBtn');

                // Show/hide role filter indicator
                if (roleFilter.value) {
                    roleFilterIndicator.classList.remove('hidden');
                } else {
                    roleFilterIndicator.classList.add('hidden');
                }

                // Show/hide status filter indicator
                if (statusFilter.value) {
                    statusFilterIndicator.classList.remove('hidden');
                } else {
                    statusFilterIndicator.classList.add('hidden');
                }

                // Show/hide clear filters button
                if (roleFilter.value || statusFilter.value || searchInput.value) {
                    clearFiltersBtn.classList.remove('hidden');
                } else {
                    clearFiltersBtn.classList.add('hidden');
                }
            }

            window.updateFilterCounts = function() {
                // Count users by role
                const roleCounts = { admin: 0, manager: 0, user: 0 };
                const statusCounts = { active: 0, inactive: 0 };

                userRows.forEach(row => {
                    const userRole = row.dataset.userRole;
                    const userStatus = row.dataset.userStatus;

                    if (roleCounts.hasOwnProperty(userRole)) {
                        roleCounts[userRole]++;
                    }

                    if (statusCounts.hasOwnProperty(userStatus)) {
                        statusCounts[userStatus]++;
                    }
                });

                // Update role filter options
                const roleOptions = roleFilter.querySelectorAll('option[value]');
                roleOptions.forEach(option => {
                    const role = option.value;
                    if (role && roleCounts.hasOwnProperty(role)) {
                        const count = roleCounts[role];
                        const originalText = option.textContent.split(' (')[0]; // Remove existing count
                        option.textContent = `${originalText} (${count})`;
                    }
                });

                // Update status filter options
                const statusOptions = statusFilter.querySelectorAll('option[value]');
                statusOptions.forEach(option => {
                    const status = option.value;
                    if (status && statusCounts.hasOwnProperty(status)) {
                        const count = statusCounts[status];
                        const originalText = option.textContent.split(' (')[0]; // Remove existing count
                        option.textContent = `${originalText} (${count})`;
                    }
                });
            };

            function updateResultsCount(count) {
                let resultsElement = document.getElementById('resultsCount');
                if (!resultsElement) {
                    resultsElement = document.createElement('div');
                    resultsElement.id = 'resultsCount';
                    resultsElement.className = 'flex items-center justify-between text-sm text-gray-600 mt-3 p-3 bg-gray-50 rounded-lg';
                    document.querySelector('.search-container').appendChild(resultsElement);
                }

                const hasFilters = roleFilter.value || statusFilter.value || searchInput.value;
                let statusText = '';
                let statusIcon = '';

                if (count === userRows.length && !hasFilters) {
                    statusText = `عرض جميع المستخدمين (${count})`;
                    statusIcon = '<i class="fas fa-users text-blue-500"></i>';
                } else if (count === userRows.length && hasFilters) {
                    statusText = `جميع المستخدمين يطابقون الفلاتر (${count})`;
                    statusIcon = '<i class="fas fa-check-circle text-green-500"></i>';
                } else if (count === 0) {
                    statusText = `لا توجد نتائج تطابق الفلاتر`;
                    statusIcon = '<i class="fas fa-exclamation-triangle text-orange-500"></i>';
                } else {
                    statusText = `عرض ${count} من ${userRows.length} مستخدم`;
                    statusIcon = '<i class="fas fa-filter text-purple-500"></i>';
                }

                resultsElement.innerHTML = `
                    <div class="flex items-center space-x-2 space-x-reverse">
                        ${statusIcon}
                        <span>${statusText}</span>
                    </div>
                    ${hasFilters ? '<span class="text-xs text-gray-500">الفلاتر نشطة</span>' : ''}
                `;
            }

            // Event listeners for search and filters
            searchInput.addEventListener('input', filterUsers);
            roleFilter.addEventListener('change', filterUsers);
            statusFilter.addEventListener('change', filterUsers);

            // Clear filters button
            const clearFiltersBtn = document.getElementById('clearFiltersBtn');
            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', function() {
                    clearFilters();
                });
            };

            // Initialize results count and run initial filter
            updateResultsCount(userRows.length);
            updateFilterIndicators();
            updateFilterCounts();

            // Enhanced action button functionality
            const actionButtons = document.querySelectorAll('.action-btn');
            actionButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Add loading state
                    const originalContent = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>جاري التحميل...';
                    this.style.pointerEvents = 'none';

                    // Restore if navigation fails
                    setTimeout(() => {
                        if (this.innerHTML.includes('جاري التحميل')) {
                            this.innerHTML = originalContent;
                            this.style.pointerEvents = 'auto';
                        }
                    }, 3000);
                });
            });

            // Enhanced table row hover effects
            userRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // Profile avatar click to enlarge
            const profileAvatars = document.querySelectorAll('.profile-avatar');
            profileAvatars.forEach(avatar => {
                avatar.addEventListener('click', function() {
                    const modal = document.createElement('div');
                    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                    modal.innerHTML = `
                        <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
                            <div class="text-center">
                                <div class="w-32 h-32 mx-auto rounded-full bg-cover bg-center border-4 border-white shadow-lg"
                                     style="background-image: url('${this.style.backgroundImage.slice(5, -2)}');"></div>
                                <button onclick="this.closest('.fixed').remove()"
                                        class="mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                                    إغلاق
                                </button>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(modal);

                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            modal.remove();
                        }
                    });
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + K to focus search
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    searchInput.focus();
                }

                // Escape to clear search
                if (e.key === 'Escape' && document.activeElement === searchInput) {
                    searchInput.value = '';
                    filterUsers();
                }
            });

            // Add keyboard shortcut hint
            const searchContainer = document.querySelector('.search-container');
            const shortcutHint = document.createElement('div');
            shortcutHint.className = 'text-xs text-gray-500 mt-1';
            shortcutHint.innerHTML = '<i class="fas fa-keyboard mr-1"></i>اضغط Ctrl+K للبحث السريع';
            searchContainer.appendChild(shortcutHint);

            // Smooth scroll to top button
            const scrollToTopBtn = document.createElement('button');
            scrollToTopBtn.className = 'fixed bottom-6 left-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 opacity-0 pointer-events-none';
            scrollToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
            scrollToTopBtn.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
            document.body.appendChild(scrollToTopBtn);

            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    scrollToTopBtn.style.opacity = '1';
                    scrollToTopBtn.style.pointerEvents = 'auto';
                } else {
                    scrollToTopBtn.style.opacity = '0';
                    scrollToTopBtn.style.pointerEvents = 'none';
                }
            });

            // Loading animation for page transitions
            const links = document.querySelectorAll('a[href^="/"]');
            links.forEach(link => {
                link.addEventListener('click', function() {
                    if (!this.classList.contains('action-btn')) {
                        const loader = document.createElement('div');
                        loader.className = 'fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50';
                        loader.innerHTML = `
                            <div class="text-center">
                                <div class="loading-shimmer w-16 h-16 rounded-full mb-4 mx-auto"></div>
                                <p class="text-gray-600">جاري التحميل...</p>
                            </div>
                        `;
                        document.body.appendChild(loader);

                        setTimeout(() => {
                            if (document.body.contains(loader)) {
                                loader.remove();
                            }
                        }, 5000);
                    }
                });
            });

        });

        // Global function for clearing filters (outside DOMContentLoaded)
        function clearFilters() {
            const searchInput = document.getElementById('searchInput');
            const roleFilter = document.getElementById('roleFilter');
            const statusFilter = document.getElementById('statusFilter');

            if (searchInput) searchInput.value = '';
            if (roleFilter) roleFilter.value = '';
            if (statusFilter) statusFilter.value = '';

            // Trigger filter update
            if (typeof window.filterUsers === 'function') {
                window.filterUsers();
            }
            if (typeof window.updateFilterIndicators === 'function') {
                window.updateFilterIndicators();
            }
            if (typeof window.updateFilterCounts === 'function') {
                window.updateFilterCounts();
            }
        }
    </script>
</body>
</html>


