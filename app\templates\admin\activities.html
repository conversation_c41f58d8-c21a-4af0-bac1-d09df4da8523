{% extends "base.html" %}

{% block title %}نشاطات النظام - CMSVS{% endblock %}

{% block content %}
<style>
/* Professional Admin Activities Styling */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    text-align: right;
    padding: 32px 24px;
    margin-bottom: 32px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: #1f2937;
}

.page-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-secondary {
    background: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-outline {
    background: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background: #f9fafb;
}

@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <header class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 20px;">
            <div style="flex: 1;">
                <h1 class="page-title">نشاطات النظام</h1>
                <p class="page-subtitle">إجمالي {{ total_activities }} نشاط - مراقبة وتتبع جميع أنشطة النظام</p>
            </div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <a href="/admin/dashboard" class="btn btn-outline">
                    <i class="fas fa-arrow-right"></i>
                    العودة للوحة التحكم
                </a>
                <button class="btn btn-secondary" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
        </div>
    </header>

    <!-- Filters -->
    <div class="card">
        <div class="card-body">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Activity Type Filter -->
                <div>
                    <label for="activity_type" class="block text-sm font-medium text-gray-700 mb-2">نوع النشاط</label>
                    <select name="activity_type" id="activity_type" class="form-input">
                        <option value="">جميع الأنشطة</option>
                        {% for activity_type in activity_types %}
                        <option value="{{ activity_type }}" {% if current_activity_type == activity_type %}selected{% endif %}>
                            {% if activity_type == 'login' %}تسجيل الدخول ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'logout' %}تسجيل الخروج ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'request_created' %}إنشاء طلب ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'request_updated' %}تحديث طلب ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'request_completed' %}إكمال طلب ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'request_rejected' %}رفض طلب ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'file_uploaded' %}رفع ملف ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'file_deleted' %}حذف ملف ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'profile_updated' %}تحديث الملف الشخصي ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'avatar_uploaded' %}رفع صورة شخصية ({{ activity_type_counts.get(activity_type, 0) }})
                            {% elif activity_type == 'password_changed' %}تغيير كلمة المرور ({{ activity_type_counts.get(activity_type, 0) }})
                            {% else %}{{ activity_type }} ({{ activity_type_counts.get(activity_type, 0) }})
                            {% endif %}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- User Search -->
                <div>
                    <label for="user_search" class="block text-sm font-medium text-gray-700 mb-2">البحث عن مستخدم</label>
                    <input type="text" name="user_search" id="user_search" value="{{ current_user_search or '' }}"
                           placeholder="اسم المستخدم أو البريد الإلكتروني..." class="form-input">
                </div>

                <!-- Items per page -->
                <div>
                    <label for="per_page" class="block text-sm font-medium text-gray-700 mb-2">عدد العناصر في الصفحة</label>
                    <select name="per_page" id="per_page" class="form-input">
                        <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                        <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                        <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>

                <!-- Hidden page field -->
                <input type="hidden" name="page" value="1">

                <!-- Filter buttons -->
                <div class="md:col-span-3 flex space-x-3 rtl:space-x-reverse">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-search mr-2"></i>
                        تطبيق الفلاتر
                    </button>
                    <a href="/admin/activities" class="btn-secondary">
                        <i class="fas fa-times mr-2"></i>
                        إزالة الفلاتر
                    </a>
                    <button type="button" onclick="seedTestData()" class="btn btn-info">
                        <i class="fas fa-database mr-2"></i>
                        إنشاء بيانات تجريبية
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Error Message -->
    {% if error %}
    <div class="card border-red-200 bg-red-50">
        <div class="card-body">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-red-800">خطأ في تحميل البيانات</h3>
                    <p class="text-red-600 mt-1">{{ error }}</p>
                </div>
            </div>
            <div class="mt-4">
                <button onclick="location.reload()" class="btn-primary">
                    <i class="fas fa-refresh mr-2"></i>
                    إعادة المحاولة
                </button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Activities Table -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">سجل النشاطات</h3>
                <div class="flex items-center space-x-2 space-x-reverse text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                    <i class="fas fa-clock"></i>
                    <span>توقيت البحرين (UTC+3)</span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            {% if activities %}
            <div class="overflow-x-auto">
                <table class="table">
                    <thead class="table-header">
                        <tr>
                            <th class="table-header-cell">النوع</th>
                            <th class="table-header-cell">المستخدم</th>
                            <th class="table-header-cell">الوصف</th>
                            <th class="table-header-cell">عنوان IP</th>
                            <th class="table-header-cell">التاريخ والوقت (البحرين)</th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        {% for activity in activities %}
                        <tr>
                            <td class="table-cell">
                                {% if activity.activity_type.value == 'LOGIN' %}
                                <span class="badge-info">
                                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                    </svg>
                                    دخول
                                </span>
                                {% elif activity.activity_type.value == 'LOGOUT' %}
                                <span class="badge-gray">
                                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    خروج
                                </span>
                                {% elif activity.activity_type.value == 'REQUEST_CREATED' %}
                                <span class="badge-success">
                                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    طلب جديد
                                </span>
                                {% elif activity.activity_type.value == 'REQUEST_UPDATED' %}
                                <span class="badge-warning">
                                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    تحديث طلب
                                </span>
                                {% elif activity.activity_type.value == 'FILE_UPLOADED' %}
                                <span class="badge-info">
                                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    رفع ملف
                                </span>
                                {% elif activity.activity_type.value == 'FILE_DELETED' %}
                                <span class="badge-danger">
                                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    حذف ملف
                                </span>
                                {% elif activity.activity_type.value == 'PROFILE_UPDATED' %}
                                <span class="badge-warning">
                                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    تحديث الملف
                                </span>
                                {% elif activity.activity_type.value == 'PASSWORD_CHANGED' %}
                                <span class="badge-warning">
                                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                    تغيير كلمة المرور
                                </span>
                                {% else %}
                                <span class="badge-gray">
                                    {{ activity.activity_type.value }}
                                </span>
                                {% endif %}
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium ml-3">
                                        م
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            مستخدم رقم {{ activity.user_id }}
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            معرف المستخدم: {{ activity.user_id }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="table-cell">
                                <div class="max-w-xs">
                                    {{ activity.description }}
                                </div>
                            </td>
                            <td class="table-cell">
                                {% if activity.ip_address %}
                                <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ activity.ip_address }}</code>
                                {% else %}
                                <span class="text-gray-400 text-sm">غير محدد</span>
                                {% endif %}
                            </td>
                            <td class="table-cell">
                                {% set bahrain_time = utc_to_bahrain(activity.created_at) %}
                                <div class="text-sm text-gray-900">
                                    {{ bahrain_time.strftime('%Y-%m-%d') if bahrain_time else 'غير محدد' }}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ bahrain_time.strftime('%H:%M:%S') if bahrain_time else 'غير محدد' }}
                                </div>
                                <div class="text-xs text-blue-600 mt-1">
                                    {% if bahrain_time and now_bahrain %}
                                        {% set time_diff = (now_bahrain() - bahrain_time).total_seconds() %}
                                        {% if time_diff < 60 %}
                                            منذ ثوانٍ قليلة
                                        {% elif time_diff < 3600 %}
                                            منذ {{ (time_diff / 60)|int }} دقيقة
                                        {% elif time_diff < 86400 %}
                                            منذ {{ (time_diff / 3600)|int }} ساعة
                                        {% else %}
                                            منذ {{ (time_diff / 86400)|int }} يوم
                                        {% endif %}
                                    {% else %}
                                        توقيت البحرين
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h4 class="mt-4 text-lg font-medium text-gray-900">لا توجد نشاطات</h4>
                <p class="mt-2 text-sm text-gray-600">لم يتم تسجيل أي نشاطات في النظام حتى الآن</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Pagination -->
    {% if total_pages and total_pages > 1 %}
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <!-- Page Info -->
                <div class="text-sm text-gray-600">
                    <span class="font-medium">عرض {{ activities|length }} من أصل {{ total_activities }} نشاط</span>
                    <span class="text-gray-400 mx-2">•</span>
                    <span>الصفحة {{ current_page }} من {{ total_pages }}</span>
                </div>

                <!-- Pagination Controls -->
                <nav class="flex items-center space-x-1 rtl:space-x-reverse">
                    <!-- Previous Page -->
                    {% if has_prev %}
                    <a href="/admin/activities?page={{ current_page - 1 }}{% if current_activity_type %}&activity_type={{ current_activity_type }}{% endif %}{% if current_user_search %}&user_search={{ current_user_search }}{% endif %}&per_page={{ per_page }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700">
                        <i class="fas fa-chevron-right mr-1"></i>
                        السابق
                    </a>
                    {% else %}
                    <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-200 rounded-lg cursor-not-allowed">
                        <i class="fas fa-chevron-right mr-1"></i>
                        السابق
                    </span>
                    {% endif %}

                    <!-- Page Numbers -->
                    <div class="flex items-center space-x-1 rtl:space-x-reverse mx-4">
                        {% for page_num in range(1, total_pages + 1) %}
                            {% if page_num == current_page %}
                            <span class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-lg">
                                {{ page_num }}
                            </span>
                            {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                            <a href="/admin/activities?page={{ page_num }}{% if current_activity_type %}&activity_type={{ current_activity_type }}{% endif %}{% if current_user_search %}&user_search={{ current_user_search }}{% endif %}&per_page={{ per_page }}"
                               class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700">
                                {{ page_num }}
                            </a>
                            {% elif page_num == 4 and current_page > 5 %}
                            <span class="inline-flex items-center px-2 py-2 text-sm font-medium text-gray-400">...</span>
                            {% elif page_num == total_pages - 3 and current_page < total_pages - 4 %}
                            <span class="inline-flex items-center px-2 py-2 text-sm font-medium text-gray-400">...</span>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <!-- Next Page -->
                    {% if has_next %}
                    <a href="/admin/activities?page={{ current_page + 1 }}{% if current_activity_type %}&activity_type={{ current_activity_type }}{% endif %}{% if current_user_search %}&user_search={{ current_user_search }}{% endif %}&per_page={{ per_page }}"
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700">
                        التالي
                        <i class="fas fa-chevron-left ml-1"></i>
                    </a>
                    {% else %}
                    <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-200 rounded-lg cursor-not-allowed">
                        التالي
                        <i class="fas fa-chevron-left ml-1"></i>
                    </span>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const activityTypeSelect = document.getElementById('activity_type');
    const perPageSelect = document.getElementById('per_page');

    if (activityTypeSelect) {
        activityTypeSelect.addEventListener('change', function() {
            // Reset to page 1 when filter changes
            const pageInput = document.querySelector('input[name="page"]');
            if (pageInput) {
                pageInput.value = '1';
            }
            this.form.submit();
        });
    }

    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            // Reset to page 1 when per_page changes
            const pageInput = document.querySelector('input[name="page"]');
            if (pageInput) {
                pageInput.value = '1';
            }
            this.form.submit();
        });
    }

    // Search input with debounce
    const userSearchInput = document.getElementById('user_search');
    if (userSearchInput) {
        let searchTimeout;
        userSearchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3 || this.value.length === 0) {
                    document.querySelector('input[name="page"]').value = '1';
                    this.form.submit();
                }
            }, 500);
        });
    }
});

// Function to seed test data
async function seedTestData() {
    if (!confirm('هل تريد إنشاء 50 نشاط تجريبي؟ سيتم إضافة بيانات وهمية للاختبار.')) {
        return;
    }

    try {
        const response = await fetch('/admin/seed-activities', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('خطأ: ' + (result.error || 'فشل في إنشاء البيانات التجريبية'));
        }
    } catch (error) {
        alert('خطأ في الاتصال: ' + error.message);
    }
}
</script>

{% endblock %}