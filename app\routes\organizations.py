from fastapi import APIRouter, Depends, HTTPException, Request, Form, UploadFile, File as FastAPIFile, Query
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse, FileResponse
from sqlalchemy.orm import Session
from typing import Optional, List
import logging
import os
from datetime import datetime

from app.database import get_db
from app.models.user import User, UserRole
from app.models.organization import Organization, Application
from app.services.organization_service import OrganizationService, ApplicationService
from app.services.activity_service import ActivityService
# Authentication handled by require_admin_cookie function below
from app.utils.templates import templates

router = APIRouter(prefix="/organizations")
logger = logging.getLogger(__name__)


async def require_admin_cookie(request: Request, db: Session = Depends(get_db)) -> User:
    """Require admin role using cookie authentication"""
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=403, detail="Not authenticated")

    # Remove 'Bearer ' prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    from app.utils.auth import verify_token
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=403, detail="Invalid token")

    username = payload.get("sub")
    if not username:
        raise HTTPException(status_code=403, detail="Invalid token payload")

    from app.services.user_service import UserService
    user = UserService.get_user_by_username(db, username)
    if not user:
        raise HTTPException(status_code=403, detail="User not found")

    if user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")

    return user


@router.get("/", response_class=HTMLResponse)
async def list_organizations(
    request: Request,
    search: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=10, le=100),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """List all organizations with search and pagination"""
    try:
        # Calculate pagination
        skip = (page - 1) * per_page
        
        # Get organizations
        if search and search.strip():
            organizations = OrganizationService.search_organizations(
                db, search.strip(), skip=skip, limit=per_page
            )
        else:
            organizations = OrganizationService.get_all_organizations(
                db, skip=skip, limit=per_page
            )
        
        # Calculate pagination info
        total_count = len(OrganizationService.get_all_organizations(db, limit=1000))
        total_pages = (total_count + per_page - 1) // per_page
        has_next = page < total_pages
        has_prev = page > 1
        
        return templates.TemplateResponse(
            "organizations/list.html",
            {
                "request": request,
                "current_user": current_user,
                "organizations": organizations,
                "search": search or "",
                "page": page,
                "per_page": per_page,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev,
                "total_count": total_count
            }
        )
    except Exception as e:
        logger.error(f"Error listing organizations: {str(e)}")
        return templates.TemplateResponse(
            "errors/500.html",
            {"request": request, "current_user": current_user},
            status_code=500
        )


@router.get("/new", response_class=HTMLResponse)
async def new_organization_form(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Display form to create new organization"""
    return templates.TemplateResponse(
        "organizations/new.html",
        {
            "request": request,
            "current_user": current_user
        }
    )


@router.post("/new")
async def create_organization(
    request: Request,
    name: str = Form(...),
    building: str = Form(...),
    street: str = Form(...),
    block: str = Form(...),
    description: Optional[str] = Form(None),
    contact_person: Optional[str] = Form(None),
    contact_phone: Optional[str] = Form(None),
    contact_email: Optional[str] = Form(None),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Create new organization"""
    try:
        # Validate required fields
        if not name.strip():
            return templates.TemplateResponse(
                "organizations/new.html",
                {
                    "request": request,
                    "current_user": current_user,
                    "error": "اسم المؤسسة مطلوب",
                    "form_data": {
                        "name": name,
                        "building": building,
                        "street": street,
                        "block": block,
                        "description": description,
                        "contact_person": contact_person,
                        "contact_phone": contact_phone,
                        "contact_email": contact_email
                    }
                },
                status_code=400
            )
        
        # Create organization
        organization = OrganizationService.create_organization(
            db=db,
            name=name,
            building=building,
            street=street,
            block=block,
            description=description,
            contact_person=contact_person,
            contact_phone=contact_phone,
            contact_email=contact_email
        )

        # Log activity (with error handling)
        try:
            ActivityService.log_activity(
                db=db,
                user_id=current_user.id,
                activity_type="organization_created",
                description=f"تم إنشاء مؤسسة جديدة: {organization.name}",
                metadata={"organization_id": organization.id, "organization_name": organization.name}
            )
        except Exception as activity_error:
            logger.warning(f"Failed to log activity for organization creation: {str(activity_error)}")
            # Continue with redirect even if activity logging fails

        # Redirect to organization detail or list
        return RedirectResponse(url=f"/organizations/{organization.id}", status_code=303)
        
    except HTTPException as e:
        return templates.TemplateResponse(
            "organizations/new.html",
            {
                "request": request,
                "current_user": current_user,
                "error": e.detail,
                "form_data": {
                    "name": name,
                    "building": building,
                    "street": street,
                    "block": block,
                    "description": description,
                    "contact_person": contact_person,
                    "contact_phone": contact_phone,
                    "contact_email": contact_email
                }
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error creating organization: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return templates.TemplateResponse(
            "organizations/new.html",
            {
                "request": request,
                "current_user": current_user,
                "error": f"حدث خطأ أثناء إنشاء المؤسسة: {str(e)}",
                "form_data": {
                    "name": name,
                    "building": building,
                    "street": street,
                    "block": block,
                    "description": description,
                    "contact_person": contact_person,
                    "contact_phone": contact_phone,
                    "contact_email": contact_email
                }
            },
            status_code=500
        )


@router.get("/{organization_id}", response_class=HTMLResponse)
async def view_organization(
    request: Request,
    organization_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """View organization details and applications"""
    try:
        # Get organization
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return templates.TemplateResponse(
                "errors/404.html",
                {"request": request, "current_user": current_user},
                status_code=404
            )
        
        # Get applications for this organization
        applications = ApplicationService.get_applications_by_organization(
            db, organization_id, limit=50
        )
        
        return templates.TemplateResponse(
            "organizations/detail.html",
            {
                "request": request,
                "current_user": current_user,
                "organization": organization,
                "applications": applications
            }
        )
    except Exception as e:
        logger.error(f"Error viewing organization {organization_id}: {str(e)}")
        return templates.TemplateResponse(
            "errors/500.html",
            {"request": request, "current_user": current_user},
            status_code=500
        )


@router.get("/{organization_id}/edit", response_class=HTMLResponse)
async def edit_organization_form(
    request: Request,
    organization_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Display form to edit organization"""
    try:
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return templates.TemplateResponse(
                "errors/404.html",
                {"request": request, "current_user": current_user},
                status_code=404
            )
        
        return templates.TemplateResponse(
            "organizations/edit.html",
            {
                "request": request,
                "current_user": current_user,
                "organization": organization
            }
        )
    except Exception as e:
        logger.error(f"Error loading edit form for organization {organization_id}: {str(e)}")
        return templates.TemplateResponse(
            "errors/500.html",
            {"request": request, "current_user": current_user},
            status_code=500
        )


@router.post("/{organization_id}/edit")
async def update_organization(
    request: Request,
    organization_id: int,
    name: str = Form(...),
    building: str = Form(...),
    street: str = Form(...),
    block: str = Form(...),
    description: Optional[str] = Form(None),
    contact_person: Optional[str] = Form(None),
    contact_phone: Optional[str] = Form(None),
    contact_email: Optional[str] = Form(None),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Update organization"""
    try:
        # Get organization first
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return templates.TemplateResponse(
                "errors/404.html",
                {"request": request, "current_user": current_user},
                status_code=404
            )

        # Validate required fields
        if not name.strip():
            return templates.TemplateResponse(
                "organizations/edit.html",
                {
                    "request": request,
                    "current_user": current_user,
                    "organization": organization,
                    "error": "اسم المؤسسة مطلوب"
                },
                status_code=400
            )

        # Update organization
        updated_organization = OrganizationService.update_organization(
            db=db,
            organization_id=organization_id,
            name=name,
            building=building,
            street=street,
            block=block,
            description=description,
            contact_person=contact_person,
            contact_phone=contact_phone,
            contact_email=contact_email
        )

        # Log activity
        ActivityService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type="organization_updated",
            description=f"تم تحديث بيانات المؤسسة: {updated_organization.name}",
            metadata={"organization_id": updated_organization.id, "organization_name": updated_organization.name}
        )

        # Redirect to organization detail
        return RedirectResponse(url=f"/organizations/{organization_id}", status_code=303)

    except HTTPException as e:
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        return templates.TemplateResponse(
            "organizations/edit.html",
            {
                "request": request,
                "current_user": current_user,
                "organization": organization,
                "error": e.detail
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error updating organization {organization_id}: {str(e)}")
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        return templates.TemplateResponse(
            "organizations/edit.html",
            {
                "request": request,
                "current_user": current_user,
                "organization": organization,
                "error": "حدث خطأ أثناء تحديث المؤسسة"
            },
            status_code=500
        )


@router.post("/{organization_id}/delete")
async def delete_organization(
    request: Request,
    organization_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Delete organization (soft delete)"""
    try:
        # Get organization first
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "المؤسسة غير موجودة"}
            )

        # Delete organization
        OrganizationService.delete_organization(db, organization_id)

        # Log activity
        ActivityService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type="organization_deleted",
            description=f"تم حذف المؤسسة: {organization.name}",
            metadata={"organization_id": organization.id, "organization_name": organization.name}
        )

        return JSONResponse(
            status_code=200,
            content={"success": True, "message": "تم حذف المؤسسة بنجاح"}
        )

    except HTTPException as e:
        return JSONResponse(
            status_code=400,
            content={"success": False, "error": e.detail}
        )
    except Exception as e:
        logger.error(f"Error deleting organization {organization_id}: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": "حدث خطأ أثناء حذف المؤسسة"}
        )


# Application routes
@router.get("/{organization_id}/applications/new", response_class=HTMLResponse)
async def new_application_form(
    request: Request,
    organization_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Display form to create new application"""
    try:
        # Get organization
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return templates.TemplateResponse(
                "errors/404.html",
                {"request": request, "current_user": current_user},
                status_code=404
            )

        return templates.TemplateResponse(
            "organizations/applications/new.html",
            {
                "request": request,
                "current_user": current_user,
                "organization": organization
            }
        )
    except Exception as e:
        logger.error(f"Error loading new application form: {str(e)}")
        return templates.TemplateResponse(
            "errors/500.html",
            {"request": request, "current_user": current_user},
            status_code=500
        )


@router.post("/{organization_id}/applications/new")
async def create_application(
    request: Request,
    organization_id: int,
    name: str = Form(...),
    mobile: str = Form(...),
    benayat: Optional[str] = Form(None),
    notes: Optional[str] = Form(None),
    attachment1: Optional[UploadFile] = FastAPIFile(None),
    attachment2: Optional[UploadFile] = FastAPIFile(None),
    attachment3: Optional[UploadFile] = FastAPIFile(None),
    attachment4: Optional[UploadFile] = FastAPIFile(None),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Create new application with file uploads"""
    try:
        # Get organization
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return templates.TemplateResponse(
                "errors/404.html",
                {"request": request, "current_user": current_user},
                status_code=404
            )

        # Validate required fields
        if not name.strip():
            return templates.TemplateResponse(
                "organizations/applications/new.html",
                {
                    "request": request,
                    "current_user": current_user,
                    "organization": organization,
                    "error": "اسم مقدم الطلب مطلوب",
                    "form_data": {
                        "name": name,
                        "mobile": mobile,
                        "benayat": benayat,
                        "notes": notes
                    }
                },
                status_code=400
            )

        if not mobile.strip():
            return templates.TemplateResponse(
                "organizations/applications/new.html",
                {
                    "request": request,
                    "current_user": current_user,
                    "organization": organization,
                    "error": "رقم الهاتف مطلوب",
                    "form_data": {
                        "name": name,
                        "mobile": mobile,
                        "benayat": benayat,
                        "notes": notes
                    }
                },
                status_code=400
            )

        # Create application
        application = ApplicationService.create_application(
            db=db,
            organization_id=organization_id,
            name=name,
            mobile=mobile,
            benayat=benayat,
            notes=notes
        )

        # Handle file uploads
        attachments = [attachment1, attachment2, attachment3, attachment4]
        for i, attachment in enumerate(attachments, 1):
            if attachment and attachment.filename:
                try:
                    ApplicationService.save_application_file(
                        db=db,
                        application_id=application.id,
                        attachment_index=i,
                        file=attachment
                    )
                except HTTPException as e:
                    logger.warning(f"Failed to save attachment {i}: {e.detail}")
                    # Continue with other files, don't fail the entire application

        # Log activity
        ActivityService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type="application_created",
            description=f"تم إنشاء طلب جديد: {application.application_id} للمؤسسة: {organization.name}",
            metadata={
                "application_id": application.id,
                "application_number": application.application_id,
                "organization_id": organization.id,
                "organization_name": organization.name
            }
        )

        # Redirect to application detail
        return RedirectResponse(url=f"/organizations/{organization_id}/applications/{application.id}", status_code=303)

    except HTTPException as e:
        return templates.TemplateResponse(
            "organizations/applications/new.html",
            {
                "request": request,
                "current_user": current_user,
                "organization": organization,
                "error": e.detail,
                "form_data": {
                    "name": name,
                    "mobile": mobile,
                    "benayat": benayat,
                    "notes": notes
                }
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error creating application: {str(e)}")
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        return templates.TemplateResponse(
            "organizations/applications/new.html",
            {
                "request": request,
                "current_user": current_user,
                "organization": organization,
                "error": "حدث خطأ أثناء إنشاء الطلب",
                "form_data": {
                    "name": name,
                    "mobile": mobile,
                    "benayat": benayat,
                    "notes": notes
                }
            },
            status_code=500
        )


@router.get("/{organization_id}/applications/{application_id}", response_class=HTMLResponse)
async def view_application(
    request: Request,
    organization_id: int,
    application_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """View application details"""
    try:
        # Get organization
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return templates.TemplateResponse(
                "errors/404.html",
                {"request": request, "current_user": current_user},
                status_code=404
            )

        # Get application
        application = ApplicationService.get_application_by_id(db, application_id)
        if not application or application.organization_id != organization_id:
            return templates.TemplateResponse(
                "errors/404.html",
                {"request": request, "current_user": current_user},
                status_code=404
            )

        return templates.TemplateResponse(
            "organizations/applications/detail.html",
            {
                "request": request,
                "current_user": current_user,
                "organization": organization,
                "application": application
            }
        )
    except Exception as e:
        logger.error(f"Error viewing application {application_id}: {str(e)}")
        return templates.TemplateResponse(
            "errors/500.html",
            {"request": request, "current_user": current_user},
            status_code=500
        )


# API endpoints for HTMX interactions
@router.get("/api/search", response_class=JSONResponse)
async def search_organizations_api(
    search: str = Query(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """API endpoint for organization search (for HTMX)"""
    try:
        organizations = OrganizationService.search_organizations(
            db, search, limit=10
        )

        return JSONResponse({
            "success": True,
            "organizations": [org.to_dict() for org in organizations]
        })
    except Exception as e:
        logger.error(f"Error searching organizations: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": "حدث خطأ أثناء البحث"
        }, status_code=500)


# File management endpoints
@router.get("/{organization_id}/applications/{application_id}/files/{attachment_index}")
async def download_application_file(
    organization_id: int,
    application_id: int,
    attachment_index: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Download application attachment file"""
    try:
        logger.info(f"File download request: org={organization_id}, app={application_id}, attachment={attachment_index}")

        # Validate attachment index
        if attachment_index < 1 or attachment_index > 4:
            logger.error(f"Invalid attachment index: {attachment_index}")
            raise HTTPException(status_code=400, detail="Invalid attachment index")

        # Verify organization exists
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            logger.error(f"Organization not found: {organization_id}")
            raise HTTPException(status_code=404, detail="Organization not found")

        # Verify application exists and belongs to organization
        application = ApplicationService.get_application_by_id(db, application_id)
        if not application:
            logger.error(f"Application not found: {application_id}")
            raise HTTPException(status_code=404, detail="Application not found")

        if application.organization_id != organization_id:
            logger.error(f"Application {application_id} does not belong to organization {organization_id}")
            raise HTTPException(status_code=404, detail="Application not found")

        # Get file path
        path_field = f"attachment{attachment_index}_path"
        filename_field = f"attachment{attachment_index}_filename"

        file_path = getattr(application, path_field, None)
        original_filename = getattr(application, filename_field, None)

        logger.info(f"File path from DB: {file_path}")
        logger.info(f"Original filename: {original_filename}")

        if not file_path:
            logger.error(f"No file path found for attachment {attachment_index}")
            raise HTTPException(status_code=404, detail="File not found")

        if not os.path.exists(file_path):
            logger.error(f"File does not exist on disk: {file_path}")
            raise HTTPException(status_code=404, detail="File not found on disk")

        # Verify file is readable
        if not os.access(file_path, os.R_OK):
            logger.error(f"File is not readable: {file_path}")
            raise HTTPException(status_code=403, detail="File access denied")

        # Log file access (with error handling)
        try:
            ActivityService.log_activity(
                db=db,
                user_id=current_user.id,
                activity_type="file_downloaded",
                description=f"تم تحميل ملف من الطلب: {application.application_id}",
                metadata={
                    "application_id": application.id,
                    "application_number": application.application_id,
                    "organization_id": organization.id,
                    "organization_name": organization.name,
                    "attachment_index": attachment_index,
                    "filename": original_filename
                }
            )
        except Exception as activity_error:
            logger.warning(f"Failed to log file download activity: {str(activity_error)}")
            # Continue with download even if activity logging fails

        logger.info(f"Serving file: {file_path} as {original_filename}")

        return FileResponse(
            path=file_path,
            filename=original_filename or f"attachment_{attachment_index}",
            media_type='application/octet-stream'
        )

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        logger.error(f"Error downloading file: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error downloading file: {str(e)}")


@router.delete("/{organization_id}/applications/{application_id}/files/{attachment_index}")
async def delete_application_file(
    organization_id: int,
    application_id: int,
    attachment_index: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Delete application attachment file"""
    try:
        # Verify organization exists
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "المؤسسة غير موجودة"}
            )

        # Verify application exists and belongs to organization
        application = ApplicationService.get_application_by_id(db, application_id)
        if not application or application.organization_id != organization_id:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "الطلب غير موجود"}
            )

        # Delete file
        success = ApplicationService.delete_application_file(db, application_id, attachment_index)

        if success:
            # Log file deletion
            ActivityService.log_activity(
                db=db,
                user_id=current_user.id,
                activity_type="file_deleted",
                description=f"تم حذف ملف من الطلب: {application.application_id}",
                metadata={
                    "application_id": application.id,
                    "application_number": application.application_id,
                    "organization_id": organization.id,
                    "organization_name": organization.name,
                    "attachment_index": attachment_index
                }
            )

            return JSONResponse({
                "success": True,
                "message": "تم حذف الملف بنجاح"
            })
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "فشل في حذف الملف"}
            )

    except Exception as e:
        logger.error(f"Error deleting file: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": "حدث خطأ أثناء حذف الملف"}
        )


@router.post("/{organization_id}/applications/{application_id}/files/{attachment_index}")
async def upload_application_file(
    organization_id: int,
    application_id: int,
    attachment_index: int,
    file: UploadFile = FastAPIFile(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Upload or replace application attachment file"""
    try:
        # Verify organization exists
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        if not organization:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "المؤسسة غير موجودة"}
            )

        # Verify application exists and belongs to organization
        application = ApplicationService.get_application_by_id(db, application_id)
        if not application or application.organization_id != organization_id:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "الطلب غير موجود"}
            )

        # Save file
        success = ApplicationService.save_application_file(
            db=db,
            application_id=application_id,
            attachment_index=attachment_index,
            file=file
        )

        if success:
            # Log file upload
            ActivityService.log_activity(
                db=db,
                user_id=current_user.id,
                activity_type="file_uploaded",
                description=f"تم رفع ملف للطلب: {application.application_id}",
                metadata={
                    "application_id": application.id,
                    "application_number": application.application_id,
                    "organization_id": organization.id,
                    "organization_name": organization.name,
                    "attachment_index": attachment_index,
                    "filename": file.filename
                }
            )

            return JSONResponse({
                "success": True,
                "message": "تم رفع الملف بنجاح",
                "filename": file.filename
            })
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "فشل في رفع الملف"}
            )

    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code,
            content={"success": False, "error": e.detail}
        )
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": "حدث خطأ أثناء رفع الملف"}
        )


# Debug route to test organization creation
@router.get("/debug/test-creation", response_class=JSONResponse)
async def debug_test_creation(
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Debug endpoint to test organization creation"""
    try:
        # Test creating a simple organization
        test_org = OrganizationService.create_organization(
            db=db,
            name=f"Debug Test Org {datetime.now().strftime('%Y%m%d%H%M%S')}",
            building="Debug Building",
            street="Debug Street",
            block="Debug Block"
        )

        # Test activity logging
        try:
            ActivityService.log_activity(
                db=db,
                user_id=current_user.id,
                activity_type="organization_created",
                description=f"Debug test: {test_org.name}",
                metadata={"organization_id": test_org.id, "organization_name": test_org.name}
            )
            activity_status = "success"
        except Exception as activity_error:
            activity_status = f"failed: {str(activity_error)}"

        # Clean up
        db.delete(test_org)
        db.commit()

        return JSONResponse({
            "success": True,
            "message": "Organization creation test successful",
            "organization_id": test_org.id,
            "organization_name": test_org.name,
            "activity_logging": activity_status
        })

    except Exception as e:
        import traceback
        return JSONResponse({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }, status_code=500)


# Debug route to test file download
@router.get("/debug/file-info/{organization_id}/applications/{application_id}/files/{attachment_index}")
async def debug_file_info(
    organization_id: int,
    application_id: int,
    attachment_index: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Debug endpoint to check file information"""
    try:
        debug_info = {
            "organization_id": organization_id,
            "application_id": application_id,
            "attachment_index": attachment_index
        }

        # Check organization
        organization = OrganizationService.get_organization_by_id(db, organization_id)
        debug_info["organization_exists"] = organization is not None
        if organization:
            debug_info["organization_name"] = organization.name

        # Check application
        application = ApplicationService.get_application_by_id(db, application_id)
        debug_info["application_exists"] = application is not None
        if application:
            debug_info["application_id_string"] = application.application_id
            debug_info["application_belongs_to_org"] = application.organization_id == organization_id

            # Check file path
            path_field = f"attachment{attachment_index}_path"
            filename_field = f"attachment{attachment_index}_filename"

            file_path = getattr(application, path_field, None)
            filename = getattr(application, filename_field, None)

            debug_info["file_path"] = file_path
            debug_info["filename"] = filename
            debug_info["file_exists_in_db"] = file_path is not None

            if file_path:
                debug_info["file_exists_on_disk"] = os.path.exists(file_path)
                if os.path.exists(file_path):
                    debug_info["file_size"] = os.path.getsize(file_path)
                    debug_info["file_readable"] = os.access(file_path, os.R_OK)
                else:
                    debug_info["file_size"] = None
                    debug_info["file_readable"] = False
            else:
                debug_info["file_exists_on_disk"] = False
                debug_info["file_size"] = None
                debug_info["file_readable"] = False

        return JSONResponse({
            "success": True,
            "debug_info": debug_info
        })

    except Exception as e:
        import traceback
        return JSONResponse({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }, status_code=500)


# Enhanced search endpoints
@router.get("/search", response_class=HTMLResponse)
async def search_page(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Dedicated search page for organizations and applications"""
    return templates.TemplateResponse(
        "organizations/search.html",
        {
            "request": request,
            "current_user": current_user
        }
    )


@router.get("/api/search/organizations", response_class=JSONResponse)
async def search_organizations_detailed(
    search: str = Query(..., min_length=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Enhanced API endpoint for organization search with applications count"""
    try:
        organizations = OrganizationService.search_organizations(
            db, search, limit=limit
        )

        results = []
        for org in organizations:
            org_data = org.to_dict()
            # Add recent applications
            recent_apps = ApplicationService.get_applications_by_organization(
                db, org.id, limit=3
            )
            org_data['recent_applications'] = [app.to_dict() for app in recent_apps]
            results.append(org_data)

        return JSONResponse({
            "success": True,
            "organizations": results,
            "total": len(results)
        })
    except Exception as e:
        logger.error(f"Error searching organizations: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": "حدث خطأ أثناء البحث"
        }, status_code=500)


@router.get("/api/search/applications", response_class=JSONResponse)
async def search_applications(
    search: str = Query(..., min_length=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Search applications by name, mobile, or application ID"""
    try:
        # Search applications by name, mobile, or application ID
        search_pattern = f"%{search.strip()}%"

        applications = db.query(Application).filter(
            and_(
                Application.is_active == True,
                or_(
                    Application.name.ilike(search_pattern),
                    Application.mobile.ilike(search_pattern),
                    Application.application_id.ilike(search_pattern),
                    Application.benayat.ilike(search_pattern)
                )
            )
        ).limit(limit).all()

        results = []
        for app in applications:
            app_data = app.to_dict()
            results.append(app_data)

        return JSONResponse({
            "success": True,
            "applications": results,
            "total": len(results)
        })
    except Exception as e:
        logger.error(f"Error searching applications: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": "حدث خطأ أثناء البحث"
        }, status_code=500)


@router.get("/api/search/global", response_class=JSONResponse)
async def global_search(
    search: str = Query(..., min_length=1),
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Global search across organizations and applications"""
    try:
        # Search organizations
        organizations = OrganizationService.search_organizations(
            db, search, limit=limit//2
        )

        # Search applications
        search_pattern = f"%{search.strip()}%"
        applications = db.query(Application).filter(
            and_(
                Application.is_active == True,
                or_(
                    Application.name.ilike(search_pattern),
                    Application.mobile.ilike(search_pattern),
                    Application.application_id.ilike(search_pattern),
                    Application.benayat.ilike(search_pattern)
                )
            )
        ).limit(limit//2).all()

        return JSONResponse({
            "success": True,
            "organizations": [org.to_dict() for org in organizations],
            "applications": [app.to_dict() for app in applications],
            "total_organizations": len(organizations),
            "total_applications": len(applications)
        })
    except Exception as e:
        logger.error(f"Error in global search: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": "حدث خطأ أثناء البحث"
        }, status_code=500)
