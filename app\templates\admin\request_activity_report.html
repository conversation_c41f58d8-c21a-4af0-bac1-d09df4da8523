{% extends "base.html" %}

{% block title %}تقرير أنشطة الطلبات - لوحة التحكم{% endblock %}

{% block content %}
<style>
/* Request Activity Report Styling */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
    border-radius: 12px;
}

.report-title {
    text-align: right;
    order: 1;
}

.report-title h1 {
    font-size: 2.25rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 8px;
    line-height: 1.2;
}

.report-title p {
    font-size: 1rem;
    color: #64748b;
    line-height: 1.5;
}

.report-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    order: 2;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s;
    min-width: 200px;
    text-align: right;
    text-decoration: none;
}

.action-btn.primary {
    background-color: #3b82f6;
    color: white;
}

.action-btn.primary:hover {
    background-color: #2563eb;
    color: white;
    text-decoration: none;
}

.action-btn.secondary {
    background-color: #64748b;
    color: white;
}

.action-btn.secondary:hover {
    background-color: #475569;
    color: white;
    text-decoration: none;
}

.action-btn i {
    margin-right: 8px;
}

/* Report Configuration Section */
.report-config {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    padding: 24px;
}

.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.config-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-title i {
    color: #10b981;
}

.config-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
}

.form-control {
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    background: #ffffff;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn {
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover {
    background: #2563eb;
    color: #ffffff;
    text-decoration: none;
}

.btn-secondary {
    background: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background: #4b5563;
    color: #ffffff;
    text-decoration: none;
}

.btn-success {
    background: #10b981;
    color: #ffffff;
}

.btn-success:hover {
    background: #059669;
    color: #ffffff;
    text-decoration: none;
}

.btn-danger {
    background: #ef4444;
    color: #ffffff;
}

.btn-danger:hover {
    background: #dc2626;
    color: #ffffff;
    text-decoration: none;
}

/* Report Summary */
.report-summary {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    padding: 24px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.summary-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.summary-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

/* Export Options */
.export-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    padding: 24px;
}

.export-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        gap: 20px;
    }

    .report-title {
        text-align: center;
        order: 1;
        padding: 0;
    }

    .report-title h1 {
        font-size: 2rem;
        margin-bottom: 4px;
    }

    .report-actions {
        order: 2;
        width: 100%;
        align-items: stretch;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
        padding: 14px;
    }

    .config-form {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .export-buttons {
        justify-content: center;
    }

    .table-container {
        overflow-x: auto;
    }

    .requests-table {
        font-size: 12px;
    }

    .requests-table th,
    .requests-table td {
        padding: 8px 4px;
    }

    .status-badge {
        font-size: 10px;
        min-width: 60px;
    }
}

/* Detailed Requests Table */
.report-details {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-container {
    overflow-x: auto;
    margin-top: 16px;
}

.requests-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.requests-table th,
.requests-table td {
    padding: 12px 8px;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
}

.requests-table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
    position: sticky;
    top: 0;
}

.requests-table tbody tr:hover {
    background-color: #f9fafb;
}

.request-number {
    font-weight: 600;
    color: #1f2937;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-مكتمل {
    background-color: #d1fae5;
    color: #065f46;
}

.status-قيد-الانتظار {
    background-color: #fef3c7;
    color: #92400e;
}

.status-قيد-المعالجة {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-مرفوض {
    background-color: #fee2e2;
    color: #991b1b;
}

.activity-count,
.file-count {
    text-align: center;
    font-weight: 600;
}

/* Charts Section */
.charts-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-top: 16px;
}

@media (max-width: 768px) {
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
}

.chart-container {
    text-align: center;
    min-height: 350px;
    position: relative;
    background: #f9fafb;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e5e7eb;
}

.chart-container h3 {
    margin-bottom: 20px;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #e5e7eb;
}

/* Prevent chart resize loops */
.chart-container canvas {
    max-width: 100% !important;
    height: auto !important;
}

/* Error and No Data Messages */
.error-message,
.no-data-message {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.error-content,
.no-data-content {
    margin-top: 16px;
}

.error-content p,
.no-data-content p {
    margin-bottom: 8px;
    color: #6b7280;
    line-height: 1.6;
}

.error-message .config-title {
    color: #dc2626;
}

.no-data-message .config-title {
    color: #2563eb;
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <header class="page-header">
        <div class="report-title">
            <h1>تقرير أنشطة الطلبات</h1>
            <p>تقرير شامل لأنشطة الطلبات لفترة {{ period }} أشهر</p>
        </div>
        <div class="report-actions">
            <a href="/admin/requests-records" class="action-btn secondary">
                <i class="fas fa-arrow-right"></i>
                <span>العودة لسجل الطلبات</span>
            </a>
        </div>
    </header>

    <!-- Report Configuration -->
    <div class="report-config">
        <div class="config-header">
            <h2 class="config-title">
                <i class="fas fa-cog"></i>
                إعدادات التقرير
            </h2>
        </div>
        <form method="get" action="/admin/request-activity-report">
            <div class="config-form">
                <div class="form-group">
                    <label for="period" class="form-label">فترة التقرير (بالأشهر)</label>
                    <select id="period" name="period" class="form-control">
                        <option value="1" {% if period == 1 %}selected{% endif %}>شهر واحد</option>
                        <option value="3" {% if period == 3 %}selected{% endif %}>3 أشهر</option>
                        <option value="6" {% if period == 6 %}selected{% endif %}>6 أشهر</option>
                        <option value="12" {% if period == 12 %}selected{% endif %}>سنة واحدة</option>
                        <option value="24" {% if period == 24 %}selected{% endif %}>سنتان</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="format" class="form-label">تنسيق التقرير</label>
                    <select id="format" name="format" class="form-control">
                        <option value="html">عرض على الشاشة</option>
                        <option value="csv">ملف CSV</option>
                        <option value="pdf">ملف PDF</option>
                        <option value="arabic">نسخة للطباعة</option>
                    </select>
                </div>
            </div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i>
                    تحديث التقرير
                </button>
                <a href="/admin/requests-records" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>

    {% if report_data %}
    {% if report_data.error %}
    <!-- Error Message -->
    <div class="error-message">
        <div class="config-header">
            <h2 class="config-title">
                <i class="fas fa-exclamation-triangle"></i>
                خطأ في إنشاء التقرير
            </h2>
        </div>
        <div class="error-content">
            <p>حدث خطأ أثناء إنشاء التقرير: {{ report_data.error }}</p>
            <p>يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.</p>
        </div>
    </div>
    {% elif report_data.summary.total_requests == 0 %}
    <!-- No Data Message -->
    <div class="no-data-message">
        <div class="config-header">
            <h2 class="config-title">
                <i class="fas fa-info-circle"></i>
                لا توجد بيانات
            </h2>
        </div>
        <div class="no-data-content">
            <p>لا توجد طلبات في الفترة المحددة ({{ period }} أشهر).</p>
            <p>جرب فترة زمنية أطول أو تأكد من وجود طلبات في النظام.</p>
        </div>
    </div>
    {% else %}
    <!-- Report Summary -->
    <div class="report-summary">
        <div class="config-header">
            <h2 class="config-title">
                <i class="fas fa-chart-bar"></i>
                ملخص التقرير
            </h2>
        </div>
        <div class="summary-grid">
            <div class="summary-card">
                <div class="summary-value">{{ report_data.summary.total_requests }}</div>
                <div class="summary-label">إجمالي الطلبات</div>
            </div>
            <div class="summary-card">
                <div class="summary-value">{{ report_data.summary.completed_requests }}</div>
                <div class="summary-label">الطلبات المكتملة</div>
            </div>
            <div class="summary-card">
                <div class="summary-value">{{ report_data.summary.pending_requests }}</div>
                <div class="summary-label">الطلبات المعلقة</div>
            </div>
            <div class="summary-card">
                <div class="summary-value">{{ report_data.summary.in_progress_requests }}</div>
                <div class="summary-label">قيد المعالجة</div>
            </div>
            <div class="summary-card">
                <div class="summary-value">{{ report_data.summary.total_activities }}</div>
                <div class="summary-label">إجمالي الأنشطة</div>
            </div>
            <div class="summary-card">
                <div class="summary-value">{{ report_data.summary.completion_rate }}%</div>
                <div class="summary-label">معدل الإنجاز</div>
            </div>
        </div>
    </div>

    <!-- Detailed Requests Table -->
    <div class="report-details">
        <div class="config-header">
            <h2 class="config-title">
                <i class="fas fa-list"></i>
                تفاصيل الطلبات
            </h2>
        </div>
        <div class="table-container">
            <table class="requests-table">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>الحالة</th>
                        <th>اسم المبنى</th>
                        <th>رقم المبنى</th>
                        <th>اسم مقدم الطلب</th>
                        <th>تاريخ الإنشاء</th>
                        <th>آخر تحديث</th>
                        <th>عدد الأنشطة</th>
                        <th>عدد الملفات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in report_data.requests %}
                    <tr>
                        <td class="request-number">{{ request.request_number }}</td>
                        <td>
                            <span class="status-badge status-{{ request.status|lower|replace(' ', '-') }}">
                                {{ request.status }}
                            </span>
                        </td>
                        <td>{{ request.building_name }}</td>
                        <td>{{ request.building_number }}</td>
                        <td>{{ request.full_name }}</td>
                        <td>{{ request.created_at }}</td>
                        <td>{{ request.updated_at }}</td>
                        <td class="activity-count">{{ request.activity_count }}</td>
                        <td class="file-count">{{ request.file_count }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
        <div class="config-header">
            <h2 class="config-title">
                <i class="fas fa-chart-pie"></i>
                الرسوم البيانية
            </h2>
        </div>
        <div class="charts-grid">
            <div class="chart-container">
                <h3>توزيع حالات الطلبات</h3>
                <div style="position: relative; height: 300px; width: 100%;">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
            <div class="chart-container">
                <h3>اتجاه الطلبات الشهرية</h3>
                <div style="position: relative; height: 300px; width: 100%;">
                    <canvas id="monthlyTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="export-section">
        <div class="config-header">
            <h2 class="config-title">
                <i class="fas fa-download"></i>
                تصدير التقرير
            </h2>
        </div>
        <div class="export-buttons">
            <a href="/admin/request-activity-report?period={{ period }}&format=csv" class="btn btn-success">
                <i class="fas fa-file-csv"></i>
                تصدير CSV
            </a>
            <a href="/admin/request-activity-report?period={{ period }}&format=pdf" class="btn btn-danger">
                <i class="fas fa-file-pdf"></i>
                تصدير PDF
            </a>
            <a href="/admin/request-activity-report?period={{ period }}&format=arabic" class="btn btn-primary" target="_blank">
                <i class="fas fa-print"></i>
                نسخة للطباعة
            </a>
        </div>
    </div>
    {% endif %}
    {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Request activity report page loaded successfully');

    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded!');
        return;
    } else {
        console.log('Chart.js is loaded successfully');
    }

    // Debug: Check if chart elements exist
    const statusElement = document.getElementById('statusChart');
    const monthlyElement = document.getElementById('monthlyTrendChart');
    console.log('Status chart element:', statusElement);
    console.log('Monthly chart element:', monthlyElement);

    // Debug: Check report data availability
    {% if report_data %}
    console.log('Report data is available');
    console.log('Total requests:', {{ report_data.requests|length }});
    {% if report_data.charts %}
    console.log('Charts data is available');
    {% else %}
    console.log('Charts data is NOT available');
    {% endif %}
    {% else %}
    console.log('Report data is NOT available');
    {% endif %}

    {% if report_data %}
    // Status Distribution Chart - Fixed to prevent infinite loops
    {% if report_data.charts %}
    const statusCtx = document.getElementById('statusChart');
    if (statusCtx) {
        // Destroy any existing chart instance to prevent conflicts
        if (window.statusChart && typeof window.statusChart.destroy === 'function') {
            window.statusChart.destroy();
        }

        // Create new chart with stable configuration
        window.statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    {% for item in report_data.charts.status_distribution %}
                    '{{ item.name }}'{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in report_data.charts.status_distribution %}
                        {{ item.value }}{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: [
                        {% for item in report_data.charts.status_distribution %}
                        '{{ item.color }}'{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 1.5,
                animation: {
                    duration: 0 // Disable animations to prevent rendering loops
                },
                interaction: {
                    intersect: false
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                family: 'Cairo, sans-serif',
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                layout: {
                    padding: 10
                }
            }
        });

        console.log('Status chart created successfully without infinite loop');
    }
    {% endif %}

    // Monthly Trend Chart - Bar Chart showing requests over time
    const monthlyTrendCtx = document.getElementById('monthlyTrendChart');
    console.log('Monthly trend chart context:', monthlyTrendCtx);

    if (monthlyTrendCtx) {
        // Destroy any existing chart instance to prevent conflicts
        if (window.monthlyTrendChart && typeof window.monthlyTrendChart.destroy === 'function') {
            window.monthlyTrendChart.destroy();
        }

        // Process monthly data from requests
        const monthlyData = {};
        console.log('Processing monthly data...');

        // Create array of request dates from server data
        const requestDates = [
            {% for request in report_data.requests %}
            '{{ request.created_at }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        // Process the dates in JavaScript
        requestDates.forEach(function(dateString) {
            const requestDate = new Date(dateString);
            const monthKey = requestDate.getFullYear() + '-' + String(requestDate.getMonth() + 1).padStart(2, '0');
            if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = 0;
            }
            monthlyData[monthKey]++;
        });

        console.log('Monthly data processed:', monthlyData);

        // Sort months and prepare data
        const sortedMonths = Object.keys(monthlyData).sort();
        console.log('Sorted months:', sortedMonths);

        if (sortedMonths.length === 0) {
            console.log('No monthly data available for chart');
            // Show a message in the chart container
            monthlyTrendCtx.getContext('2d').fillText('لا توجد بيانات للعرض', 50, 150);
            return;
        }

        const monthLabels = sortedMonths.map(month => {
            const [year, monthNum] = month.split('-');
            const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                              'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
            return monthNames[parseInt(monthNum) - 1] + ' ' + year;
        });
        const monthValues = sortedMonths.map(month => monthlyData[month]);

        console.log('Month labels:', monthLabels);
        console.log('Month values:', monthValues);

        // Create new chart with stable configuration
        window.monthlyTrendChart = new Chart(monthlyTrendCtx, {
            type: 'bar',
            data: {
                labels: monthLabels,
                datasets: [{
                    label: 'عدد الطلبات',
                    data: monthValues,
                    backgroundColor: 'rgba(59, 130, 246, 0.6)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 2,
                    borderRadius: 4,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 1.5,
                animation: {
                    duration: 0 // Disable animations to prevent rendering loops
                },
                interaction: {
                    intersect: false
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الطلبات',
                            font: {
                                family: 'Cairo, sans-serif',
                                size: 12
                            }
                        },
                        ticks: {
                            font: {
                                family: 'Cairo, sans-serif',
                                size: 10
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'الشهر',
                            font: {
                                family: 'Cairo, sans-serif',
                                size: 12
                            }
                        },
                        ticks: {
                            font: {
                                family: 'Cairo, sans-serif',
                                size: 10
                            },
                            maxRotation: 45
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            title: function(context) {
                                return 'شهر: ' + context[0].label;
                            },
                            label: function(context) {
                                return 'عدد الطلبات: ' + context.parsed.y;
                            }
                        }
                    }
                },
                layout: {
                    padding: 10
                }
            }
        });

        console.log('Monthly trend chart created successfully');
    } else {
        console.log('Monthly trend chart element not found');
    }

    {% else %}
    console.log('No report data available for charts');
    {% endif %}
});
</script>

{% endblock %}
