<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل أنشطة {{ target_user.full_name or target_user.username }} - لوحة التحكم</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Modern Arabic font stack */
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif, 'Arabic UI Text', 'Geeza Pro', 'Traditional Arabic', 'Simplified Arabic';
            background: #f8fafc;
            min-height: 100vh;
        }

        /* Page Container */
        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* Page Header */
        .page-header {
            text-align: right;
            padding: 32px 24px;
            margin-bottom: 32px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            margin: 0 0 8px 0;
            color: #1f2937;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6b7280;
            margin: 0;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
            border-color: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
            border-color: #4b5563;
        }

        .btn-outline {
            background: transparent;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* Enhanced profile avatar */
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            background-color: #f3f4f6;
            border: 4px solid #ffffff;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            flex-shrink: 0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
        }

        /* Enhanced activity badges */
        .activity-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            transition: all 0.2s ease;
        }

        .activity-badge.login {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .activity-badge.request_created {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .activity-badge.request_updated {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .activity-badge.request_completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .activity-badge.request_rejected {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        .activity-badge.profile_updated {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
        }



        /* Role badges */
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 14px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .role-badge.admin {
            background-color: #fef3c7;
            color: #92400e;
            border: 2px solid #fbbf24;
        }

        .role-badge.manager {
            background-color: #dbeafe;
            color: #1e40af;
            border: 2px solid #3b82f6;
        }

        .role-badge.user {
            background-color: #f3e8ff;
            color: #7c3aed;
            border: 2px solid #8b5cf6;
        }

        /* Enhanced buttons */
        .primary-btn {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
        }

        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
        }

        .secondary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
        }

        /* Section styling */
        .section {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e5e7eb;
            margin-bottom: 24px;
        }

        .form-section {
            padding: 24px;
            border-bottom: 1px solid #f1f5f9;
        }

        .form-section:last-child {
            border-bottom: none;
        }



        /* Stats cards */
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        /* Timeline styling */
        .timeline-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
        }

        .timeline-item {
            position: relative;
            padding: 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            transition: background-color 0.2s ease;
        }

        .timeline-item:hover {
            background-color: #f8fafc;
        }

        .timeline-item:last-child {
            border-bottom: none;
        }

        .timeline-connector {
            position: absolute;
            top: 2rem;
            right: 2rem;
            width: 2px;
            height: calc(100% - 1rem);
            background: linear-gradient(to bottom, #e5e7eb, #f3f4f6);
        }

        .timeline-icon {
            position: relative;
            z-index: 10;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Success/Error messages */
        .alert {
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-success {
            background-color: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background-color: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert-info {
            background-color: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        /* Loading animation */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .form-section {
                padding: 1.5rem;
            }

            .page-header {
                padding: 2rem;
            }

            .stat-card {
                padding: 1rem;
            }
        }

        /* Mobile-specific enhancements */
        @media (max-width: 640px) {
            /* Touch-friendly buttons */
            .primary-btn, .secondary-btn, button, a[class*="btn"] {
                min-height: 44px;
                padding: 12px 16px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            /* Better spacing for mobile */
            .form-container {
                margin: 0 -1rem;
                border-radius: 0;
            }

            /* Mobile-optimized cards */
            .bg-white {
                border-radius: 12px;
                margin-bottom: 1rem;
            }

            /* Improved text readability */
            .text-sm {
                font-size: 14px;
                line-height: 1.5;
            }

            /* Touch-friendly pagination */
            .pagination a, .pagination span {
                min-height: 44px;
                min-width: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* Better form inputs */
            input[type="date"], input[type="text"], select {
                font-size: 16px; /* Prevents zoom on iOS */
                min-height: 44px;
            }

            /* Improved status badges */
            .rounded-full {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 120px;
            }
        }

        /* Tablet optimizations */
        @media (min-width: 641px) and (max-width: 1024px) {
            .grid-cols-1.sm\\:grid-cols-2 {
                grid-template-columns: 1fr;
            }
        }

        /* Enhanced touch targets */
        @media (hover: none) and (pointer: coarse) {
            /* Touch device specific styles */
            button, a, input, select {
                min-height: 44px;
            }

            .hover\\:shadow-md:hover {
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }
        }
    </style>
</head>
<body>

    <div class="page-container">
        <!-- Page Header -->
        <header class="page-header">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-5">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 space-x-reverse mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center shadow-sm flex-shrink-0 border border-gray-300">
                            <i class="fas fa-history text-gray-600 text-xl"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h1 class="page-title">سجل أنشطة {{ target_user.full_name or target_user.username }}</h1>
                            <p class="page-subtitle">تتبع ومراقبة جميع أنشطة وطلبات المستخدم</p>
                        </div>
                    </div>

                    <!-- User Info Cards -->
                    <div class="flex flex-wrap gap-3 mt-4">
                        <div class="flex items-center space-x-2 space-x-reverse bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                            <i class="fas fa-envelope text-gray-600 text-sm"></i>
                            <span class="text-sm text-gray-700">{{ target_user.email }}</span>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                            <i class="fas fa-user-tag text-gray-600 text-sm"></i>
                            <span class="text-sm text-gray-700">
                                {% if target_user.role.value == 'admin' %}
                                    مدير النظام
                                {% elif target_user.role.value == 'manager' %}
                                    مدير المشاريع
                                {% else %}
                                    مستخدم
                                {% endif %}
                            </span>
                        </div>
                        {% if activity_stats and activity_stats.activity_level %}
                        <div class="flex items-center space-x-2 space-x-reverse bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                            <i class="fas fa-chart-line text-gray-600 text-sm"></i>
                            <span class="text-sm text-gray-700">{{ activity_stats.activity_level }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <a href="/admin/users" class="btn btn-secondary">
                        <i class="fas fa-arrow-right mr-2"></i>
                        العودة لإدارة المستخدمين
                    </a>
                    <a href="/admin/users/{{ target_user.id }}/requests" class="btn btn-outline">
                        <i class="fas fa-file-alt ml-2"></i>
                        طلبات المستخدم
                    </a>
                    <a href="/admin/users/{{ target_user.id }}/edit" class="btn btn-primary">
                        <i class="fas fa-edit ml-2"></i>
                        تعديل المستخدم
                    </a>
                </div>
            </div>
        </header>

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Activities -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الأنشطة</p>
                        <p class="text-3xl font-bold text-blue-600">{{ activity_stats.total_activities }}</p>
                        <p class="text-xs text-gray-500 mt-1">جميع الأنشطة المسجلة</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full flex items-center justify-center border border-blue-200">
                        <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Daily Activities -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">أنشطة اليوم</p>
                        <p class="text-3xl font-bold text-green-600">{{ activity_stats.daily_activities }}</p>
                        <p class="text-xs text-gray-500 mt-1">آخر 24 ساعة</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-green-50 to-green-100 rounded-full flex items-center justify-center border border-green-200">
                        <i class="fas fa-calendar-day text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Weekly Activities -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">أنشطة الأسبوع</p>
                        <p class="text-3xl font-bold text-yellow-600">{{ activity_stats.weekly_activities }}</p>
                        <p class="text-xs text-gray-500 mt-1">آخر 7 أيام</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-full flex items-center justify-center border border-yellow-200">
                        <i class="fas fa-calendar-week text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Monthly Activities -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">آخر 30 يوم</p>
                        <p class="text-3xl font-bold text-purple-600">{{ activity_stats.recent_activities }}</p>
                        <p class="text-xs text-gray-500 mt-1">الشهر الماضي</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-purple-50 to-purple-100 rounded-full flex items-center justify-center border border-purple-200">
                        <i class="fas fa-calendar-alt text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Filters Section -->
        <div class="form-container mb-8">
            <div class="form-section">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center border border-gray-300">
                            <i class="fas fa-filter text-gray-600 text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">فلترة الأنشطة</h2>
                            <p class="text-gray-600">ابحث وصفي الأنشطة حسب النوع والتاريخ</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                        <i class="fas fa-info-circle"></i>
                        <span>{{ activities|length }} نشاط</span>
                    </div>
                </div>

                <form method="GET" class="space-y-4 sm:space-y-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                        <!-- Date From -->
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-alt text-gray-400 ml-1"></i>
                                من تاريخ
                            </label>
                            <div class="relative">
                                <input type="date"
                                       id="date_from"
                                       name="date_from"
                                       value="{{ current_date_from or '' }}"
                                       class="w-full px-3 py-2 sm:px-4 sm:py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                            </div>
                        </div>

                        <!-- Date To -->
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-check text-gray-400 ml-1"></i>
                                إلى تاريخ
                            </label>
                            <div class="relative">
                                <input type="date"
                                       id="date_to"
                                       name="date_to"
                                       value="{{ current_date_to or '' }}"
                                       class="w-full px-3 py-2 sm:px-4 sm:py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons - Mobile optimized -->
                    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center pt-4 border-t border-gray-200 space-y-3 sm:space-y-0">
                        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 sm:space-x-reverse">
                            <button type="submit" class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                                <i class="fas fa-search ml-2"></i>
                                بحث
                            </button>
                            <a href="/admin/users/{{ target_user.id }}/activities" class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                                <i class="fas fa-times ml-2"></i>
                                مسح الفلاتر
                            </a>
                        </div>

                        <!-- Export Options -->
                        <div class="relative">
                            <button type="button" id="exportBtn" class="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                                <i class="fas fa-download mr-2"></i>
                                تصدير البيانات
                            </button>
                            <div id="exportMenu" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                <a href="/admin/users/{{ target_user.id }}/activities/export?format=csv{% if current_activity_type %}&activity_type={{ current_activity_type }}{% endif %}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg">
                                    <i class="fas fa-file-csv mr-2"></i>
                                    تصدير CSV
                                </a>
                                <a href="/admin/users/{{ target_user.id }}/activities/export?format=json{% if current_activity_type %}&activity_type={{ current_activity_type }}{% endif %}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg">
                                    <i class="fas fa-file-code mr-2"></i>
                                    تصدير JSON
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Results Info -->
                    {% if current_activity_type or current_date_from or current_date_to %}
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <i class="fas fa-info-circle text-blue-500"></i>
                            <div>
                                <p class="text-sm font-medium text-blue-800">نتائج الفلترة:</p>
                                <div class="text-sm text-blue-600">
                                    {% if current_activity_type %}
                                    <span>النوع: {{ current_activity_type }}</span>
                                    {% endif %}
                                    {% if current_date_from %}
                                    <span>{% if current_activity_type %} • {% endif %}من: {{ current_date_from }}</span>
                                    {% endif %}
                                    {% if current_date_to %}
                                    <span>{% if current_activity_type or current_date_from %} • {% endif %}إلى: {{ current_date_to }}</span>
                                    {% endif %}
                                    <span> • {{ activities|length }} نشاط</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- Enhanced Activities Timeline -->
        {% if activities %}
        <div class="timeline-container">
            <div class="form-section border-b">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center border border-gray-300">
                            <i class="fas fa-history text-gray-600 text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">سجل أنشطة الطلبات</h2>
                            <p class="text-gray-600">تسلسل زمني لجميع أنشطة الطلبات المتعلقة بالمستخدم</p>
                            <div class="flex items-center space-x-2 space-x-reverse text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full mt-2 inline-flex">
                                <i class="fas fa-globe"></i>
                                <span>جميع الأوقات بتوقيت البحرين (UTC+3)</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                        <i class="fas fa-clock"></i>
                        <span>{{ activities|length }} نشاط</span>
                    </div>
                </div>
            </div>

            <div class="relative">
                <!-- Timeline Line -->
                <div class="absolute right-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-purple-200 to-pink-200"></div>
                {% for activity in activities %}
                <div class="timeline-item">
                    <div class="flex items-start space-x-4 space-x-reverse">
                        <!-- Enhanced Activity Icon -->
                        <div class="timeline-icon bg-gradient-to-br
                            {% if activity.type == 'login' %}from-green-500 to-green-600
                            {% elif activity.type == 'request_created' %}from-blue-500 to-blue-600
                            {% elif activity.type == 'request_updated' %}from-yellow-500 to-yellow-600
                            {% elif activity.type == 'request_completed' %}from-green-500 to-green-600
                            {% elif activity.type == 'request_rejected' %}from-red-500 to-red-600
                            {% else %}from-gray-500 to-gray-600
                            {% endif %}">
                            <i class="
                                {% if activity.type == 'request_created' %}fas fa-plus-circle
                                {% elif activity.type == 'request_updated' %}fas fa-edit
                                {% elif activity.type == 'request_completed' %}fas fa-check-circle
                                {% elif activity.type == 'request_rejected' %}fas fa-times-circle
                                {% else %}fas fa-circle
                                {% endif %} text-white"></i>
                        </div>

                        <!-- Enhanced Activity Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-2">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ activity.title }}</h3>
                                    <p class="text-gray-600">{{ activity.description }}</p>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="activity-badge {{ activity.type }}">
                                        <i class="fas fa-tag mr-1"></i>
                                        {% if activity.type == 'request_created' %}إنشاء طلب
                                        {% elif activity.type == 'request_updated' %}تحديث طلب
                                        {% elif activity.type == 'request_completed' %}إكمال طلب
                                        {% elif activity.type == 'request_rejected' %}رفض طلب
                                        {% else %}{{ activity.type }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>

                            <!-- Enhanced Activity Details -->
                            {% if activity.details %}
                            <div class="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                                {% if activity.type in ['request_created', 'request_updated', 'request_completed', 'request_rejected'] %}
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-hashtag text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">رقم الطلب:</span>
                                        <code class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">{{ activity.details.request_number }}</code>
                                    </div>

                                    {% if activity.details.building_name %}
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-building text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">المبنى:</span>
                                        <span class="text-sm text-gray-900">{{ activity.details.building_name }}</span>
                                    </div>
                                    {% endif %}

                                    {% if activity.details.full_name %}
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-user text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">الاسم:</span>
                                        <span class="text-sm text-gray-900">{{ activity.details.full_name }}</span>
                                    </div>
                                    {% endif %}

                                    {% if activity.details.personal_number %}
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-id-card text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">الرقم الشخصي:</span>
                                        <span class="text-sm text-gray-900 font-mono">{{ activity.details.personal_number }}</span>
                                    </div>
                                    {% endif %}

                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-info-circle text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">الحالة:</span>
                                        <span class="activity-badge {{ activity.details.status }}">
                                            {% if activity.details.status_arabic %}
                                                {{ activity.details.status_arabic }}
                                            {% elif activity.details.status == 'pending' %}قيد الانتظار
                                            {% elif activity.details.status == 'in_progress' %}قيد المعالجة
                                            {% elif activity.details.status == 'completed' %}مكتمل
                                            {% elif activity.details.status == 'rejected' %}مرفوض
                                            {% else %}{{ activity.details.status }}
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>

                                <!-- Additional details for completed requests -->
                                {% if activity.type == 'request_completed' and activity.details.processing_time %}
                                <div class="mt-3 pt-3 border-t border-gray-200">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-clock text-green-500"></i>
                                        <span class="text-sm font-medium text-gray-700">مدة المعالجة:</span>
                                        <span class="text-sm text-green-600 font-semibold">{{ activity.details.processing_time }}</span>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Additional details for rejected requests -->
                                {% if activity.type == 'request_rejected' and activity.details.reason %}
                                <div class="mt-3 pt-3 border-t border-gray-200">
                                    <div class="flex items-start space-x-2 space-x-reverse">
                                        <i class="fas fa-exclamation-triangle text-red-500 mt-1"></i>
                                        <div>
                                            <span class="text-sm font-medium text-gray-700">سبب الرفض:</span>
                                            <p class="text-sm text-red-600 mt-1">{{ activity.details.reason }}</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Additional details for updated requests -->
                                {% if activity.type == 'request_updated' and activity.details.update_time_diff %}
                                <div class="mt-3 pt-3 border-t border-gray-200">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-history text-blue-500"></i>
                                        <span class="text-sm font-medium text-gray-700">وقت التحديث:</span>
                                        <span class="text-sm text-blue-600">{{ activity.details.update_time_diff }} بعد الإنشاء</span>
                                    </div>
                                </div>
                                {% endif %}

                                {% endif %}
                            </div>
                            {% endif %}

                            <!-- Enhanced Timestamp (Bahrain Time) -->
                            <div class="mt-4 flex items-center justify-between">
                                <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                                    <i class="fas fa-clock"></i>
                                    {% set bahrain_time = utc_to_bahrain(activity.timestamp) %}
                                    <time datetime="{{ activity.timestamp.isoformat() }}">
                                        {{ bahrain_time.strftime('%Y-%m-%d') if bahrain_time else 'غير محدد' }} في {{ bahrain_time.strftime('%H:%M') if bahrain_time else 'غير محدد' }}
                                    </time>
                                    <span class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">توقيت البحرين</span>
                                </div>
                                <div class="text-xs text-gray-400">
                                    {% if bahrain_time and now_bahrain %}
                                        {% set time_diff = (now_bahrain() - bahrain_time).total_seconds() %}
                                        {% if time_diff < 60 %}
                                            منذ ثوانٍ قليلة
                                        {% elif time_diff < 3600 %}
                                            منذ {{ (time_diff / 60)|int }} دقيقة
                                        {% elif time_diff < 86400 %}
                                            منذ {{ (time_diff / 3600)|int }} ساعة
                                        {% else %}
                                            منذ {{ (time_diff / 86400)|int }} يوم
                                        {% endif %}
                                    {% else %}
                                        توقيت البحرين
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Connector -->
                    {% if not loop.last %}
                    <div class="timeline-connector"></div>
                    {% endif %}
                </div>
                            </div>
                        </div>
                    </li>
                    {% endfor %}
            </div>
        </div>

        {% endif %}

        <!-- User's Latest Requests Section - Always Display -->
        <div class="form-container mt-8">
            <div class="form-section border-b">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center flex-shrink-0 border border-gray-300">
                            <i class="fas fa-file-alt text-gray-600 text-sm sm:text-lg"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h2 class="text-xl sm:text-2xl font-bold text-gray-900">آخر طلبات المستخدم</h2>
                            <p class="text-sm sm:text-base text-gray-600 truncate">أحدث الطلبات المقدمة من {{ target_user.full_name or target_user.username }}</p>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 sm:space-x-reverse text-sm text-gray-500">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-list"></i>
                            <span>{{ total_requests }} طلب إجمالي</span>
                        </div>
                        {% if total_pages > 1 %}
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <span class="hidden sm:inline text-gray-400">|</span>
                            <i class="fas fa-bookmark sm:hidden"></i>
                            <span>صفحة {{ current_page }} من {{ total_pages }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            {% if user_requests %}
            <div class="space-y-4">
                {% for request in user_requests %}
                <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 hover:shadow-md transition-shadow">
                    <!-- Mobile-first layout -->
                    <div class="space-y-4">
                        <!-- Header with name and status -->
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                            <h3 class="text-lg font-semibold text-gray-900 truncate">{{ request.full_name }}</h3>
                            <span class="px-3 py-1 text-xs font-medium rounded-full self-start sm:self-auto
                                {% if request.status.value == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif request.status.value == 'approved' or request.status.value == 'completed' %}bg-green-100 text-green-800
                                {% elif request.status.value == 'rejected' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {% if request.status.value == 'pending' %}قيد المراجعة
                                {% elif request.status.value == 'approved' or request.status.value == 'completed' %}مكتمل
                                {% elif request.status.value == 'rejected' %}مرفوض
                                {% else %}{{ request.status.value }}{% endif %}
                            </span>
                        </div>

                        <!-- Request details - Mobile optimized -->
                        <div class="space-y-3 sm:space-y-2">
                            <!-- Request number - Always visible -->
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-hashtag text-gray-400 text-sm"></i>
                                <span class="text-sm text-gray-600">رقم الطلب:</span>
                                <span class="text-sm font-medium text-gray-900">{{ request.request_number }}</span>
                            </div>

                            <!-- Two-column grid for larger screens, single column for mobile -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-id-card text-gray-400 text-sm"></i>
                                    <span class="text-sm text-gray-600">الرقم الشخصي:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ request.personal_number }}</span>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-phone text-gray-400 text-sm"></i>
                                    <span class="text-sm text-gray-600">الهاتف:</span>
                                    <span class="text-sm font-medium text-gray-900 dir-ltr">{{ request.phone_number }}</span>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-calendar text-gray-400 text-sm"></i>
                                    <span class="text-sm text-gray-600">تاريخ الإنشاء:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ request.created_at.strftime('%Y-%m-%d') }}</span>
                                </div>
                                {% if request.building_name %}
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-building text-gray-400 text-sm"></i>
                                    <span class="text-sm text-gray-600">رقم المبنى:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ request.building_name }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Action button - Full width on mobile -->
                        <div class="pt-3 border-t border-gray-100">
                            <a href="/admin/requests/{{ request.id }}" class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                                <i class="fas fa-eye text-sm ml-2"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-8">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-file-alt text-2xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات</h3>
                <p class="text-gray-600">لم يقم {{ target_user.full_name or target_user.username }} بتقديم أي طلبات حتى الآن.</p>
            </div>
            {% endif %}

            <!-- Pagination -->
            {% if total_pages > 1 %}
            <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-6">
                <div class="flex flex-1 justify-between sm:hidden">
                    {% if has_prev %}
                    <a href="?page={{ prev_page }}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                       class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                        السابق
                    </a>
                    {% else %}
                    <span class="relative inline-flex items-center rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-400 cursor-not-allowed">
                        السابق
                    </span>
                    {% endif %}

                    {% if has_next %}
                    <a href="?page={{ next_page }}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                       class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                        التالي
                    </a>
                    {% else %}
                    <span class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-400 cursor-not-allowed">
                        التالي
                    </span>
                    {% endif %}
                </div>

                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            عرض
                            <span class="font-medium">{{ ((current_page - 1) * per_page) + 1 }}</span>
                            إلى
                            <span class="font-medium">{{ ((current_page - 1) * per_page) + user_requests|length }}</span>
                            من
                            <span class="font-medium">{{ total_requests }}</span>
                            طلب
                        </p>
                    </div>

                    <div>
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <!-- Previous button -->
                            {% if has_prev %}
                            <a href="?page={{ prev_page }}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                               class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">السابق</span>
                                <i class="fas fa-chevron-right h-5 w-5" aria-hidden="true"></i>
                            </a>
                            {% else %}
                            <span class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 cursor-not-allowed">
                                <span class="sr-only">السابق</span>
                                <i class="fas fa-chevron-right h-5 w-5" aria-hidden="true"></i>
                            </span>
                            {% endif %}

                            <!-- Page numbers -->
                            {% set start_page = [1, current_page - 2]|max %}
                            {% set end_page = [total_pages, current_page + 2]|min %}

                            {% if start_page > 1 %}
                            <a href="?page=1{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                               class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">1</a>
                            {% if start_page > 2 %}
                            <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                            {% endif %}
                            {% endif %}

                            {% for page_num in range(start_page, end_page + 1) %}
                            {% if page_num == current_page %}
                            <span aria-current="page" class="relative z-10 inline-flex items-center bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">{{ page_num }}</span>
                            {% else %}
                            <a href="?page={{ page_num }}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                               class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ page_num }}</a>
                            {% endif %}
                            {% endfor %}

                            {% if end_page < total_pages %}
                            {% if end_page < total_pages - 1 %}
                            <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                            {% endif %}
                            <a href="?page={{ total_pages }}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                               class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ total_pages }}</a>
                            {% endif %}

                            <!-- Next button -->
                            {% if has_next %}
                            <a href="?page={{ next_page }}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                               class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">التالي</span>
                                <i class="fas fa-chevron-left h-5 w-5" aria-hidden="true"></i>
                            </a>
                            {% else %}
                            <span class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 cursor-not-allowed">
                                <span class="sr-only">التالي</span>
                                <i class="fas fa-chevron-left h-5 w-5" aria-hidden="true"></i>
                            </span>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Export menu toggle
            const exportBtn = document.getElementById('exportBtn');
            const exportMenu = document.getElementById('exportMenu');

            if (exportBtn && exportMenu) {
                exportBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    exportMenu.classList.toggle('hidden');
                });

                // Close export menu when clicking outside
                document.addEventListener('click', function() {
                    exportMenu.classList.add('hidden');
                });

                exportMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // Timeline animations
            const timelineItems = document.querySelectorAll('.timeline-item');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            timelineItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
                observer.observe(item);
            });

            // Auto-refresh activities every 30 seconds
            setInterval(() => {
                const currentUrl = new URL(window.location);
                fetch(currentUrl.href, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    // Update activity count in header
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newCount = doc.querySelector('.timeline-container .form-section span');
                    const currentCount = document.querySelector('.timeline-container .form-section span');

                    if (newCount && currentCount && newCount.textContent !== currentCount.textContent) {
                        showAlert('تم تحديث الأنشطة - توجد أنشطة جديدة', 'info');
                    }
                })
                .catch(error => {
                    console.log('Auto-refresh failed:', error);
                });
            }, 30000);
        });

        // Alert function
        function showAlert(message, type = 'info') {
            // Remove existing alerts
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;

            const icon = type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle';

            alertDiv.innerHTML = `
                <i class="fas ${icon}"></i>
                <span>${message}</span>
            `;

            // Insert at the top of the page
            const container = document.querySelector('.max-w-7xl');
            if (container) {
                container.insertBefore(alertDiv, container.firstChild);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }

        // Check for URL parameters and show messages
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('success')) {
                showAlert('تم تنفيذ العملية بنجاح', 'success');
            }
            if (urlParams.get('error')) {
                showAlert('حدث خطأ أثناء تنفيذ العملية', 'error');
            }
        });
    </script>

</body>
</html>
