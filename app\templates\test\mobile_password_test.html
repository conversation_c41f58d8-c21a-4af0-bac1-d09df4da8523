{% extends "base.html" %}

{% block title %}اختبار كلمة المرور على الجوال{% endblock %}

{% block content %}
<div class="max-w-md mx-auto p-4">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-xl font-bold text-gray-900 mb-6 text-center">اختبار كلمة المرور على الجوال</h1>
        
        <!-- Mobile Test Form -->
        <form class="space-y-6">
            <div class="form-group">
                <label for="mobile_password" class="form-label text-lg">كلمة المرور</label>
                <div class="relative">
                    <input type="password"
                           id="mobile_password"
                           name="password"
                           class="form-input pr-10 pl-12 text-lg h-12"
                           placeholder="أدخل كلمة المرور">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="mobile_confirm" class="form-label text-lg">تأكيد كلمة المرور</label>
                <div class="relative">
                    <input type="password"
                           id="mobile_confirm"
                           name="confirm_password"
                           class="form-input pr-10 pl-12 text-lg h-12"
                           placeholder="أعد إدخال كلمة المرور">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <button type="button" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg text-lg font-medium">
                اختبار (تجريبي)
            </button>
        </form>

        <!-- Instructions -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-blue-800 mb-3">تعليمات الاختبار على الجوال</h3>
            <ul class="text-blue-700 space-y-2 text-sm">
                <li>• يجب أن تظهر أيقونة العين بوضوح بجانب كل حقل</li>
                <li>• اضغط على أيقونة العين لإظهار/إخفاء كلمة المرور</li>
                <li>• يجب أن تكون الأيقونة كبيرة بما يكفي للمس بسهولة</li>
                <li>• لا يجب أن تظهر أي حركة أو تأثيرات عند المرور عليها</li>
                <li>• يجب أن تعمل بسلاسة على الشاشات الصغيرة</li>
            </ul>
        </div>

        <!-- Device Info -->
        <div class="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">معلومات الجهاز</h3>
            <div class="text-sm text-gray-600 space-y-1">
                <p>عرض الشاشة: <span id="screen-width"></span>px</p>
                <p>ارتفاع الشاشة: <span id="screen-height"></span>px</p>
                <p>نوع الجهاز: <span id="device-type"></span></p>
                <p>دعم اللمس: <span id="touch-support"></span></p>
            </div>
        </div>

        <!-- Test Results -->
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">نتائج الاختبار</h3>
            <div id="test-results" class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span>أيقونات العين مرئية:</span>
                    <span id="icons-visible" class="font-medium">جاري الفحص...</span>
                </div>
                <div class="flex justify-between">
                    <span>حجم منطقة اللمس مناسب:</span>
                    <span id="touch-area" class="font-medium">جاري الفحص...</span>
                </div>
                <div class="flex justify-between">
                    <span>وظيفة التبديل تعمل:</span>
                    <span id="toggle-works" class="font-medium">جاري الفحص...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Display device information
document.addEventListener('DOMContentLoaded', function() {
    // Screen dimensions
    document.getElementById('screen-width').textContent = window.innerWidth;
    document.getElementById('screen-height').textContent = window.innerHeight;
    
    // Device type detection
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
    const isDesktop = window.innerWidth > 1024;
    
    let deviceType = 'غير محدد';
    if (isMobile) deviceType = 'جوال';
    else if (isTablet) deviceType = 'تابلت';
    else if (isDesktop) deviceType = 'سطح المكتب';
    
    document.getElementById('device-type').textContent = deviceType;
    
    // Touch support
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    document.getElementById('touch-support').textContent = hasTouch ? 'نعم' : 'لا';
    
    // Test password toggle functionality
    setTimeout(testPasswordToggle, 1000);
});

function testPasswordToggle() {
    // Check if eye icons are visible
    const toggleButtons = document.querySelectorAll('.password-toggle-btn');
    const iconsVisible = toggleButtons.length >= 2;
    document.getElementById('icons-visible').textContent = iconsVisible ? '✅ نعم' : '❌ لا';
    document.getElementById('icons-visible').className = iconsVisible ? 'font-medium text-green-600' : 'font-medium text-red-600';
    
    // Check touch area size
    let touchAreaOk = true;
    toggleButtons.forEach(btn => {
        const rect = btn.getBoundingClientRect();
        if (rect.width < 32 || rect.height < 32) {
            touchAreaOk = false;
        }
    });
    document.getElementById('touch-area').textContent = touchAreaOk ? '✅ نعم' : '❌ لا';
    document.getElementById('touch-area').className = touchAreaOk ? 'font-medium text-green-600' : 'font-medium text-red-600';
    
    // Test toggle functionality
    if (toggleButtons.length > 0) {
        const firstButton = toggleButtons[0];
        const passwordField = document.getElementById('mobile_password');
        
        // Simulate click
        firstButton.click();
        
        setTimeout(() => {
            const toggleWorks = passwordField.type === 'text';
            document.getElementById('toggle-works').textContent = toggleWorks ? '✅ نعم' : '❌ لا';
            document.getElementById('toggle-works').className = toggleWorks ? 'font-medium text-green-600' : 'font-medium text-red-600';
            
            // Reset password field
            if (toggleWorks) {
                firstButton.click();
            }
        }, 100);
    } else {
        document.getElementById('toggle-works').textContent = '❌ لا توجد أزرار';
        document.getElementById('toggle-works').className = 'font-medium text-red-600';
    }
}

// Update screen size on orientation change
window.addEventListener('orientationchange', function() {
    setTimeout(() => {
        document.getElementById('screen-width').textContent = window.innerWidth;
        document.getElementById('screen-height').textContent = window.innerHeight;
    }, 100);
});
</script>
{% endblock %}
