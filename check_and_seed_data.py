#!/usr/bin/env python3
"""
Check existing data and create sample data if needed
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import random

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_and_seed_data():
    """Check existing data and create sample data if needed"""
    try:
        from app.database import SessionLocal
        from app.models.request import Request, RequestStatus
        from app.models.activity import Activity, ActivityType
        from app.models.user import User, UserRole
        from app.services.user_service import UserService
        from app.services.request_service import RequestService
        from app.services.activity_service import ActivityService
        
        db = SessionLocal()
        
        try:
            # Check existing data
            users_count = db.query(User).count()
            requests_count = db.query(Request).count()
            activities_count = db.query(Activity).count()
            
            print(f"📊 Current Data Status:")
            print(f"   Users: {users_count}")
            print(f"   Requests: {requests_count}")
            print(f"   Activities: {activities_count}")
            print()
            
            # Create sample users if needed
            if users_count < 3:
                print("👥 Creating sample users...")
                sample_users = [
                    ("user1", "<EMAIL>", "أحمد محمد علي", "password123"),
                    ("user2", "<EMAIL>", "فاطمة أحمد سالم", "password123"),
                    ("user3", "<EMAIL>", "محمد سعد الدين", "password123"),
                ]
                
                for username, email, full_name, password in sample_users:
                    existing_user = UserService.get_user_by_email(db, email)
                    if not existing_user:
                        user = UserService.create_user(
                            db=db,
                            username=username,
                            email=email,
                            full_name=full_name,
                            password=password,
                            role=UserRole.USER
                        )
                        print(f"   ✅ Created user: {full_name}")
            
            # Create sample requests if needed
            if requests_count < 5:
                print("📋 Creating sample requests...")
                users = db.query(User).filter(User.role == UserRole.USER).all()
                
                if users:
                    sample_requests = [
                        {
                            "full_name": "أحمد محمد علي",
                            "personal_number": "123456789",
                            "phone_number": "966501234567",
                            "building_name": "مجمع الأعمال التجاري",
                            "road_name": "شارع الملك فهد",
                            "building_number": "123",
                            "status": RequestStatus.PENDING,
                            "licenses_section": True,
                            "fire_equipment_section": True,
                        },
                        {
                            "full_name": "فاطمة أحمد سالم",
                            "personal_number": "987654321",
                            "phone_number": "966507654321",
                            "building_name": "برج الأعمال الحديث",
                            "road_name": "شارع العليا",
                            "building_number": "456",
                            "status": RequestStatus.IN_PROGRESS,
                            "commercial_records_section": True,
                            "engineering_offices_section": True,
                        },
                        {
                            "full_name": "محمد سعد الدين",
                            "personal_number": "456789123",
                            "phone_number": "966509876543",
                            "building_name": "مركز التسوق الكبير",
                            "road_name": "شارع التحلية",
                            "building_number": "789",
                            "status": RequestStatus.COMPLETED,
                            "hazardous_materials_section": True,
                            "licenses_section": True,
                        },
                        {
                            "full_name": "سارة عبدالله محمد",
                            "personal_number": "321654987",
                            "phone_number": "966502468135",
                            "building_name": "مجمع المكاتب الإدارية",
                            "road_name": "شارع الأمير محمد",
                            "building_number": "321",
                            "status": RequestStatus.REJECTED,
                            "fire_equipment_section": True,
                            "commercial_records_section": True,
                        },
                        {
                            "full_name": "عبدالرحمن خالد أحمد",
                            "personal_number": "789123456",
                            "phone_number": "966508642097",
                            "building_name": "مركز الخدمات الطبية",
                            "road_name": "شارع الملك عبدالعزيز",
                            "building_number": "654",
                            "status": RequestStatus.PENDING,
                            "engineering_offices_section": True,
                            "hazardous_materials_section": True,
                        }
                    ]
                    
                    for i, req_data in enumerate(sample_requests):
                        user = users[i % len(users)]
                        
                        # Create request
                        request = RequestService.create_request(
                            db=db,
                            user_id=user.id,
                            **{k: v for k, v in req_data.items() if k != 'status'}
                        )
                        
                        # Update status
                        request.status = req_data['status']
                        db.commit()
                        
                        print(f"   ✅ Created request: {request.request_number} - {req_data['full_name']}")
            
            # Create sample activities if needed
            if activities_count < 10:
                print("📝 Creating sample activities...")
                users = db.query(User).all()
                requests = db.query(Request).all()
                
                if users and requests:
                    # Create request-related activities
                    activity_templates = [
                        (ActivityType.REQUEST_CREATED, "تم إنشاء طلب جديد رقم {request_number}"),
                        (ActivityType.REQUEST_UPDATED, "تم تحديث الطلب رقم {request_number}"),
                        (ActivityType.REQUEST_COMPLETED, "تم إكمال الطلب رقم {request_number}"),
                        (ActivityType.FILE_UPLOADED, "تم رفع ملف للطلب رقم {request_number}"),
                        (ActivityType.PROFILE_UPDATED, "تم تحديث الملف الشخصي"),
                    ]
                    
                    for request in requests:
                        user = next((u for u in users if u.id == request.user_id), users[0])
                        
                        # Create 2-3 activities per request
                        num_activities = random.randint(2, 3)
                        selected_templates = random.sample(activity_templates, num_activities)
                        
                        for i, (activity_type, description_template) in enumerate(selected_templates):
                            # Create activity with timestamp spread over last 30 days
                            days_ago = random.randint(0, 30)
                            hours_ago = random.randint(0, 23)
                            activity_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago)
                            
                            description = description_template.format(request_number=request.request_number)
                            
                            activity = Activity(
                                user_id=user.id,
                                activity_type=activity_type,
                                description=description,
                                details=f'{{"request_number": "{request.request_number}", "status": "{request.status.value}"}}',
                                ip_address="*************",
                                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                created_at=activity_time
                            )
                            
                            db.add(activity)
                            print(f"   ✅ Created activity: {description}")
                    
                    db.commit()
            
            # Final count
            users_count = db.query(User).count()
            requests_count = db.query(Request).count()
            activities_count = db.query(Activity).count()
            
            print()
            print(f"📊 Final Data Status:")
            print(f"   Users: {users_count}")
            print(f"   Requests: {requests_count}")
            print(f"   Activities: {activities_count}")
            print()
            print("✅ Data check and seeding completed successfully!")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Checking and seeding sample data...")
    print("=" * 50)
    
    success = check_and_seed_data()
    
    if not success:
        sys.exit(1)
    
    print("=" * 50)
    print("🎉 Ready to test reports with real data!")
