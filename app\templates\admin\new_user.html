{% extends "base.html" %}

{% block title %}إنشاء مستخدم جديد - CMSVS{% endblock %}

{% block head %}
<!-- Admin Forms JavaScript -->
<script src="/static/js/admin-forms.js?v={{ cache_bust }}" defer></script>


{% endblock %}

{% block content %}

<div class="admin-form-container">
    <!-- Header -->
    <div class="admin-form-header">
        <div>
            <h1 class="admin-form-title">إنشاء مستخدم جديد</h1>
            <p class="admin-form-subtitle">قم بإدخال بيانات المستخدم الجديد</p>
        </div>
        <div>
            <a href="/admin/users" class="admin-btn admin-btn-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة لإدارة المستخدمين
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <div class="admin-form-card">
                <h2 class="admin-form-section-title">
                    <i class="fas fa-user-plus text-green-600"></i>
                    بيانات المستخدم الجديد
                </h2>

                <form method="post" action="/admin/users/new" id="newUserForm" novalidate
                      role="form" aria-labelledby="form-title" aria-describedby="form-description">
                    <div id="form-description" class="sr-only">
                        نموذج إنشاء مستخدم جديد في النظام. جميع الحقول المطلوبة مميزة بعلامة النجمة.
                    </div>

                    <fieldset class="admin-form-grid two-cols">
                        <legend class="sr-only">معلومات المستخدم الأساسية</legend>

                        <div class="admin-form-field">
                            <label for="username" class="admin-form-label">
                                اسم المستخدم <span class="text-red-500" aria-label="مطلوب">*</span>
                            </label>
                            <input type="text"
                                   id="username"
                                   name="username"
                                   value="{{ form_data.username if form_data else '' }}"
                                   required
                                   autocomplete="username"
                                   aria-describedby="username-help"
                                   aria-invalid="false"
                                   pattern="[a-zA-Z0-9_]{3,20}"
                                   maxlength="20"
                                   minlength="3"
                                   class="admin-form-input">
                            <div id="username-help" class="admin-form-help" role="note">
                                3-20 حرف، يمكن استخدام الأرقام والشرطة السفلية
                            </div>
                        </div>

                        <div class="admin-form-field">
                            <label for="email" class="admin-form-label">
                                البريد الإلكتروني <span class="text-red-500" aria-label="مطلوب">*</span>
                            </label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   value="{{ form_data.email if form_data else '' }}"
                                   required
                                   autocomplete="email"
                                   aria-invalid="false"
                                   aria-describedby="email-help"
                                   class="admin-form-input">
                            <div id="email-help" class="admin-form-help sr-only" role="note">
                                أدخل عنوان بريد إلكتروني صحيح
                            </div>
                        </div>
                    </fieldset>

                    <fieldset class="admin-form-grid two-cols">
                        <legend class="sr-only">الاسم والدور</legend>

                        <div class="admin-form-field">
                            <label for="full_name" class="admin-form-label">
                                الاسم الكامل <span class="text-red-500" aria-label="مطلوب">*</span>
                            </label>
                            <input type="text"
                                   id="full_name"
                                   name="full_name"
                                   value="{{ form_data.full_name if form_data else '' }}"
                                   required
                                   autocomplete="name"
                                   aria-invalid="false"
                                   aria-describedby="full-name-help"
                                   maxlength="100"
                                   class="admin-form-input">
                            <div id="full-name-help" class="admin-form-help sr-only" role="note">
                                أدخل الاسم الكامل للمستخدم
                            </div>
                        </div>

                        <div class="admin-form-field">
                            <label for="role" class="admin-form-label">
                                الدور <span class="text-red-500" aria-label="مطلوب">*</span>
                            </label>
                            <select id="role"
                                    name="role"
                                    required
                                    aria-describedby="role-help"
                                    aria-invalid="false"
                                    class="admin-form-select">
                                <option value="" disabled>اختر الدور</option>
                                {% for role_option in roles %}
                                <option value="{{ role_option }}"
                                        {% if form_data and form_data.role == role_option %}selected{% endif %}>
                                    {% if role_option == 'user' %}مستخدم عادي
                                    {% elif role_option == 'admin' %}مدير النظام
                                    {% elif role_option == 'manager' %}مدير المشاريع
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                            <div id="role-help" class="admin-form-help" role="note">
                                <strong>مستخدم عادي:</strong> يمكنه إنشاء وعرض طلباته فقط<br>
                                <strong>مدير المشاريع:</strong> يمكنه إدارة الطلبات والمشاريع<br>
                                <strong>مدير النظام:</strong> يمكنه إدارة جميع المستخدمين والطلبات
                            </div>
                        </div>
                    </fieldset>

                    <fieldset class="admin-form-grid two-cols">
                        <legend class="sr-only">كلمة المرور</legend>

                        <div class="admin-form-field">
                            <label for="password" class="admin-form-label">
                                كلمة المرور <span class="text-red-500" aria-label="مطلوب">*</span>
                            </label>
                            <div class="relative">
                                <input type="password"
                                       id="password"
                                       name="password"
                                       required
                                       minlength="6"
                                       maxlength="128"
                                       autocomplete="new-password"
                                       aria-describedby="password-help"
                                       aria-invalid="false"
                                       class="admin-form-input">
                                <button type="button"
                                        id="togglePassword"
                                        aria-label="إظهار كلمة المرور"
                                        aria-pressed="false"
                                        tabindex="0"
                                        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded">
                                    <i class="fas fa-eye" aria-hidden="true"></i>
                                </button>
                            </div>
                            <div id="password-help" class="admin-form-help" role="note">
                                يجب أن تكون كلمة المرور 6 أحرف على الأقل
                            </div>
                        </div>

                        <div class="admin-form-field">
                            <label for="confirm_password" class="admin-form-label">
                                تأكيد كلمة المرور <span class="text-red-500" aria-label="مطلوب">*</span>
                            </label>
                            <div class="relative">
                                <input type="password"
                                       id="confirm_password"
                                       name="confirm_password"
                                       required
                                       minlength="6"
                                       maxlength="128"
                                       autocomplete="new-password"
                                       aria-describedby="confirm-password-help"
                                       aria-invalid="false"
                                       class="admin-form-input pl-10">
                                <!-- Password toggle button will be added automatically by password-toggle.js -->
                            </div>
                            <div id="confirm-password-help" class="admin-form-help sr-only" role="note">
                                أعد إدخال كلمة المرور للتأكيد
                            </div>
                        </div>
                    </fieldset>

                    <div class="admin-form-actions">
                        <a href="/admin/users" class="admin-btn admin-btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="admin-btn admin-btn-primary">
                            <i class="fas fa-user-plus"></i>
                            إنشاء المستخدم
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <aside class="admin-sidebar" role="complementary" aria-label="معلومات إضافية">
            <!-- User Roles Info -->
            <section class="admin-info-card" aria-labelledby="roles-title">
                <h3 id="roles-title" class="admin-info-title">
                    <i class="fas fa-info-circle text-blue-600" aria-hidden="true"></i>
                    أدوار المستخدمين
                </h3>

                <div class="admin-role-card user" role="region" aria-labelledby="user-role-title">
                    <div id="user-role-title" class="admin-role-title">
                        <i class="fas fa-user" aria-hidden="true"></i>
                        مستخدم عادي
                    </div>
                    <ul class="admin-role-list" role="list">
                        <li role="listitem">• إنشاء طلبات جديدة</li>
                        <li role="listitem">• عرض طلباته الشخصية</li>
                        <li role="listitem">• تعديل طلباته قبل المراجعة</li>
                        <li role="listitem">• رفع وإدارة الملفات</li>
                    </ul>
                </div>

                <div class="admin-role-card admin" role="region" aria-labelledby="admin-role-title">
                    <div id="admin-role-title" class="admin-role-title">
                        <i class="fas fa-crown" aria-hidden="true"></i>
                        مدير النظام
                    </div>
                    <ul class="admin-role-list" role="list">
                        <li role="listitem">• جميع صلاحيات المستخدم العادي</li>
                        <li role="listitem">• إدارة جميع المستخدمين</li>
                        <li role="listitem">• عرض وإدارة جميع الطلبات</li>
                        <li role="listitem">• تغيير حالة الطلبات</li>
                        <li role="listitem">• الوصول للوحة التحكم</li>
                    </ul>
                </div>
            </section>

            <!-- Security Guidelines -->
            <section class="admin-info-card" aria-labelledby="security-title">
                <h3 id="security-title" class="admin-info-title">
                    <i class="fas fa-shield-alt text-green-600" aria-hidden="true"></i>
                    إرشادات الأمان
                </h3>
                <div class="admin-security-list" role="list">
                    <div class="admin-security-item success" role="listitem">
                        <i class="fas fa-check-circle" aria-hidden="true"></i>
                        <div class="admin-security-text">
                            استخدم كلمة مرور قوية تحتوي على أحرف وأرقام
                        </div>
                    </div>
                    <div class="admin-security-item success" role="listitem">
                        <i class="fas fa-check-circle" aria-hidden="true"></i>
                        <div class="admin-security-text">
                            تأكد من صحة البريد الإلكتروني للمستخدم
                        </div>
                    </div>
                    <div class="admin-security-item success" role="listitem">
                        <i class="fas fa-check-circle" aria-hidden="true"></i>
                        <div class="admin-security-text">
                            اختر الدور المناسب حسب احتياجات المستخدم
                        </div>
                    </div>
                    <div class="admin-security-item warning" role="listitem">
                        <i class="fas fa-exclamation-triangle" aria-label="تحذير" aria-hidden="true"></i>
                        <div class="admin-security-text">
                            صلاحيات المدير تشمل الوصول الكامل للنظام
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Stats -->
            <section class="admin-info-card" aria-labelledby="stats-title">
                <h3 id="stats-title" class="admin-info-title">
                    <i class="fas fa-chart-bar text-indigo-600" aria-hidden="true"></i>
                    إحصائيات سريعة
                </h3>
                <div class="admin-stats-grid" role="list" aria-label="إحصائيات المستخدمين">
                    <div class="admin-stat-item" role="listitem">
                        <span class="admin-stat-label">إجمالي المستخدمين:</span>
                        <span class="admin-stat-value total" aria-label="{{ total_users or 0 }} مستخدم إجمالي">{{ total_users or 0 }}</span>
                    </div>
                    <div class="admin-stat-item" role="listitem">
                        <span class="admin-stat-label">المديرون:</span>
                        <span class="admin-stat-value admin" aria-label="{{ admin_count or 0 }} مدير">{{ admin_count or 0 }}</span>
                    </div>
                    <div class="admin-stat-item" role="listitem">
                        <span class="admin-stat-label">المستخدمون العاديون:</span>
                        <span class="admin-stat-value user" aria-label="{{ user_count or 0 }} مستخدم عادي">{{ user_count or 0 }}</span>
                    </div>
                    <div class="admin-stat-item" role="listitem">
                        <span class="admin-stat-label">المستخدمون النشطون:</span>
                        <span class="admin-stat-value active" aria-label="{{ active_users or 0 }} مستخدم نشط">{{ active_users or 0 }}</span>
                    </div>
                </div>
            </section>
        </aside>
    </div>
</div>


{% endblock %}