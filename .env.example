# إرشيف الدفاع المدني - Environment Configuration
# Copy this file to .env and update the values

# Server Configuration
SERVER_IP=************
TZ=UTC

# Database Configuration
POSTGRES_DB=cmsvs_db
POSTGRES_USER=cmsvs_user
POSTGRES_PASSWORD=your_secure_db_password_here
DATABASE_URL=**********************************************/cmsvs_db

# Database Pool Configuration
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=60
DB_POOL_RECYCLE=3600

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_URL=redis://:your_secure_redis_password_here@redis:6379/0

# Application Configuration
APP_NAME=إرشيف الدفاع المدني
APP_VERSION=2.0.0
APP_ENV=production
DEBUG=False
LOG_LEVEL=INFO

# Security
SECRET_KEY=your_very_long_secret_key_here_minimum_32_characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_secure_admin_password_here

# File Upload Configuration
MAX_FILE_SIZE=52428800  # 50MB in bytes
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif,xlsx,xls
UPLOAD_DIRECTORY=/app/uploads

# CORS and Hosts
ALLOWED_HOSTS=localhost,127.0.0.1,************,your-domain.com
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Performance
WORKERS=2
WORKER_CONNECTIONS=1000
NGINX_WORKERS=auto
NGINX_CONNECTIONS=1024

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Data Directories
DATA_DIR=./data
SECRETS_DIR=./secrets

# Build Information
BUILD_DATE=
VCS_REF=
