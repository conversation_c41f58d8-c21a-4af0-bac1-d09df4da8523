{% extends "base.html" %}

{% block title %}سجل أنشطة الطلبات - لوحة التحكم{% endblock %}

{% block content %}
<style>
/* Professional Admin Requests Records Styling */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

/* Requests Records header optimization for desktop */
.page-header {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.requests-title {
    text-align: right;
    order: 2; /* Title second in DOM order - will appear on left in RTL */
}

.requests-title h1 {
    font-size: 2.25rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 8px;
    line-height: 1.2;
}

.requests-title p {
    font-size: 1rem;
    color: #64748b;
    line-height: 1.5;
}

.requests-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    order: 1; /* Actions first in DOM order - will appear on right in RTL */
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s;
    min-width: 200px;
    text-align: right;
    text-decoration: none;
    gap: 20px;
}

.action-btn span {
    margin-left: 20px;
}

.action-btn.primary {
    background-color: #3b82f6;
    color: white;
}

.action-btn.primary:hover {
    background-color: #2563eb;
    color: white;
    text-decoration: none;
}

.action-btn.secondary {
    background-color: #64748b;
    color: white;
}

.action-btn.secondary:hover {
    background-color: #475569;
    color: white;
    text-decoration: none;
}



/* RTL layout adjustments for desktop */
@media (min-width: 769px) {
    .page-header {
        flex-direction: row-reverse; /* RTL direction - title on left, actions on right */
    }

    .requests-title {
        padding-left: 0;
        padding-right: 24px;
    }

    .action-btn {
        justify-content: flex-start; /* Normal flex start alignment */
    }
}

/* Mobile optimization */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        gap: 20px;
    }

    .requests-title {
        text-align: center;
        order: 1;
        padding: 0;
    }

    .requests-title h1 {
        font-size: 2rem;
        margin-bottom: 4px;
    }

    .requests-title p {
        font-size: 0.875rem;
    }

    .requests-actions {
        order: 2;
        width: 100%;
        align-items: stretch;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
        padding: 14px;
    }
}

/* Report creation section reorganization */
.report-creation-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Header with title and icon */
.report-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.report-section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

.report-section-icon {
  color: #10b981;
  font-size: 1.25rem;
}

/* Description text */
.report-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 24px;
  text-align: right;
}

/* Report options grid */
.report-options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

/* Report option buttons */
.report-option-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  color: white;
  text-align: center;
  transition: all 0.2s;
  text-decoration: none;
  font-size: 0.9rem;
}

.report-option-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: white;
}

.report-option-btn.blue {
  background-color: #3b82f6;
}

.report-option-btn.green {
  background-color: #10b981;
}

.report-option-btn i {
  font-size: 1.25rem;
}

/* Report settings - Desktop reorganization */
.report-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
}

.report-settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-settings-title {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.report-settings-title i {
  color: #10b981;
  font-size: 1rem;
}

.report-settings-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.report-settings-description {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
}

.report-settings-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.report-settings-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  background-color: #ffffff;
  color: #374151;
  border: 1px solid #d1d5db;
  transition: all 0.2s;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
}

.report-settings-btn:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
  text-decoration: none;
  color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-settings-btn.primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.report-settings-btn.primary:hover {
  background-color: #2563eb;
  border-color: #2563eb;
  color: white;
}

/* Desktop-specific enhancements */
@media (min-width: 768px) {
  .report-settings {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .report-settings-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .report-settings-description {
    flex: 1;
    margin-right: 16px;
  }

  .report-settings-actions {
    flex-shrink: 0;
  }
}

/* Export options - Desktop reorganization */
.export-options {
  margin-top: 24px;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
}

.export-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.export-title {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.export-title i {
  color: #3b82f6;
  font-size: 1rem;
}

.export-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.export-description {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
}

.export-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.export-btn {
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 100px;
  text-decoration: none;
  transition: all 0.2s;
  border: 2px solid transparent;
  font-size: 0.9rem;
}

.export-btn:hover {
  text-decoration: none;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.export-btn.print {
  background-color: #8b5cf6;
  border-color: #8b5cf6;
}

.export-btn.print:hover {
  background-color: #7c3aed;
  border-color: #7c3aed;
}

.export-btn.pdf {
  background-color: #ef4444;
  border-color: #ef4444;
}

.export-btn.pdf:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

.export-btn.csv {
  background-color: #10b981;
  border-color: #10b981;
}

.export-btn.csv:hover {
  background-color: #059669;
  border-color: #059669;
}

.export-btn i {
  font-size: 1.1rem;
}

/* Desktop-specific enhancements for export */
@media (min-width: 768px) {
  .export-options {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .export-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .export-description {
    flex: 1;
    margin-right: 16px;
  }

  .export-buttons {
    flex-shrink: 0;
    gap: 8px;
  }

  .export-btn {
    min-width: 90px;
    padding: 10px 16px;
  }
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .report-creation-section {
    grid-template-columns: 3fr 2fr;
  }

  .report-content {
    grid-column: 1;
  }

  .report-options-container {
    grid-column: 2;
  }
}

@media (max-width: 767px) {
  .report-options-grid {
    grid-template-columns: 1fr;
  }

  .export-buttons {
    justify-content: center;
  }
}

/* Legacy styles for backward compatibility */
.section {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    overflow: hidden;
}

.section-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-content {
    padding: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
}

.form-control {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: #ffffff;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-primary {
    background: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-outline {
    background: #ffffff;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-outline:hover {
    background: #f9fafb;
}

.activities-table {
    width: 100%;
    border-collapse: collapse;
}

.activities-table th,
.activities-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
}

.activities-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.activities-table td {
    font-size: 14px;
    color: #111827;
}

.activity-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.activity-type.request {
    background: #dbeafe;
    color: #1e40af;
}

.activity-type.file {
    background: #dcfce7;
    color: #166534;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
}

.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d1d5db;
}

@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <header class="page-header">
        <div class="requests-actions">
            <a href="/admin/dashboard" class="action-btn secondary">
                <i class="fas fa-arrow-right"></i>
                <span>العودة للوحة التحكم</span>
            </a>
        </div>
        <div class="requests-title">
            <h1>سجل أنشطة الطلبات</h1>
            <p>تتبع ومراقبة جميع الأنشطة المتعلقة بالطلبات والتفاعلات</p>
        </div>
    </header>

    <!-- Report Generation Section -->
    <div class="report-creation-section">
      <div class="report-content">
        <div class="report-section-header">
          <h2 class="report-section-title">تقارير الطلبات</h2>
          <i class="fas fa-file-chart-line report-section-icon"></i>
        </div>

        <p class="report-description">
          قم بإنشاء تقارير مفصلة للطلبات تتضمن إحصائيات شاملة حول حالات الطلبات والتفاعلات خلال فترات زمنية مختلفة.
        </p>

        <div class="report-settings">
          <div class="report-settings-header">
            <h3 class="report-settings-title">
              <i class="fas fa-cog"></i>
              إعدادات التقرير
            </h3>
          </div>
          <div class="report-settings-content">
            <p class="report-settings-description">
              اختر من التقارير السريعة أو استخدم الخيارات المتقدمة لتخصيص تقرير الطلبات
            </p>
            <div class="report-settings-actions">
              <a href="/admin/request-activity-report" class="report-settings-btn primary">
                <i class="fas fa-tools"></i>
                <span>خيارات متقدمة</span>
              </a>
              <a href="/admin/requests-records" class="report-settings-btn">
                <i class="fas fa-refresh"></i>
                <span>تحديث البيانات</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Report Options Container -->
      <div class="report-options-container">
        <!-- Quick Report Buttons -->
        <div class="report-options-grid">
          <a href="/admin/request-activity-report?period=3&format=html" class="report-option-btn blue">
            <i class="fas fa-chart-line"></i>
            <span>تقرير طلبات 3 أشهر</span>
          </a>
          <a href="/admin/request-activity-report?period=6&format=html" class="report-option-btn blue">
            <i class="fas fa-chart-bar"></i>
            <span>تقرير طلبات 6 أشهر</span>
          </a>
          <a href="/admin/request-activity-report?period=12&format=html" class="report-option-btn blue">
            <i class="fas fa-chart-area"></i>
            <span>تقرير طلبات سنوي</span>
          </a>
          <a href="/admin/request-activity-report" class="report-option-btn green">
            <i class="fas fa-tools"></i>
            <span>خيارات متقدمة</span>
          </a>
        </div>

        <!-- Export Options -->
        <div class="export-options">
          <div class="export-header">
            <h4 class="export-title">
              <i class="fas fa-download"></i>
              تصدير سريع (آخر 3 أشهر)
            </h4>
          </div>
          <div class="export-content">
            <p class="export-description">
              احصل على تقرير سريع للطلبات لآخر 3 أشهر بصيغ مختلفة للمراجعة أو المشاركة
            </p>
            <div class="export-buttons">
              <a href="/admin/request-activity-report?period=3&format=csv" class="export-btn csv">
                <i class="fas fa-file-csv"></i>
                <span>CSV</span>
              </a>
              <a href="/admin/request-activity-report?period=3&format=pdf" class="export-btn pdf">
                <i class="fas fa-file-pdf"></i>
                <span>PDF</span>
              </a>
              <a href="/admin/request-activity-report?period=3&format=arabic" class="export-btn print" target="_blank">
                <i class="fas fa-print"></i>
                <span>طباعة</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="section">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-filter" style="color: #6b7280;"></i>
          تصفية بيانات الطلبات
        </h2>
      </div>
      <div class="section-content">
        <form method="get" action="/admin/requests-records">
          <div class="filters-grid">
            <div class="form-group">
              <label for="activity_type" class="form-label">نوع النشاط</label>
              <select id="activity_type" name="activity_type" class="form-control">
                <option value="">جميع الأنشطة</option>
                {% for type in activity_types %}
                <option value="{{ type.value }}" {% if filters.activity_type == type.value %}selected{% endif %}>
                  {{ type.get_arabic_name() }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="form-group">
              <label for="status" class="form-label">حالة الطلب</label>
              <select id="status" name="status" class="form-control">
                <option value="">جميع الحالات</option>
                {% for status in request_statuses %}
                <option value="{{ status.value }}" {% if filters.status == status.value %}selected{% endif %}>
                  {{ status.get_arabic_name() }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="form-group">
              <label for="date_from" class="form-label">من تاريخ</label>
              <input type="date" id="date_from" name="date_from" class="form-control" value="{{ filters.date_from or '' }}">
            </div>
            <div class="form-group">
              <label for="date_to" class="form-label">إلى تاريخ</label>
              <input type="date" id="date_to" name="date_to" class="form-control" value="{{ filters.date_to or '' }}">
            </div>
            <div class="form-group">
              <label for="search" class="form-label">بحث</label>
              <input type="text" id="search" name="search" class="form-control" placeholder="رقم الطلب، اسم المبنى..." value="{{ filters.search or '' }}">
            </div>
            <div class="form-group filter-actions">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
                تطبيق الفلتر
              </button>
              <a href="/admin/requests-records" class="btn btn-outline">
                <i class="fas fa-times"></i>
                إعادة ضبط
              </a>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Request Statistics -->
    <div class="section">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-chart-pie" style="color: #6b7280;"></i>
          إحصائيات الطلبات
        </h2>
      </div>
      <div class="section-content">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">{{ request_stats.total_requests }}</div>
            <div class="stat-label">إجمالي الطلبات</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ request_stats.pending_requests }}</div>
            <div class="stat-label">الطلبات المعلقة</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ request_stats.completed_requests }}</div>
            <div class="stat-label">الطلبات المكتملة</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ request_stats.in_progress_requests }}</div>
            <div class="stat-label">الطلبات قيد التنفيذ</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Activities Table -->
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-history" style="color: #6b7280;"></i>
                سجل أنشطة الطلبات
                {% if activities %}
                <span style="background: #f3f4f6; color: #374151; font-size: 12px; font-weight: 500; padding: 4px 8px; border-radius: 12px; margin-right: 8px;">
                    {{ activities|length }} نشاط
                </span>
                {% endif %}
            </h2>
        </div>
        <div class="section-content">
            {% if activities %}
            <div style="overflow-x: auto;">
                <table class="activities-table">
                    <thead>
                        <tr>
                            <th>النوع</th>
                            <th>الوصف</th>
                            <th>رقم الطلب</th>
                            <th>حالة الطلب</th>
                            <th>التاريخ والوقت</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for activity in activities %}
                        <tr>
                            <td>
                                <span class="activity-type request">
                                    <i class="fas fa-file-alt"></i>
                                    {{ activity.activity_type.get_arabic_name() }}
                                </span>
                            </td>
                            <td>{{ activity.description }}</td>
                            <td>
                                {% if activity.details and 'request_number' in activity.details %}
                                    {{ activity.details.request_number }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>
                                {% if activity.details and 'status' in activity.details %}
                                    <span class="activity-type request">
                                        {{ activity.details.status }}
                                    </span>
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>
                                <div style="font-size: 13px;">{{ activity.created_at.strftime('%Y-%m-%d') }}</div>
                                <div style="font-size: 11px; color: #6b7280;">{{ activity.created_at.strftime('%H:%M:%S') }}</div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination.has_prev or pagination.has_next %}
            <div class="pagination">
                {% if pagination.has_prev %}
                <a href="?page={{ pagination.page - 1 }}{% if filters.activity_type %}&activity_type={{ filters.activity_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}{% if filters.search %}&search={{ filters.search }}{% endif %}{% if filters.status %}&status={{ filters.status }}{% endif %}" class="btn btn-outline">
                    <i class="fas fa-chevron-right"></i>
                    السابق
                </a>
                {% endif %}

                <span style="padding: 8px 16px; color: #6b7280; font-size: 14px;">
                    صفحة {{ pagination.page }}
                </span>

                {% if pagination.has_next %}
                <a href="?page={{ pagination.page + 1 }}{% if filters.activity_type %}&activity_type={{ filters.activity_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}{% if filters.search %}&search={{ filters.search }}{% endif %}{% if filters.status %}&status={{ filters.status }}{% endif %}" class="btn btn-outline">
                    التالي
                    <i class="fas fa-chevron-left"></i>
                </a>
                {% endif %}
            </div>
            {% endif %}

            {% else %}
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-history"></i>
                <h3 style="margin: 0 0 8px 0; font-size: 18px; color: #374151;">لا توجد أنشطة</h3>
                <p style="margin: 0; font-size: 14px;">لم يتم العثور على أنشطة تطابق المعايير المحددة</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Professional Admin Requests Records JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin requests records page loaded successfully');

    // Auto-submit form when filters change (optional)
    const filterSelects = document.querySelectorAll('#activity_type, #status');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // Optional: Auto-submit on change
            // this.form.submit();
        });
    });
});
</script>

{% endblock %}
