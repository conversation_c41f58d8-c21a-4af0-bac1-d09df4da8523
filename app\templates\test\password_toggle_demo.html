<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض توضيحي لإظهار/إخفاء كلمة المرور - CMSVS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .rtl { direction: rtl; text-align: right; }
    </style>
</head>
<body class="bg-gray-50 rtl">
    <div class="min-h-screen py-8">
        <!-- Header -->
        <header class="bg-blue-600 text-white shadow-lg mb-8">
            <div class="container mx-auto px-4 py-4">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold">عرض توضيحي لإظهار/إخفاء كلمة المرور</h1>
                    <a href="/dashboard" class="bg-blue-500 px-3 py-1 rounded hover:bg-blue-400">العودة للوحة التحكم</a>
                </div>
            </div>
        </header>

        <div class="container mx-auto px-4 max-w-4xl">
            <!-- Introduction -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold mb-4 text-gray-800">نظرة عامة</h2>
                <p class="text-gray-600 mb-4">
                    تم تطبيق وظيفة إظهار/إخفاء كلمة المرور على جميع حقول كلمات المرور في النظام. 
                    هذه الوظيفة تعمل تلقائياً على جميع حقول كلمات المرور الموجودة والجديدة.
                </p>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-800 mb-2">الميزات:</h3>
                    <ul class="text-blue-700 space-y-1">
                        <li>• تطبيق تلقائي على جميع حقول كلمات المرور</li>
                        <li>• دعم إمكانية الوصول (ARIA labels)</li>
                        <li>• دعم لوحة المفاتيح (Enter و Space)</li>
                        <li>• تصميم متجاوب مع التخطيط العربي (RTL)</li>
                        <li>• مراقبة الحقول المضافة ديناميكياً</li>
                    </ul>
                </div>
            </div>

            <!-- Demo Forms -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Login Style Form -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4 text-gray-800">نموذج تسجيل الدخول</h3>
                    <form class="space-y-4">
                        <div>
                            <label for="demo_username" class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                            <input type="text" id="demo_username" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل اسم المستخدم">
                        </div>
                        <div>
                            <label for="demo_password1" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                            <div class="relative">
                                <input type="password" id="demo_password1" class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل كلمة المرور">
                                <!-- Password toggle will be added automatically -->
                            </div>
                        </div>
                        <button type="button" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">تسجيل الدخول (تجريبي)</button>
                    </form>
                </div>

                <!-- Registration Style Form -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4 text-gray-800">نموذج التسجيل</h3>
                    <form class="space-y-4">
                        <div>
                            <label for="demo_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                            <input type="email" id="demo_email" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="أدخل البريد الإلكتروني">
                        </div>
                        <div>
                            <label for="demo_password2" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                            <div class="relative">
                                <input type="password" id="demo_password2" class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="أدخل كلمة المرور">
                                <!-- Password toggle will be added automatically -->
                            </div>
                        </div>
                        <div>
                            <label for="demo_confirm_password" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور</label>
                            <div class="relative">
                                <input type="password" id="demo_confirm_password" class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="أعد إدخال كلمة المرور">
                                <!-- Password toggle will be added automatically -->
                            </div>
                        </div>
                        <button type="button" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700">إنشاء حساب (تجريبي)</button>
                    </form>
                </div>

                <!-- Settings Style Form -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4 text-gray-800">تغيير كلمة المرور</h3>
                    <form class="space-y-4">
                        <div>
                            <label for="demo_current_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الحالية</label>
                            <div class="relative">
                                <input type="password" id="demo_current_password" class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500" placeholder="أدخل كلمة المرور الحالية">
                                <!-- Password toggle will be added automatically -->
                            </div>
                        </div>
                        <div>
                            <label for="demo_new_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة</label>
                            <div class="relative">
                                <input type="password" id="demo_new_password" class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500" placeholder="أدخل كلمة المرور الجديدة">
                                <!-- Password toggle will be added automatically -->
                            </div>
                        </div>
                        <div>
                            <label for="demo_confirm_new_password" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور الجديدة</label>
                            <div class="relative">
                                <input type="password" id="demo_confirm_new_password" class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500" placeholder="أعد إدخال كلمة المرور الجديدة">
                                <!-- Password toggle will be added automatically -->
                            </div>
                        </div>
                        <button type="button" class="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700">تحديث كلمة المرور (تجريبي)</button>
                    </form>
                </div>

                <!-- Dynamic Form Test -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4 text-gray-800">اختبار الحقول الديناميكية</h3>
                    <div id="dynamic-form-container">
                        <p class="text-gray-600 mb-4">اضغط على الزر لإضافة حقل كلمة مرور جديد ديناميكياً:</p>
                        <button type="button" onclick="addPasswordField()" class="bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 mb-4">
                            <i class="fas fa-plus mr-2"></i>
                            إضافة حقل كلمة مرور
                        </button>
                        <div id="dynamic-fields" class="space-y-4">
                            <!-- Dynamic password fields will be added here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-8">
                <h3 class="text-lg font-bold mb-4 text-gray-800">كيفية الاستخدام</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-700 mb-2">للمستخدمين:</h4>
                        <ul class="text-gray-600 space-y-1">
                            <li>• انقر على أيقونة العين لإظهار كلمة المرور</li>
                            <li>• انقر مرة أخرى لإخفاء كلمة المرور</li>
                            <li>• يمكن استخدام مفتاح Enter أو Space للتبديل</li>
                            <li>• الوظيفة متاحة في جميع النماذج</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-700 mb-2">للمطورين:</h4>
                        <ul class="text-gray-600 space-y-1">
                            <li>• تطبيق تلقائي على جميع حقول type="password"</li>
                            <li>• يتطلب إضافة class="pl-10" للحقل</li>
                            <li>• يتطلب div parent مع class="relative"</li>
                            <li>• يدعم الحقول المضافة ديناميكياً</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the password toggle script -->
    <script src="{{ url_for('static', path='js/password-toggle.js') }}"></script>
    
    <script>
        let fieldCounter = 0;
        
        function addPasswordField() {
            fieldCounter++;
            const container = document.getElementById('dynamic-fields');
            
            const fieldDiv = document.createElement('div');
            fieldDiv.innerHTML = `
                <label for="dynamic_password_${fieldCounter}" class="block text-sm font-medium text-gray-700 mb-2">
                    حقل كلمة مرور ديناميكي #${fieldCounter}
                </label>
                <div class="relative">
                    <input type="password" 
                           id="dynamic_password_${fieldCounter}" 
                           class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" 
                           placeholder="كلمة مرور ديناميكية">
                    <!-- Password toggle will be added automatically -->
                </div>
            `;
            
            container.appendChild(fieldDiv);
        }
    </script>
</body>
</html>
