version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: cmsvs_app_dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app  # Mount source code for hot reload
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    environment:
      - ENVIRONMENT=development
    env_file:
      - .env.development
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cmsvs_network
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  db:
    image: postgres:15-alpine
    container_name: cmsvs_db_dev
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: cmsvs_dev
      POSTGRES_USER: cmsvs_user
      POSTGRES_PASSWORD: admin
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    networks:
      - cmsvs_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cmsvs_user -d cmsvs_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: cmsvs_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - cmsvs_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: cmsvs_nginx_dev
    ports:
      - "80:80"
    volumes:
      - ./nginx/conf.d/app.conf:/etc/nginx/conf.d/default.conf
      - ./app/static:/var/www/static
    depends_on:
      - app
    networks:
      - cmsvs_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local

networks:
  cmsvs_network:
    driver: bridge
