{% extends "base.html" %}

{% block title %}الطلبات المؤرشفة - CMSVS{% endblock %}

{% block content %}
<div class="space-y-4 md:space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold text-gray-900">الطلبات المؤرشفة</h1>
            <p class="text-gray-600 mt-1 md:mt-2 text-sm md:text-base">الطلبات التي تم أرشفتها من النظام</p>
        </div>
        <div class="flex-shrink-0">
            <a href="/admin/requests"
               class="inline-flex items-center justify-center w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm md:text-base">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة للطلبات النشطة
            </a>
        </div>
    </div>

    <!-- Search Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 md:p-6">
        <form method="get" action="/admin/archive" id="searchForm" class="space-y-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div class="lg:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                        البحث في الطلبات المؤرشفة
                    </label>
                    <div class="relative">
                        <input type="text"
                               id="search"
                               name="search"
                               value="{{ current_search or '' }}"
                               placeholder="البحث برقم الطلب، اسم المقدم، أو رقم إجازة البناء..."
                               class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm md:text-base">
                        <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-end gap-2">
                    <button type="submit"
                            class="flex-1 sm:flex-none bg-blue-600 hover:bg-blue-700 text-white px-4 md:px-6 py-2 rounded-lg transition-colors text-sm md:text-base">
                        <i class="fas fa-search mr-2"></i>
                        بحث
                    </button>
                    <a href="/admin/archive"
                       class="flex-1 sm:flex-none text-center bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors text-sm md:text-base">
                        <i class="fas fa-times ml-2"></i>
                        مسح
                    </a>
                </div>
            </div>

            <div class="text-sm text-gray-600">
                {% if current_search %}
                    <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-search text-blue-500"></i>
                            <span>نتائج البحث: "<strong>{{ current_search }}</strong>"</span>
                        </div>
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs w-fit">
                            {{ requests|length }} نتيجة
                        </span>
                    </div>
                {% else %}
                    <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-archive text-gray-500"></i>
                            <span>إجمالي الطلبات المؤرشفة</span>
                        </div>
                        <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs w-fit">
                            {{ requests|length }} طلب مؤرشف
                        </span>
                    </div>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- Requests Table/Cards -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {% if requests %}
        <!-- Desktop Table View -->
        <div class="hidden lg:block overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            رقم الطلب
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            إجازة البناء
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            مقدم الطلب
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            تاريخ الأرشفة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for req in requests %}
                    <tr class="hover:bg-gray-50 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <i class="fas fa-archive text-gray-400 ml-2"></i>
                                <span class="text-sm font-medium text-gray-900">{{ req.request_number }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if req.building_permit_number %}
                                <code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">
                                    {{ req.building_permit_number }}
                                </code>
                            {% else %}
                                <span class="text-gray-400 text-sm">غير محدد</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                        <span class="text-xs font-medium text-gray-600">
                                            {{ req.user.full_name[:2] if req.user.full_name else 'غ' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="mr-3">
                                    <div class="text-sm font-medium text-gray-900">{{ req.user.full_name }}</div>
                                    <div class="text-sm text-gray-500">{{ req.user.email }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="space-y-1">
                                {% if req.status.value == 'PENDING' %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock ml-1"></i>
                                        قيد المراجعة
                                    </span>
                                {% elif req.status.value == 'IN_PROGRESS' %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-cog fa-spin ml-1"></i>
                                        قيد التنفيذ
                                    </span>
                                {% elif req.status.value == 'COMPLETED' %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        مكتمل
                                    </span>
                                {% elif req.status.value == 'REJECTED' %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        مرفوض
                                    </span>
                                {% endif %}
                                <div>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-archive ml-1"></i>
                                        مؤرشف
                                    </span>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="space-y-1">
                                <div>{{ req.updated_at.strftime('%Y-%m-%d') if req.updated_at else req.created_at.strftime('%Y-%m-%d') }}</div>
                                <div class="text-xs text-gray-500">{{ req.updated_at.strftime('%H:%M') if req.updated_at else req.created_at.strftime('%H:%M') }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="/requests/{{ req.id }}"
                                   class="text-blue-600 hover:text-blue-900 transition-colors">
                                    <i class="fas fa-eye"></i>
                                    عرض
                                </a>
                                <form method="post" action="/admin/requests/{{ req.id }}/restore" class="inline">
                                    <button type="submit"
                                            onclick="return confirm('هل أنت متأكد من استعادة هذا الطلب؟')"
                                            class="text-green-600 hover:text-green-900 transition-colors">
                                        <i class="fas fa-undo ml-1"></i>
                                        استعادة
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Mobile Card View -->
        <div class="lg:hidden divide-y divide-gray-200">
            {% for req in requests %}
            <div class="p-4 hover:bg-gray-50 transition-colors">
                <!-- Request Header -->
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-archive text-gray-400 ml-2"></i>
                        <span class="text-sm font-medium text-gray-900">{{ req.request_number }}</span>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <a href="/admin/requests/{{ req.id }}/view"
                           class="text-blue-600 hover:text-blue-900 transition-colors text-sm">
                            <i class="fas fa-eye ml-1"></i>
                            عرض
                        </a>
                        <form method="post" action="/admin/requests/{{ req.id }}/restore" class="inline">
                            <button type="submit"
                                    onclick="return confirm('هل أنت متأكد من استعادة هذا الطلب؟')"
                                    class="text-green-600 hover:text-green-900 transition-colors text-sm">
                                <i class="fas fa-undo ml-1"></i>
                                استعادة
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Request Details -->
                <div class="space-y-3">
                    <!-- Building Permit -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">إجازة البناء:</span>
                        {% if req.building_permit_number %}
                            <code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">
                                {{ req.building_permit_number }}
                            </code>
                        {% else %}
                            <span class="text-gray-400 text-sm">غير محدد</span>
                        {% endif %}
                    </div>

                    <!-- User Info -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">مقدم الطلب:</span>
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-6 w-6 ml-2">
                                <div class="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center">
                                    <span class="text-xs font-medium text-gray-600">
                                        {{ req.user.full_name[:2] if req.user.full_name else 'غ' }}
                                    </span>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">{{ req.user.full_name }}</div>
                                <div class="text-xs text-gray-500">{{ req.user.email }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">الحالة:</span>
                        <div class="flex flex-col items-end space-y-1">
                            {% if req.status.value == 'PENDING' %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock ml-1"></i>
                                    قيد المراجعة
                                </span>
                            {% elif req.status.value == 'IN_PROGRESS' %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-cog fa-spin ml-1"></i>
                                    قيد التنفيذ
                                </span>
                            {% elif req.status.value == 'COMPLETED' %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle ml-1"></i>
                                    مكتمل
                                </span>
                            {% elif req.status.value == 'REJECTED' %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle ml-1"></i>
                                    مرفوض
                                </span>
                            {% endif %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i class="fas fa-archive ml-1"></i>
                                مؤرشف
                            </span>
                        </div>
                    </div>

                    <!-- Archive Date -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">تاريخ الأرشفة:</span>
                        <div class="text-right">
                            <div class="text-sm text-gray-900">{{ req.updated_at.strftime('%Y-%m-%d') if req.updated_at else req.created_at.strftime('%Y-%m-%d') }}</div>
                            <div class="text-xs text-gray-500">{{ req.updated_at.strftime('%H:%M') if req.updated_at else req.created_at.strftime('%H:%M') }}</div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8 md:py-12 px-4">
            <i class="fas fa-archive text-gray-400 text-3xl md:text-4xl mb-4"></i>
            <h3 class="text-base md:text-lg font-medium text-gray-900 mb-2">لا توجد طلبات مؤرشفة</h3>
            <p class="text-gray-500 text-sm md:text-base">لم يتم أرشفة أي طلبات حتى الآن</p>
        </div>
        {% endif %}
    </div>

    <!-- Info Cards -->
    {% if requests %}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 md:p-6">
            <div class="flex items-center mb-3">
                <i class="fas fa-info-circle text-blue-600 text-lg md:text-xl ml-2 md:ml-3"></i>
                <h3 class="text-base md:text-lg font-semibold text-blue-900">معلومات الأرشفة</h3>
            </div>
            <p class="text-blue-800 text-sm md:text-base leading-relaxed">
                الطلبات المؤرشفة لا تظهر في القوائم العادية ولكن يمكن استعادتها في أي وقت.
                جميع البيانات والملفات المرتبطة بالطلب محفوظة بأمان.
            </p>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-xl p-4 md:p-6">
            <div class="flex items-center mb-3">
                <i class="fas fa-shield-alt text-green-600 text-lg md:text-xl ml-2 md:ml-3"></i>
                <h3 class="text-base md:text-lg font-semibold text-green-900">الأمان والخصوصية</h3>
            </div>
            <p class="text-green-800 text-sm md:text-base leading-relaxed">
                جميع البيانات والملفات محفوظة بأمان ويمكن الوصول إليها عند الحاجة.
                عملية الأرشفة قابلة للعكس بالكامل.
            </p>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}