{% extends "base.html" %}

{% block title %}{{ organization.name }} - إرشيف{% endblock %}

{% block content %}
<style>
/* Professional Organization Detail Styling */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 24px;
}

.org-info {
    flex: 1;
}

.org-title {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
}

.org-address {
    font-size: 16px;
    color: #6b7280;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.org-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #6b7280;
}

.meta-icon {
    color: #9ca3af;
    width: 16px;
}

.header-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.breadcrumb {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
    font-size: 14px;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.stats-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
}

.stat-card {
    text-align: center;
    padding: 20px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    display: block;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
}

.applications-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-icon {
    color: #3b82f6;
}

.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.application-card {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s;
}

.application-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.app-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.app-id {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.app-date {
    font-size: 12px;
    color: #6b7280;
}

.app-details {
    margin-bottom: 16px;
}

.app-detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    font-size: 14px;
    color: #374151;
}

.app-detail-icon {
    width: 14px;
    color: #9ca3af;
}

.app-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.empty-state {
    text-align: center;
    padding: 64px 32px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.empty-state-text {
    margin-bottom: 24px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .page-header {
        padding: 24px;
    }
    
    .header-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .org-meta {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .applications-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .app-actions {
        flex-direction: column;
    }
}
</style>

<div class="page-container">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/organizations/">إدارة المؤسسات</a> / {{ organization.name }}
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="org-info">
                <h1 class="org-title">{{ organization.name }}</h1>
                <div class="org-address">
                    <i class="fas fa-map-marker-alt"></i>
                    {{ organization.full_address }}
                </div>
                
                {% if organization.description %}
                <p style="color: #6b7280; margin: 8px 0;">{{ organization.description }}</p>
                {% endif %}
                
                <div class="org-meta">
                    {% if organization.contact_person %}
                    <div class="meta-item">
                        <i class="fas fa-user meta-icon"></i>
                        <span>{{ organization.contact_person }}</span>
                    </div>
                    {% endif %}
                    
                    {% if organization.contact_phone %}
                    <div class="meta-item">
                        <i class="fas fa-phone meta-icon"></i>
                        <span>{{ organization.contact_phone }}</span>
                    </div>
                    {% endif %}
                    
                    {% if organization.contact_email %}
                    <div class="meta-item">
                        <i class="fas fa-envelope meta-icon"></i>
                        <span>{{ organization.contact_email }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="meta-item">
                        <i class="fas fa-calendar meta-icon"></i>
                        <span>تاريخ الإنشاء: {{ organization.created_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                </div>
            </div>
            
            <div class="header-actions">
                <a href="/organizations/{{ organization.id }}/applications/new" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    طلب جديد
                </a>
                <a href="/organizations/{{ organization.id }}/edit" class="btn btn-secondary">
                    <i class="fas fa-edit"></i>
                    تعديل المؤسسة
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Section -->
    <div class="stats-section">
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number">{{ applications|length if applications else 0 }}</span>
                <span class="stat-label">إجمالي الطلبات</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ organization.created_at.strftime('%Y-%m-%d') if organization.created_at else 'غير محدد' }}</span>
                <span class="stat-label">تاريخ الإنشاء</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ 'نشط' if organization.is_active else 'غير نشط' }}</span>
                <span class="stat-label">الحالة</span>
            </div>
        </div>
    </div>

    <!-- Applications Section -->
    <div class="applications-section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-file-alt section-icon"></i>
                الطلبات
            </h2>
            <a href="/organizations/{{ organization.id }}/applications/new" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة طلب جديد
            </a>
        </div>

        {% if applications %}
        <div class="applications-grid">
            {% for app in applications %}
            <div class="application-card">
                <div class="app-header">
                    <h3 class="app-id">{{ app.application_id }}</h3>
                    <span class="app-date">{{ app.created_at.strftime('%Y-%m-%d') }}</span>
                </div>
                
                <div class="app-details">
                    <div class="app-detail-item">
                        <i class="fas fa-user app-detail-icon"></i>
                        <span>{{ app.name }}</span>
                    </div>
                    <div class="app-detail-item">
                        <i class="fas fa-phone app-detail-icon"></i>
                        <span>{{ app.mobile }}</span>
                    </div>
                    {% if app.benayat %}
                    <div class="app-detail-item">
                        <i class="fas fa-info-circle app-detail-icon"></i>
                        <span>{{ app.benayat }}</span>
                    </div>
                    {% endif %}
                    <div class="app-detail-item">
                        <i class="fas fa-paperclip app-detail-icon"></i>
                        <span>{{ app.attachment_count }} مرفق</span>
                    </div>
                </div>
                
                <div class="app-actions">
                    <a href="/organizations/{{ organization.id }}/applications/{{ app.id }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <h3 class="empty-state-title">لا توجد طلبات</h3>
            <p class="empty-state-text">لم يتم تقديم أي طلبات لهذه المؤسسة بعد</p>
            <a href="/organizations/{{ organization.id }}/applications/new" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة أول طلب
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
