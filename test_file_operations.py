#!/usr/bin/env python3
"""
Test script to debug file upload/download operations
"""

import sys
import os
import tempfile
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_file_operations():
    """Test file upload and download operations"""
    print("📁 Testing File Operations...")
    print("=" * 50)
    
    try:
        # Test database connection
        print("📊 Testing database connection...")
        from app.database import SessionLocal
        from app.services.organization_service import OrganizationService, ApplicationService
        from app.models.user import User, UserRole
        
        db = SessionLocal()
        print("✅ Database connection successful")
        
        # Get admin user
        admin_user = db.query(User).filter(User.role == UserRole.ADMIN).first()
        if not admin_user:
            print("❌ No admin user found. Please create an admin user first.")
            return False
        
        print(f"✅ Found admin user: {admin_user.username}")
        
        # Create test organization
        print("\n🏢 Creating test organization...")
        test_org = OrganizationService.create_organization(
            db=db,
            name=f"Test File Org {int(time.time())}",
            building="Test Building",
            street="Test Street",
            block="Test Block"
        )
        print(f"✅ Test organization created: {test_org.name} (ID: {test_org.id})")
        
        # Create test application
        print("\n📝 Creating test application...")
        test_app = ApplicationService.create_application(
            db=db,
            organization_id=test_org.id,
            name="Test Applicant",
            mobile="12345678"
        )
        print(f"✅ Test application created: {test_app.application_id} (ID: {test_app.id})")
        
        # Create a test file
        print("\n📄 Creating test file...")
        test_content = "This is a test file for upload/download testing."
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name
        
        print(f"✅ Test file created: {temp_file_path}")
        
        # Test file storage directory creation
        print("\n📂 Testing file storage...")
        upload_dir = ApplicationService.get_upload_directory(test_org.name, test_app.application_id)
        print(f"✅ Upload directory: {upload_dir}")
        print(f"   Directory exists: {os.path.exists(upload_dir)}")
        
        # Simulate file upload by copying the test file
        print("\n⬆️ Simulating file upload...")
        import shutil
        
        # Create the target file path
        target_file_path = os.path.join(upload_dir, "attachment_1.txt")
        shutil.copy2(temp_file_path, target_file_path)
        
        # Update the application record
        test_app.attachment1_path = target_file_path
        test_app.attachment1_filename = "test_file.txt"
        db.commit()
        db.refresh(test_app)
        
        print(f"✅ File uploaded to: {target_file_path}")
        print(f"   File exists: {os.path.exists(target_file_path)}")
        print(f"   File size: {os.path.getsize(target_file_path)} bytes")
        print(f"   File readable: {os.access(target_file_path, os.R_OK)}")
        
        # Test file path retrieval
        print("\n🔍 Testing file path retrieval...")
        retrieved_path = ApplicationService.get_file_path(db, test_app.id, 1)
        print(f"✅ Retrieved file path: {retrieved_path}")
        print(f"   Matches uploaded path: {retrieved_path == target_file_path}")
        
        # Test file download simulation
        print("\n⬇️ Testing file download simulation...")
        if retrieved_path and os.path.exists(retrieved_path):
            with open(retrieved_path, 'r') as f:
                downloaded_content = f.read()
            print(f"✅ File content matches: {downloaded_content == test_content}")
        else:
            print("❌ File not found for download")
        
        # Test URL construction
        print("\n🔗 Testing download URL...")
        download_url = f"/organizations/{test_org.id}/applications/{test_app.id}/files/1"
        debug_url = f"/organizations/debug/file-info/{test_org.id}/applications/{test_app.id}/files/1"
        
        print(f"✅ Download URL: {download_url}")
        print(f"✅ Debug URL: {debug_url}")
        
        # Clean up
        print("\n🧹 Cleaning up...")
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        if os.path.exists(target_file_path):
            os.unlink(target_file_path)
        
        db.delete(test_app)
        db.delete(test_org)
        db.commit()
        
        # Try to remove upload directory if empty
        try:
            os.rmdir(upload_dir)
            org_dir = os.path.dirname(upload_dir)
            if os.path.exists(org_dir) and not os.listdir(org_dir):
                os.rmdir(org_dir)
        except OSError:
            pass  # Directory not empty or other issue
        
        print("✅ Cleanup completed")
        
        db.close()
        
        print("\n🎉 File Operations Test Summary:")
        print("✅ Organization creation")
        print("✅ Application creation")
        print("✅ File upload directory creation")
        print("✅ File storage simulation")
        print("✅ File path retrieval")
        print("✅ File download simulation")
        
        print(f"\n🔧 To test the actual download endpoint:")
        print(f"1. Create an organization and application through the web interface")
        print(f"2. Upload a file to the application")
        print(f"3. Use the debug URL to check file info: {debug_url}")
        print(f"4. Use the download URL to download the file: {download_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    import time
    success = test_file_operations()
    sys.exit(0 if success else 1)
