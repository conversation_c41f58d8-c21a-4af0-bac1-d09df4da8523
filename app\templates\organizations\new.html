{% extends "base.html" %}

{% block title %}إضافة مؤسسة جديدة - إرشيف{% endblock %}

{% block content %}
<style>
/* Professional New Organization Form Styling */
.page-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
}

.page-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

.breadcrumb {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
    font-size: 14px;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.form-container {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-icon {
    color: #3b82f6;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.form-label.required::after {
    content: " *";
    color: #ef4444;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s;
    background: #ffffff;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control.error {
    border-color: #ef4444;
}

.form-control.error:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    margin-top: 32px;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    min-width: 120px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.success-message {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-help {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .page-header {
        padding: 24px;
    }
    
    .form-container {
        padding: 24px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">إضافة مؤسسة جديدة</h1>
        <p class="page-subtitle">إضافة مؤسسة جديدة لإدارة الطلبات والملفات</p>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/organizations/">إدارة المؤسسات</a> / إضافة مؤسسة جديدة
    </div>

    <!-- Form Container -->
    <div class="form-container">
        {% if error %}
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
        </div>
        {% endif %}

        <form method="post" action="/organizations/new">
            <!-- Basic Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-building section-icon"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name" class="form-label required">اسم المؤسسة</label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            class="form-control" 
                            value="{{ form_data.name if form_data else '' }}"
                            required
                            placeholder="أدخل اسم المؤسسة"
                        >
                        <div class="form-help">اسم المؤسسة يجب أن يكون فريداً في النظام</div>
                    </div>
                </div>
            </div>

            <!-- Address Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-map-marker-alt section-icon"></i>
                    معلومات العنوان
                </h3>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="building" class="form-label required">المبنى</label>
                        <input 
                            type="text" 
                            id="building" 
                            name="building" 
                            class="form-control" 
                            value="{{ form_data.building if form_data else '' }}"
                            required
                            placeholder="أدخل اسم أو رقم المبنى"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="street" class="form-label required">الشارع</label>
                        <input 
                            type="text" 
                            id="street" 
                            name="street" 
                            class="form-control" 
                            value="{{ form_data.street if form_data else '' }}"
                            required
                            placeholder="أدخل اسم الشارع"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="block" class="form-label required">المجمع</label>
                        <input 
                            type="text" 
                            id="block" 
                            name="block" 
                            class="form-control" 
                            value="{{ form_data.block if form_data else '' }}"
                            required
                            placeholder="أدخل رقم أو اسم المجمع"
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description" class="form-label">وصف المؤسسة</label>
                    <textarea 
                        id="description" 
                        name="description" 
                        class="form-control form-textarea"
                        placeholder="أدخل وصف اختياري للمؤسسة"
                    >{{ form_data.description if form_data else '' }}</textarea>
                    <div class="form-help">وصف اختياري لتوضيح طبيعة عمل المؤسسة</div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-address-book section-icon"></i>
                    معلومات الاتصال (اختيارية)
                </h3>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="contact_person" class="form-label">الشخص المسؤول</label>
                        <input 
                            type="text" 
                            id="contact_person" 
                            name="contact_person" 
                            class="form-control" 
                            value="{{ form_data.contact_person if form_data else '' }}"
                            placeholder="اسم الشخص المسؤول"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="contact_phone" class="form-label">رقم الهاتف</label>
                        <input 
                            type="tel" 
                            id="contact_phone" 
                            name="contact_phone" 
                            class="form-control" 
                            value="{{ form_data.contact_phone if form_data else '' }}"
                            placeholder="رقم الهاتف"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="contact_email" class="form-label">البريد الإلكتروني</label>
                        <input 
                            type="email" 
                            id="contact_email" 
                            name="contact_email" 
                            class="form-control" 
                            value="{{ form_data.contact_email if form_data else '' }}"
                            placeholder="البريد الإلكتروني"
                        >
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ المؤسسة
                </button>
                <a href="/organizations/" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const requiredFields = form.querySelectorAll('[required]');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('error');
                isValid = false;
            } else {
                field.classList.remove('error');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
    
    // Remove error class on input
    requiredFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value.trim()) {
                this.classList.remove('error');
            }
        });
    });
});
</script>
{% endblock %}
