{% extends "base.html" %}

{% block title %}إدارة ملفات الطلب {{ req.request_number }} - لوحة التحكم{% endblock %}

{% block content %}
<style>
/* Professional Admin File Management Styling */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    text-align: right;
    padding: 32px 24px;
    margin-bottom: 32px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: #000000;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.page-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.breadcrumb {
    background: #ffffff;
    padding: 16px 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.section {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    overflow: hidden;
}

.section-header {
    background: #f8fafc;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.section-content {
    padding: 24px;
}

.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.file-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

.file-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-size: 18px;
    color: white;
}

.file-icon.pdf { background: #ef4444; }
.file-icon.image { background: #10b981; }
.file-icon.document { background: #3b82f6; }
.file-icon.default { background: #6b7280; }

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
    font-size: 14px;
    line-height: 1.4;
    word-break: break-word;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.file-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;
    font-size: 12px;
    color: #6b7280;
}

.file-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.file-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-outline {
    background: transparent;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn-outline:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d1d5db;
}

.empty-state h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.empty-state p {
    font-size: 14px;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.action-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.action-buttons .btn {
    padding: 12px 20px;
    font-size: 14px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .page-header {
        padding: 24px 16px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .file-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        justify-content: center;
    }
}

/* Image Preview Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.active {
    display: flex;
}

.modal-content {
    max-width: 90%;
    max-height: 90%;
    position: relative;
}

.modal-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
}

.modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 8px;
}

.modal-close:hover {
    color: #ccc;
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">إدارة ملفات الطلب</h1>
        <p class="page-subtitle">
            رقم الطلب: <strong>{{ req.request_number }}</strong> | 
            المستخدم: <strong>{{ request_owner.full_name or request_owner.username }}</strong>
        </p>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/admin">لوحة التحكم</a> / 
        <a href="/admin/requests">إدارة الطلبات</a> / 
        <a href="/admin/requests/{{ req.id }}/view">تفاصيل الطلب</a> / 
        <span>إدارة الملفات</span>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="/admin/requests/{{ req.id }}/view" class="btn btn-outline">
            <i class="fas fa-arrow-right"></i>
            العودة لتفاصيل الطلب
        </a>
        <a href="/admin/requests/{{ req.id }}/edit" class="btn btn-primary">
            <i class="fas fa-edit"></i>
            تعديل الطلب
        </a>
        {% if req.files %}
        <button onclick="downloadSelected()" class="btn btn-success">
            <i class="fas fa-download"></i>
            تحميل الملفات المحددة
        </button>
        {% endif %}
    </div>

    <!-- File Statistics -->
    {% if req.files %}
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">إحصائيات الملفات</h2>
        </div>
        <div class="section-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ req.files|length }}</div>
                    <div class="stat-label">إجمالي الملفات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ req.files|selectattr('file_type', 'equalto', 'pdf')|list|length }}</div>
                    <div class="stat-label">ملفات PDF</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ req.files|selectattr('file_type', 'in', ['jpg', 'jpeg', 'png', 'gif'])|list|length }}</div>
                    <div class="stat-label">الصور</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">
                        {% set file_sizes = [] %}
                        {% for file in req.files %}
                            {% if file.file_size and file.file_size > 0 %}
                                {% set _ = file_sizes.append(file.file_size) %}
                            {% endif %}
                        {% endfor %}
                        {{ "%.1f"|format(file_sizes|sum / 1024 / 1024) }} MB
                    </div>
                    <div class="stat-label">الحجم الإجمالي</div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Files Section -->
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">
                ملفات الطلب
                {% if req.files %}
                <span class="text-sm text-gray-500">({{ req.files|length }} ملف)</span>
                {% endif %}
            </h2>
        </div>
        <div class="section-content">
            {% if req.files %}
            <div class="file-grid">
                {% for file in req.files %}
                <div class="file-card">
                    <div class="file-header">
                        <div class="file-icon {% if file.file_type == 'pdf' %}pdf{% elif file.file_type in ['jpg', 'jpeg', 'png', 'gif'] %}image{% elif file.file_type in ['doc', 'docx'] %}document{% else %}default{% endif %}">
                            {% if file.file_type == 'pdf' %}
                            <i class="fas fa-file-pdf"></i>
                            {% elif file.file_type in ['jpg', 'jpeg', 'png', 'gif'] %}
                            <i class="fas fa-image"></i>
                            {% elif file.file_type in ['doc', 'docx'] %}
                            <i class="fas fa-file-word"></i>
                            {% else %}
                            <i class="fas fa-file"></i>
                            {% endif %}
                        </div>
                        <div class="file-info">
                            <h3 class="file-name">{{ file.original_filename }}</h3>
                            <input type="checkbox" class="file-checkbox" value="{{ file.id }}" style="margin-top: 4px;">
                        </div>
                    </div>

                    <!-- File Metadata -->
                    <div class="file-meta">
                        <span><i class="fas fa-tag"></i>{{ file.file_category or 'غير محدد' }}</span>
                        <span><i class="fas fa-hdd"></i>
                            {% if file.file_size and file.file_size > 0 %}
                                {% if file.file_size >= 1024 * 1024 %}
                                    {{ "%.1f"|format(file.file_size / 1024 / 1024) }} MB
                                {% else %}
                                    {{ "%.1f"|format(file.file_size / 1024) }} KB
                                {% endif %}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </span>
                        <span><i class="fas fa-calendar"></i>{{ file.uploaded_at.strftime('%Y-%m-%d') if file.uploaded_at else 'غير محدد' }}</span>
                    </div>

                    <!-- File Actions -->
                    <div class="file-actions">
                        {% if file.stored_filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) %}
                        <button onclick="previewImage('{{ file.stored_filename }}', '/files/view/{{ file.id }}')" class="btn btn-outline" title="معاينة الصورة">
                            <i class="fas fa-search-plus"></i>
                            معاينة
                        </button>
                        {% endif %}
                        {% if file.file_path %}
                        <a href="/files/view/{{ file.id }}" target="_blank" class="btn btn-primary" title="عرض الملف">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>
                        {% endif %}
                        <a href="/files/download/{{ file.id }}" class="btn btn-success" title="تحميل الملف">
                            <i class="fas fa-download"></i>
                            تحميل
                        </a>
                        <button onclick="confirmDeleteFile({{ file.id }}, '{{ file.original_filename }}')" class="btn btn-danger" title="حذف الملف">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <i class="fas fa-folder-open"></i>
                <h3>لا توجد ملفات</h3>
                <p>لم يتم رفع أي ملفات لهذا الطلب بعد</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div id="imageModal" class="modal">
    <div class="modal-content">
        <button class="modal-close" onclick="closeImagePreview()">&times;</button>
        <img id="modalImage" src="" alt="معاينة الصورة">
    </div>
</div>

<script>
// File management functions
function previewImage(filename, viewUrl) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');

    modalImage.src = viewUrl;
    modal.classList.add('active');
}

function closeImagePreview() {
    const modal = document.getElementById('imageModal');
    modal.classList.remove('active');
}

// Close modal when clicking outside
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImagePreview();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImagePreview();
    }
});

// Download selected files
function downloadSelected() {
    const selectedCheckboxes = document.querySelectorAll('.file-checkbox:checked');
    const fileIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (fileIds.length === 0) {
        alert('يرجى تحديد ملف واحد على الأقل');
        return;
    }

    // Create multiple download links
    fileIds.forEach(fileId => {
        const link = document.createElement('a');
        link.href = `/files/download/${fileId}`;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}

// Confirm file deletion
function confirmDeleteFile(fileId, filename) {
    if (confirm(`هل أنت متأكد من حذف الملف "${filename}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        deleteFile(fileId);
    }
}

// Delete file function
async function deleteFile(fileId) {
    try {
        const response = await fetch(`/admin/api/files/${fileId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            // Show success message
            alert('تم حذف الملف بنجاح');
            // Reload the page to reflect changes
            window.location.reload();
        } else {
            alert('فشل في حذف الملف: ' + (data.error || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error deleting file:', error);
        alert('حدث خطأ أثناء حذف الملف');
    }
}

// Select/Deselect all files
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.file-checkbox');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });

    selectAllBtn.textContent = allChecked ? 'تحديد الكل' : 'إلغاء التحديد';
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add select all button if there are files
    const fileGrid = document.querySelector('.file-grid');
    if (fileGrid) {
        const actionButtons = document.querySelector('.action-buttons');
        const selectAllBtn = document.createElement('button');
        selectAllBtn.id = 'selectAllBtn';
        selectAllBtn.className = 'btn btn-outline';
        selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
        selectAllBtn.onclick = toggleSelectAll;
        actionButtons.appendChild(selectAllBtn);
    }
});
</script>
{% endblock %}
