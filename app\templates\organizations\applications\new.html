{% extends "base.html" %}

{% block title %}طلب جديد - {{ organization.name }} - إرشيف{% endblock %}

{% block content %}
<style>
/* Professional Application Form Styling */
.page-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
}

.page-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

.organization-info {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.org-icon {
    color: #0284c7;
    font-size: 20px;
}

.org-details h4 {
    margin: 0 0 4px 0;
    color: #0c4a6e;
    font-weight: 600;
}

.org-details p {
    margin: 0;
    color: #0369a1;
    font-size: 14px;
}

.breadcrumb {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
    font-size: 14px;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.form-container {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-icon {
    color: #3b82f6;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.form-label.required::after {
    content: " *";
    color: #ef4444;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s;
    background: #ffffff;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.file-upload-section {
    background: #f9fafb;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.2s;
}

.file-upload-section:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.file-upload-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

.file-upload-item {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
}

.file-upload-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.file-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 12px;
    background: #ffffff;
}

.file-input:focus {
    outline: none;
    border-color: #3b82f6;
}

.file-help {
    font-size: 11px;
    color: #6b7280;
    margin-top: 4px;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    margin-top: 32px;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    min-width: 120px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-help {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .page-header {
        padding: 24px;
    }
    
    .form-container {
        padding: 24px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .file-upload-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">طلب جديد</h1>
        <p class="page-subtitle">إضافة طلب جديد للمؤسسة</p>
    </div>

    <!-- Organization Info -->
    <div class="organization-info">
        <i class="fas fa-building org-icon"></i>
        <div class="org-details">
            <h4>{{ organization.name }}</h4>
            <p>{{ organization.full_address }}</p>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/organizations/">إدارة المؤسسات</a> / 
        <a href="/organizations/{{ organization.id }}">{{ organization.name }}</a> / 
        طلب جديد
    </div>

    <!-- Form Container -->
    <div class="form-container">
        {% if error %}
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
        </div>
        {% endif %}

        <form method="post" action="/organizations/{{ organization.id }}/applications/new" enctype="multipart/form-data">
            <!-- Applicant Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-user section-icon"></i>
                    معلومات مقدم الطلب
                </h3>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name" class="form-label required">اسم مقدم الطلب</label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            class="form-control" 
                            value="{{ form_data.name if form_data else '' }}"
                            required
                            placeholder="أدخل الاسم الكامل"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="mobile" class="form-label required">رقم الهاتف</label>
                        <input 
                            type="tel" 
                            id="mobile" 
                            name="mobile" 
                            class="form-control" 
                            value="{{ form_data.mobile if form_data else '' }}"
                            required
                            placeholder="أدخل رقم الهاتف"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="benayat" class="form-label">البيانات</label>
                        <input 
                            type="text" 
                            id="benayat" 
                            name="benayat" 
                            class="form-control" 
                            value="{{ form_data.benayat if form_data else '' }}"
                            placeholder="بيانات إضافية (اختياري)"
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea 
                        id="notes" 
                        name="notes" 
                        class="form-control form-textarea"
                        placeholder="أدخل أي ملاحظات إضافية"
                    >{{ form_data.notes if form_data else '' }}</textarea>
                    <div class="form-help">ملاحظات اختيارية حول الطلب</div>
                </div>
            </div>

            <!-- File Attachments Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-paperclip section-icon"></i>
                    المرفقات
                </h3>
                
                <div class="file-upload-section">
                    <div class="file-upload-grid">
                        <div class="file-upload-item">
                            <label for="attachment1" class="file-upload-label">المرفق الأول</label>
                            <input 
                                type="file" 
                                id="attachment1" 
                                name="attachment1" 
                                class="file-input"
                                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                            >
                            <div class="file-help">PDF, DOC, صور (حد أقصى 10MB)</div>
                        </div>
                        
                        <div class="file-upload-item">
                            <label for="attachment2" class="file-upload-label">المرفق الثاني</label>
                            <input 
                                type="file" 
                                id="attachment2" 
                                name="attachment2" 
                                class="file-input"
                                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                            >
                            <div class="file-help">PDF, DOC, صور (حد أقصى 10MB)</div>
                        </div>
                        
                        <div class="file-upload-item">
                            <label for="attachment3" class="file-upload-label">المرفق الثالث</label>
                            <input 
                                type="file" 
                                id="attachment3" 
                                name="attachment3" 
                                class="file-input"
                                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                            >
                            <div class="file-help">PDF, DOC, صور (حد أقصى 10MB)</div>
                        </div>
                        
                        <div class="file-upload-item">
                            <label for="attachment4" class="file-upload-label">المرفق الرابع</label>
                            <input 
                                type="file" 
                                id="attachment4" 
                                name="attachment4" 
                                class="file-input"
                                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                            >
                            <div class="file-help">PDF, DOC, صور (حد أقصى 10MB)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ الطلب
                </button>
                <a href="/organizations/{{ organization.id }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation and file handling
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const fileInputs = form.querySelectorAll('input[type="file"]');
    const maxFileSize = 10 * 1024 * 1024; // 10MB
    
    // File validation
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                if (file.size > maxFileSize) {
                    alert('حجم الملف كبير جداً. الحد الأقصى 10MB');
                    this.value = '';
                    return;
                }
                
                // Show file name
                const label = this.previousElementSibling;
                label.textContent = `${label.textContent.split(' - ')[0]} - ${file.name}`;
            }
        });
    });
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const mobile = document.getElementById('mobile').value.trim();
        
        if (!name) {
            e.preventDefault();
            alert('يرجى إدخال اسم مقدم الطلب');
            document.getElementById('name').focus();
            return;
        }
        
        if (!mobile) {
            e.preventDefault();
            alert('يرجى إدخال رقم الهاتف');
            document.getElementById('mobile').focus();
            return;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    });
});
</script>
{% endblock %}
