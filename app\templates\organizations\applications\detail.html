{% extends "base.html" %}

{% block title %}{{ application.application_id }} - {{ organization.name }} - إرشيف{% endblock %}

{% block content %}
<style>
/* Professional Application Detail Styling */
.page-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.app-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
}

.app-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

.organization-info {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.org-icon {
    color: #0284c7;
    font-size: 20px;
}

.org-details h4 {
    margin: 0 0 4px 0;
    color: #0c4a6e;
    font-weight: 600;
}

.org-details p {
    margin: 0;
    color: #0369a1;
    font-size: 14px;
}

.breadcrumb {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
    font-size: 14px;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 24px;
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.info-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-icon {
    color: #3b82f6;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
}

.info-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 500;
}

.notes-section {
    margin-top: 16px;
}

.notes-content {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    font-size: 14px;
    color: #374151;
    line-height: 1.5;
}

.files-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.file-item {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: all 0.2s;
}

.file-item.has-file {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.file-item.has-file:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.file-icon {
    font-size: 32px;
    margin-bottom: 8px;
    color: #9ca3af;
}

.file-item.has-file .file-icon {
    color: #3b82f6;
}

.file-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.file-name {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 12px;
    word-break: break-all;
}

.file-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-view {
    background: #10b981;
    color: white;
    border: 1px solid #059669;
}

.btn-view:hover {
    background: #059669;
    transform: translateY(-1px);
}

.action-buttons {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-buttons .btn {
    width: 100%;
    justify-content: center;
    margin-bottom: 8px;
    padding: 12px;
    font-size: 14px;
}

.meta-info {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
    font-size: 14px;
}

.meta-item:last-child {
    border-bottom: none;
}

.meta-label {
    color: #6b7280;
    font-weight: 500;
}

.meta-value {
    color: #1f2937;
}

.empty-file {
    color: #9ca3af;
    font-style: italic;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        order: -1;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .files-grid {
        grid-template-columns: 1fr;
    }
    
    .file-actions {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 6px;
    }

    .file-actions .btn {
        flex: 1;
        min-width: 70px;
        font-size: 11px;
        padding: 6px 8px;
    }
}
</style>

<div class="page-container">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/organizations/">إدارة المؤسسات</a> / 
        <a href="/organizations/{{ organization.id }}">{{ organization.name }}</a> / 
        {{ application.application_id }}
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="app-title">{{ application.application_id }}</h1>
        <p class="app-subtitle">تفاصيل الطلب</p>
    </div>

    <!-- Organization Info -->
    <div class="organization-info">
        <i class="fas fa-building org-icon"></i>
        <div class="org-details">
            <h4>{{ organization.name }}</h4>
            <p>{{ organization.full_address }}</p>
        </div>
    </div>

    <!-- Content Grid -->
    <div class="content-grid">
        <!-- Main Content -->
        <div class="main-content">
            <!-- Application Information -->
            <div class="info-section">
                <h3 class="section-title">
                    <i class="fas fa-user section-icon"></i>
                    معلومات الطلب
                </h3>
                
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">رقم الطلب</span>
                        <span class="info-value">{{ application.application_id }}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">اسم مقدم الطلب</span>
                        <span class="info-value">{{ application.name }}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">رقم الهاتف</span>
                        <span class="info-value">{{ application.mobile }}</span>
                    </div>
                    
                    {% if application.benayat %}
                    <div class="info-item">
                        <span class="info-label">البيانات</span>
                        <span class="info-value">{{ application.benayat }}</span>
                    </div>
                    {% endif %}
                </div>
                
                {% if application.notes %}
                <div class="notes-section">
                    <h4 style="margin: 16px 0 8px 0; color: #374151; font-size: 16px;">الملاحظات</h4>
                    <div class="notes-content">
                        {{ application.notes }}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- File Attachments -->
            <div class="files-section">
                <h3 class="section-title">
                    <i class="fas fa-paperclip section-icon"></i>
                    المرفقات ({{ application.attachment_count }})
                </h3>
                
                <div class="files-grid">
                    {% for attachment in application.attachments %}
                    <div class="file-item {% if attachment.exists %}has-file{% endif %}">
                        <div class="file-icon">
                            {% if attachment.exists %}
                            <i class="fas fa-file-alt"></i>
                            {% else %}
                            <i class="fas fa-file-plus"></i>
                            {% endif %}
                        </div>
                        
                        <div class="file-title">المرفق {{ attachment.index }}</div>
                        
                        {% if attachment.exists %}
                        <div class="file-name">{{ attachment.filename }}</div>
                        <div class="file-actions">
                            <a href="/organizations/{{ organization.id }}/applications/{{ application.id }}/files/{{ attachment.index }}/view"
                               class="btn btn-view" target="_blank" title="عرض الملف">
                                <i class="fas fa-eye"></i>
                                عرض
                            </a>
                            <a href="/organizations/{{ organization.id }}/applications/{{ application.id }}/files/{{ attachment.index }}"
                               class="btn btn-primary" download title="تحميل الملف">
                                <i class="fas fa-download"></i>
                                تحميل
                            </a>
                            <button onclick="deleteFile({{ attachment.index }})" class="btn btn-danger" title="حذف الملف">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                        {% else %}
                        <div class="file-name empty-file">لا يوجد ملف</div>
                        <div class="file-actions">
                            <label for="file-{{ attachment.index }}" class="btn btn-secondary" style="cursor: pointer;">
                                <i class="fas fa-upload"></i>
                                رفع ملف
                            </label>
                            <input type="file" id="file-{{ attachment.index }}" style="display: none;" 
                                   onchange="uploadFile({{ attachment.index }}, this)"
                                   accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif">
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Action Buttons -->
            <div class="action-buttons">
                <h4 style="margin: 0 0 16px 0; color: #374151; font-size: 16px;">الإجراءات</h4>
                <a href="/organizations/{{ organization.id }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للمؤسسة
                </a>
                <a href="/organizations/{{ organization.id }}/applications/new" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    طلب جديد
                </a>
            </div>

            <!-- Meta Information -->
            <div class="meta-info">
                <h4 style="margin: 0 0 16px 0; color: #374151; font-size: 16px;">معلومات إضافية</h4>
                
                <div class="meta-item">
                    <span class="meta-label">تاريخ الإنشاء</span>
                    <span class="meta-value">{{ application.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                
                {% if application.updated_at %}
                <div class="meta-item">
                    <span class="meta-label">آخر تحديث</span>
                    <span class="meta-value">{{ application.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                {% endif %}
                
                <div class="meta-item">
                    <span class="meta-label">عدد المرفقات</span>
                    <span class="meta-value">{{ application.attachment_count }} من 4</span>
                </div>
                
                <div class="meta-item">
                    <span class="meta-label">الحالة</span>
                    <span class="meta-value" style="color: #059669;">نشط</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function uploadFile(attachmentIndex, input) {
    const file = input.files[0];
    if (!file) return;
    
    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 10MB');
        input.value = '';
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    // Show loading state
    const fileItem = input.closest('.file-item');
    const originalContent = fileItem.innerHTML;
    fileItem.innerHTML = '<div style="padding: 20px; text-align: center;"><i class="fas fa-spinner fa-spin"></i> جاري الرفع...</div>';
    
    fetch(`/organizations/{{ organization.id }}/applications/{{ application.id }}/files/${attachmentIndex}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('خطأ: ' + data.error);
            fileItem.innerHTML = originalContent;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء رفع الملف');
        fileItem.innerHTML = originalContent;
    });
}

function deleteFile(attachmentIndex) {
    if (confirm('هل أنت متأكد من حذف هذا الملف؟\n\nلن يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/organizations/{{ organization.id }}/applications/{{ application.id }}/files/${attachmentIndex}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الملف');
        });
    }
}
</script>
{% endblock %}
