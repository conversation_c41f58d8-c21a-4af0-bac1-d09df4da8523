{% extends "base.html" %}

{% block title %}إنشاء حساب جديد - إرشيف{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Register Form Card -->
        <div class="card">
            <div class="card-header text-center">
                <h3 class="text-2xl font-bold text-gray-900">إنشاء حساب جديد</h3>
                <p class="mt-2 text-sm text-gray-600">انضم إلى نظام الأرشفة الداخلي</p>
            </div>

            <div class="card-body">
                <!-- Display error message if present -->
                {% if error %}
                <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="mr-3">
                            <p class="text-sm text-red-800" style="font-family: 'IBM Plex Sans Arabic', sans-serif;">
                                {{ error }}
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <form method="post" action="/register" class="space-y-6" id="registerForm">
                    <div class="form-group">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <div class="relative">
                            <input type="text"
                                   id="username"
                                   name="username"
                                   required
                                   minlength="3"
                                   maxlength="20"
                                   pattern="[a-zA-Z0-9_]+"
                                   class="form-input pr-10"
                                   placeholder="أدخل اسم المستخدم"
                                   value="{{ form_data.username if form_data else '' }}">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                        </div>
                        <div class="mt-1 text-xs text-gray-500">
                            3-20 حرف، يمكن استخدام الأرقام والشرطة السفلية
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <div class="relative">
                            <input type="email"
                                   id="email"
                                   name="email"
                                   required
                                   class="form-input pr-10"
                                   placeholder="أدخل البريد الإلكتروني"
                                   value="{{ form_data.email if form_data else '' }}">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="full_name" class="form-label">الاسم الكامل</label>
                        <div class="relative">
                            <input type="text"
                                   id="full_name"
                                   name="full_name"
                                   required
                                   class="form-input pr-10"
                                   placeholder="أدخل الاسم الكامل"
                                   value="{{ form_data.full_name if form_data else '' }}">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="relative">
                            <input type="password"
                                   id="password"
                                   name="password"
                                   required
                                   minlength="6"
                                   class="form-input pr-10 pl-10"
                                   placeholder="أدخل كلمة المرور">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <!-- Password toggle button will be added automatically by password-toggle.js -->
                        </div>
                        <div class="mt-1 text-xs text-gray-500">
                            يجب أن تكون 6 أحرف على الأقل
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                        <div class="relative">
                            <input type="password"
                                   id="confirm_password"
                                   name="confirm_password"
                                   required
                                   class="form-input pr-10 pl-10"
                                   placeholder="أعد إدخال كلمة المرور">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <!-- Password toggle button will be added automatically by password-toggle.js -->
                        </div>
                    </div>

                    <div>
                        <button type="submit" class="btn-primary w-full">
                            إنشاء الحساب
                        </button>
                    </div>
                </form>

                <hr class="my-6 border-gray-200">

                <div class="text-center">
                    <p class="text-sm text-gray-600">لديك حساب بالفعل؟</p>
                    <a href="/login" class="mt-2 inline-block text-primary-600 hover:text-primary-500 font-medium">
                        تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registerForm');
    const formFields = ['username', 'email', 'full_name'];

    // Load saved form data from localStorage if no server-side data is present
    {% if not form_data %}
    formFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        const savedValue = localStorage.getItem(`register_${fieldName}`);
        if (savedValue && field && !field.value) {
            field.value = savedValue;
        }
    });
    {% endif %}

    // Save form data to localStorage on input
    formFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('input', function() {
                localStorage.setItem(`register_${fieldName}`, this.value);
            });
        }
    });

    // Clear localStorage on successful form submission
    form.addEventListener('submit', function() {
        // Add a small delay to allow form submission to complete
        setTimeout(() => {
            formFields.forEach(fieldName => {
                localStorage.removeItem(`register_${fieldName}`);
            });
        }, 100);
    });

    // Clear localStorage when navigating to login page
    const loginLink = document.querySelector('a[href="/login"]');
    if (loginLink) {
        loginLink.addEventListener('click', function() {
            formFields.forEach(fieldName => {
                localStorage.removeItem(`register_${fieldName}`);
            });
        });
    }

    // Add real-time validation feedback
    const usernameField = document.getElementById('username');
    const emailField = document.getElementById('email');
    const fullNameField = document.getElementById('full_name');
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');

    // Username validation
    if (usernameField) {
        usernameField.addEventListener('blur', function() {
            const value = this.value;
            const isValid = /^[a-zA-Z0-9_]+$/.test(value) && value.length >= 3 && value.length <= 20;

            if (value && !isValid) {
                this.classList.add('border-red-500');
                this.classList.remove('border-gray-300');
            } else {
                this.classList.remove('border-red-500');
                this.classList.add('border-gray-300');
            }
        });
    }

    // Email validation
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const value = this.value;
            const isValid = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);

            if (value && !isValid) {
                this.classList.add('border-red-500');
                this.classList.remove('border-gray-300');
            } else {
                this.classList.remove('border-red-500');
                this.classList.add('border-gray-300');
            }
        });
    }

    // Password confirmation validation
    if (confirmPasswordField && passwordField) {
        function validatePasswordMatch() {
            const password = passwordField.value;
            const confirmPassword = confirmPasswordField.value;

            if (confirmPassword && password !== confirmPassword) {
                confirmPasswordField.classList.add('border-red-500');
                confirmPasswordField.classList.remove('border-gray-300');
            } else {
                confirmPasswordField.classList.remove('border-red-500');
                confirmPasswordField.classList.add('border-gray-300');
            }
        }

        passwordField.addEventListener('input', validatePasswordMatch);
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }
});
</script>
{% endblock %}