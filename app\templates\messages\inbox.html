{% extends "base.html" %}

{% block title %}{{ page_title or 'صندوق الرسائل' }} - إرشيف{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">صندوق الرسائل</h1>
            <p class="text-gray-600 mt-2">إدارة الرسائل الواردة والمرسلة</p>
        </div>
        <div>
            <a href="/messages/compose"
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-plus ml-2"></i>
                رسالة جديدة
            </a>
        </div>
    </div>

    <!-- Messages Navigation -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <nav class="flex space-x-4 space-x-reverse">
            <a href="/messages"
               class="flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-lg font-medium">
                <i class="fas fa-inbox ml-2"></i>
                الواردة
                {% if unread_count > 0 %}
                    <span class="bg-red-500 text-white text-xs rounded-full px-2 py-1 mr-2">{{ unread_count }}</span>
                {% endif %}
            </a>
            <a href="/messages/sent"
               class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <i class="fas fa-paper-plane ml-2"></i>
                المرسلة
            </a>
        </nav>
    </div>

    <!-- Messages List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        {% if messages %}
        <div class="divide-y divide-gray-200">
            {% for message in messages %}
            <div class="p-6 hover:bg-gray-50 transition-colors {{ 'bg-blue-50' if not message.is_read else '' }}"
                 data-message-id="{{ message.id }}">
                <div class="flex items-start space-x-4 space-x-reverse">
                    <!-- Sender Avatar -->
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-semibold text-sm">
                                {{ message.sender_name[0] if message.sender_name else 'غ' }}{{ message.sender_name.split(' ')[1][0] if message.sender_name and message.sender_name.split(' ')|length > 1 else '' }}
                            </span>
                        </div>
                    </div>

                    <!-- Message Content -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <h3 class="text-sm font-medium text-gray-900">{{ message.sender_name or 'مرسل غير معروف' }}</h3>
                                <span class="text-xs text-gray-500">{{ message.created_at.strftime('%Y-%m-%d %H:%M') if message.created_at else '' }}</span>
                            </div>
                            {% if not message.is_read %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-circle text-blue-500 text-xs ml-1"></i>
                                    جديد
                                </span>
                            {% endif %}
                        </div>

                        <div class="mb-2">
                            <a href="/messages/{{ message.id }}"
                               class="text-gray-900 hover:text-blue-600 transition-colors font-medium">
                                {{ message.subject or 'بدون موضوع' }}
                            </a>
                        </div>

                        <p class="text-sm text-gray-600 line-clamp-2">
                            {{ message.content[:100] if message.content else 'لا يوجد محتوى' }}{% if message.content and message.content|length > 100 %}...{% endif %}
                        </p>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="toggleReadStatus({{ message.id }}, {{ 'false' if message.is_read else 'true' }})"
                                class="text-gray-400 hover:text-blue-600 transition-colors p-2"
                                title="{{ 'تحديد كغير مقروء' if message.is_read else 'تحديد كمقروء' }}">
                            <i class="fas {{ 'fa-envelope-open' if message.is_read else 'fa-envelope' }}"></i>
                        </button>
                        <button onclick="deleteMessage({{ message.id }})"
                                class="text-gray-400 hover:text-red-600 transition-colors p-2"
                                title="حذف الرسالة">
                            <i class="fas fa-trash"></i>
                        </button>
                        <a href="/messages/compose?recipient_id={{ message.sender_id }}"
                           class="text-gray-400 hover:text-green-600 transition-colors p-2"
                           title="رد على الرسالة">
                            <i class="fas fa-reply"></i>
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Bulk Actions -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center space-x-4 space-x-reverse">
                <button id="markAllReadBtn"
                        onclick="markAllAsRead()"
                        class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                    <i class="fas fa-check-double ml-1"></i>
                    تحديد الكل كمقروء
                </button>
                <button id="deleteSelectedBtn"
                        onclick="deleteSelected()"
                        class="text-sm text-red-600 hover:text-red-800 transition-colors">
                    <i class="fas fa-trash ml-1"></i>
                    حذف المحدد
                </button>
            </div>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <i class="fas fa-inbox text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد رسائل</h3>
            <p class="text-gray-500 mb-4">لم تتلق أي رسائل بعد. ستظهر الرسائل الواردة هنا.</p>
            <a href="/messages/compose"
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-plus ml-2"></i>
                إرسال رسالة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Toggle read status
function toggleReadStatus(messageId, markAsRead) {
    fetch(`/messages/${messageId}/toggle-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_read: markAsRead })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء تحديث حالة الرسالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث حالة الرسالة');
    });
}

// Delete message
function deleteMessage(messageId) {
    if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
        fetch(`/messages/${messageId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الرسالة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الرسالة');
        });
    }
}

// Mark all as read
function markAllAsRead() {
    if (confirm('هل تريد تحديد جميع الرسائل كمقروءة؟')) {
        fetch('/messages/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث الرسائل');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث الرسائل');
        });
    }
}

// Delete selected (placeholder - you can implement checkbox selection)
function deleteSelected() {
    alert('هذه الميزة قيد التطوير');
}
</script>
{% endblock %}