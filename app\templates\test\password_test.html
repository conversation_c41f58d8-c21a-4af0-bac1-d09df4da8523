{% extends "base.html" %}

{% block title %}اختبار إظهار/إخفاء كلمة المرور{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6 text-center">اختبار وظيفة إظهار/إخفاء كلمة المرور</h1>
        
        <!-- Test Form 1: Login Style -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">نموذج تسجيل الدخول</h2>
            <form class="space-y-4">
                <div class="form-group">
                    <label for="test_login_password" class="form-label">كلمة المرور</label>
                    <div class="relative">
                        <input type="password"
                               id="test_login_password"
                               name="password"
                               class="form-input pr-10 pl-10"
                               placeholder="أدخل كلمة المرور">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Test Form 2: Registration Style -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">نموذج التسجيل</h2>
            <form class="space-y-4">
                <div class="form-group">
                    <label for="test_reg_password" class="form-label">كلمة المرور</label>
                    <div class="relative">
                        <input type="password"
                               id="test_reg_password"
                               name="password"
                               class="form-input pr-10 pl-10"
                               placeholder="أدخل كلمة المرور">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="test_confirm_password" class="form-label">تأكيد كلمة المرور</label>
                    <div class="relative">
                        <input type="password"
                               id="test_confirm_password"
                               name="confirm_password"
                               class="form-input pr-10 pl-10"
                               placeholder="أعد إدخال كلمة المرور">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Test Form 3: Admin Style -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">نموذج الإدارة</h2>
            <form class="space-y-4">
                <div class="admin-form-field">
                    <label for="test_admin_password" class="admin-form-label">كلمة المرور</label>
                    <div class="relative">
                        <input type="password"
                               id="test_admin_password"
                               name="password"
                               class="admin-form-input pl-10"
                               placeholder="أدخل كلمة المرور">
                    </div>
                </div>
            </form>
        </div>

        <!-- Test Form 4: Simple Style -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">نموذج بسيط</h2>
            <form class="space-y-4">
                <div>
                    <label for="test_simple_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                    <div class="relative">
                        <input type="password"
                               id="test_simple_password"
                               name="password"
                               class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل كلمة المرور">
                    </div>
                </div>
            </form>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-blue-800 mb-2">تعليمات الاختبار</h3>
            <ul class="text-blue-700 space-y-1">
                <li>• يجب أن تظهر أيقونة العين بجانب كل حقل كلمة مرور</li>
                <li>• انقر على أيقونة العين لإظهار/إخفاء كلمة المرور</li>
                <li>• يجب أن تتغير الأيقونة من عين مفتوحة إلى عين مغلقة</li>
                <li>• يجب أن تعمل الوظيفة مع لوحة المفاتيح (Tab + Enter/Space)</li>
                <li>• يجب أن تعمل على جميع أنواع النماذج</li>
            </ul>
        </div>

        <!-- Dynamic Test -->
        <div class="mt-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">اختبار الحقول الديناميكية</h3>
            <button type="button" onclick="addDynamicPasswordField()" class="btn-primary mb-4">
                إضافة حقل كلمة مرور ديناميكي
            </button>
            <div id="dynamic-container" class="space-y-4"></div>
        </div>
    </div>
</div>

<script>
let dynamicCounter = 0;

function addDynamicPasswordField() {
    dynamicCounter++;
    const container = document.getElementById('dynamic-container');
    
    const fieldDiv = document.createElement('div');
    fieldDiv.innerHTML = `
        <div class="form-group">
            <label for="dynamic_password_${dynamicCounter}" class="form-label">حقل ديناميكي #${dynamicCounter}</label>
            <div class="relative">
                <input type="password"
                       id="dynamic_password_${dynamicCounter}"
                       name="dynamic_password_${dynamicCounter}"
                       class="form-input pr-10 pl-10"
                       placeholder="كلمة مرور ديناميكية">
            </div>
        </div>
    `;
    
    container.appendChild(fieldDiv);
}
</script>
{% endblock %}
