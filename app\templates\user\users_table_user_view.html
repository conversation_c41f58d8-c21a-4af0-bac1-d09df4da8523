<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملفي الشخصي - جدول المعلومات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Modern Arabic font stack */
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif, 'Arabic UI Text', 'Geeza Pro', 'Traditional Arabic', 'Simplified Arabic';
        }

        /* Enhanced header */
        .page-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            border-radius: 25px;
            margin-bottom: 2.5rem;
            padding: 3rem;
            box-shadow: 0 20px 50px rgba(6, 182, 212, 0.3);
        }

        /* Stats cards */
        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #06b6d4, #0891b2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced table styling */
        .users-table {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .table-header {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            padding: 2rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .user-row {
            padding: 2rem;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
        }

        .user-row:hover {
            background-color: #f8fafc;
            transform: translateX(-5px);
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            background-color: #f3f4f6;
            border: 4px solid #ffffff;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            flex-shrink: 0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .user-avatar:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
        }

        /* Role badges */
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .role-badge.admin {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
        }

        .role-badge.manager {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .role-badge.user {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }

        /* Status badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .status-badge.active {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .status-badge.inactive {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        /* Action buttons */
        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: 15px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
            border: 2px solid transparent;
            margin: 4px;
        }

        .action-btn.edit {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .action-btn.edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .action-btn.requests {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .action-btn.requests:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .action-btn.activities {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }

        .action-btn.activities:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
        }

        .action-btn.profile {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .action-btn.profile:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .page-header {
                padding: 2rem;
            }
            
            .stat-card {
                padding: 1.5rem;
            }
            
            .user-row {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Enhanced Header Section -->
        <div class="page-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6 space-x-reverse">
                    <div class="user-avatar" style="background-image: url('{{ current_user.avatar_url }}');"></div>
                    <div>
                        <h1 class="text-5xl font-bold mb-3">ملفي الشخصي</h1>
                        <p class="text-cyan-100 text-xl mb-4">{{ current_user.full_name or current_user.username }}</p>
                        <div class="flex items-center space-x-6 space-x-reverse">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-envelope text-cyan-300"></i>
                                <span class="text-lg">{{ current_user.email }}</span>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-user-tag text-cyan-300"></i>
                                <span class="text-lg">
                                    {% if current_user.role.value == 'admin' %}
                                        مدير النظام
                                    {% elif current_user.role.value == 'manager' %}
                                        مدير المشاريع
                                    {% else %}
                                        مستخدم
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-4 space-x-reverse">
                    <a href="/requests/new" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 text-white rounded-xl hover:bg-opacity-30 transition-all duration-200 font-semibold">
                        <i class="fas fa-plus ml-2"></i>
                        طلب جديد
                    </a>
                    <a href="/requests" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 text-white rounded-xl hover:bg-opacity-30 transition-all duration-200 font-semibold">
                        <i class="fas fa-file-alt ml-2"></i>
                        طلباتي
                    </a>
                    <a href="/dashboard" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 text-white rounded-xl hover:bg-opacity-30 transition-all duration-200 font-semibold">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-10">
            <!-- Total Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 mb-2">إجمالي طلباتي</p>
                        <p class="text-4xl font-bold text-blue-600">{{ user_stats.total_requests }}</p>
                        <p class="text-xs text-gray-500 mt-2">جميع الطلبات المرسلة</p>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-file-alt text-white text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Pending Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 mb-2">الطلبات المعلقة</p>
                        <p class="text-4xl font-bold text-yellow-600">{{ user_stats.pending_requests }}</p>
                        <p class="text-xs text-gray-500 mt-2">تحتاج للمراجعة</p>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-clock text-white text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- In Progress Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 mb-2">قيد المعالجة</p>
                        <p class="text-4xl font-bold text-purple-600">{{ user_stats.in_progress_requests }}</p>
                        <p class="text-xs text-gray-500 mt-2">قيد التنفيذ</p>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-cog text-white text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Completed Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 mb-2">الطلبات المكتملة</p>
                        <p class="text-4xl font-bold text-green-600">{{ user_stats.completed_requests }}</p>
                        <p class="text-xs text-gray-500 mt-2">تم إنجازها</p>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-white text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced User Table -->
        <div class="users-table">
            <!-- Table Header -->
            <div class="table-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-3xl font-bold text-gray-900">معلوماتي في النظام</h2>
                            <p class="text-gray-600">تفاصيل حسابي وإحصائياتي الشخصية</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse text-gray-600">
                        <i class="fas fa-shield-alt"></i>
                        <span class="text-sm font-medium">حساب محمي</span>
                    </div>
                </div>
            </div>

            <!-- User Row -->
            <div class="user-row">
                <div class="grid grid-cols-1 lg:grid-cols-12 gap-6 items-center">
                    <!-- User Info (4 columns) -->
                    <div class="lg:col-span-4">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="user-avatar" style="background-image: url('{{ current_user.avatar_url }}');"></div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">{{ current_user.full_name or current_user.username }}</h3>
                                <p class="text-gray-600">{{ current_user.email }}</p>
                                <div class="flex items-center space-x-2 space-x-reverse mt-2">
                                    <span class="role-badge {{ current_user.role.value }}">
                                        {% if current_user.role.value == 'admin' %}
                                            <i class="fas fa-crown mr-1"></i>
                                            مدير النظام
                                        {% elif current_user.role.value == 'manager' %}
                                            <i class="fas fa-user-tie mr-1"></i>
                                            مدير المشاريع
                                        {% else %}
                                            <i class="fas fa-user mr-1"></i>
                                            مستخدم
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status (2 columns) -->
                    <div class="lg:col-span-2 text-center">
                        <span class="status-badge {{ 'active' if current_user.is_active else 'inactive' }}">
                            <i class="fas {{ 'fa-check-circle' if current_user.is_active else 'fa-times-circle' }} mr-1"></i>
                            {{ 'نشط' if current_user.is_active else 'غير نشط' }}
                        </span>
                    </div>

                    <!-- Statistics (3 columns) -->
                    <div class="lg:col-span-3">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                    <i class="fas fa-file-alt text-blue-500"></i>
                                    <span class="font-medium text-gray-900">{{ user_stats.total_requests }}</span>
                                    <span class="text-gray-500 text-xs">طلب</span>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                    <i class="fas fa-clock text-orange-500"></i>
                                    <span class="text-xs text-gray-500">
                                        آخر نشاط: {{ current_user.updated_at.strftime('%m-%d') if current_user.updated_at else 'غير محدد' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions (3 columns) -->
                    <div class="lg:col-span-3">
                        <div class="flex flex-wrap justify-end">
                            <!-- تعديل الملف الشخصي -->
                            <a href="/profile" class="action-btn edit">
                                <i class="fas fa-edit mr-1"></i>
                                تعديل
                            </a>

                            <!-- طلباتي -->
                            <a href="/requests" class="action-btn requests">
                                <i class="fas fa-file-alt mr-1"></i>
                                طلباتي
                            </a>

                            <!-- الملف الشخصي -->
                            <a href="/profile" class="action-btn profile">
                                <i class="fas fa-user mr-1"></i>
                                الملف الشخصي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-10">
            <div class="stat-card text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-plus text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">إنشاء طلب جديد</h3>
                <p class="text-gray-600 mb-4">ابدأ بإرسال طلب جديد للنظام</p>
                <a href="/requests/new" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 font-semibold">
                    <i class="fas fa-plus ml-2"></i>
                    طلب جديد
                </a>
            </div>

            <div class="stat-card text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-list text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">عرض طلباتي</h3>
                <p class="text-gray-600 mb-4">تصفح جميع الطلبات المرسلة</p>
                <a href="/requests" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 font-semibold">
                    <i class="fas fa-list ml-2"></i>
                    طلباتي
                </a>
            </div>

            <div class="stat-card text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-user-edit text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">تحديث الملف الشخصي</h3>
                <p class="text-gray-600 mb-4">تعديل معلوماتك الشخصية</p>
                <a href="/profile" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-xl hover:from-purple-600 hover:to-pink-700 transition-all duration-200 font-semibold">
                    <i class="fas fa-user-edit ml-2"></i>
                    تعديل الملف
                </a>
            </div>
        </div>
    </div>

</body>
</html>
