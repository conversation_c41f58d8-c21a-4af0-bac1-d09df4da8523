{% extends "base.html" %}

{% block title %}تسجيل الدخول - إرشيف{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Login Form Card -->
        <div class="card">
            <div class="card-header text-center">
                <h3 class="text-2xl font-bold text-gray-900">تسجيل الدخول</h3>
                <p class="mt-2 text-sm text-gray-600">مرحباً بك في النظام</p>
            </div>

            <div class="card-body">
                <!-- Display success message if present -->
                {% if success %}
                <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="mr-3">
                            <p class="text-sm text-green-800" style="font-family: 'IBM Plex Sans Arabic', sans-serif;">
                                {{ success }}
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Display error message if present -->
                {% if error %}
                <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="mr-3">
                            <p class="text-sm text-red-800" style="font-family: 'IBM Plex Sans Arabic', sans-serif;">
                                {{ error }}
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <form method="post" action="/login" class="space-y-6" id="loginForm">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            اسم المستخدم أو البريد الإلكتروني
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="username"
                                   name="username"
                                   required
                                   class="form-input pr-10"
                                   placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                                   value="{{ form_data.username if form_data else '' }}">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="relative">
                            <input type="password"
                                   id="password"
                                   name="password"
                                   required
                                   class="form-input pr-10 pl-10"
                                   placeholder="أدخل كلمة المرور">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <!-- Password toggle button will be added automatically by password-toggle.js -->
                        </div>
                    </div>

                    <div>
                        <button type="submit" class="btn-primary w-full">
                            تسجيل الدخول
                        </button>
                    </div>
                </form>

                <hr class="my-6 border-gray-200">

                <div class="text-center">
                    <p class="text-sm text-gray-600">ليس لديك حساب؟</p>
                    <a href="/register" class="mt-2 inline-block text-primary-600 hover:text-primary-500 font-medium">
                        إنشاء حساب جديد
                    </a>
                </div>
            </div>
        </div>


    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const usernameField = document.getElementById('username');

    // Load saved username from localStorage if no server-side data is present
    {% if not form_data %}
    const savedUsername = localStorage.getItem('login_username');
    if (savedUsername && usernameField && !usernameField.value) {
        usernameField.value = savedUsername;
    }
    {% endif %}

    // Save username to localStorage on input (but not password for security)
    if (usernameField) {
        usernameField.addEventListener('input', function() {
            localStorage.setItem('login_username', this.value);
        });
    }

    // Clear localStorage on successful form submission
    form.addEventListener('submit', function() {
        // Add a small delay to allow form submission to complete
        setTimeout(() => {
            // Only clear if login was successful (no error on page)
            if (!document.querySelector('.bg-red-50')) {
                localStorage.removeItem('login_username');
            }
        }, 100);
    });

    // Clear localStorage when navigating to register page
    const registerLink = document.querySelector('a[href="/register"]');
    if (registerLink) {
        registerLink.addEventListener('click', function() {
            localStorage.removeItem('login_username');
        });
    }
});
</script>
{% endblock %}