@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts for Arabic support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Base RTL styles */
@layer base {
  html {
    direction: rtl;
    font-family: 'Noto Sans Arabic', Tahoma, Arial, sans-serif;
  }
  
  body {
    @apply text-gray-900 bg-gray-50;
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Ensure proper RTL text alignment */
  [dir="rtl"] {
    text-align: right;
  }
  
  /* Fix for HTMX indicators in RTL */
  .htmx-indicator {
    @apply opacity-0 transition-opacity;
  }
  
  .htmx-request .htmx-indicator {
    @apply opacity-100;
  }
  
  .htmx-request.htmx-indicator {
    @apply opacity-100;
  }
}

/* Custom component styles */
@layer components {
  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Form styles */
  .form-group {
    @apply mb-4;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-textarea {
    @apply form-input resize-vertical;
  }
  
  .form-select {
    @apply form-input;
  }
  
  /* Alert styles */
  .alert {
    @apply p-4 rounded-md mb-4;
  }
  
  .alert-success {
    @apply alert bg-success-50 text-success-800 border border-success-200;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 text-warning-800 border border-warning-200;
  }
  
  .alert-danger {
    @apply alert bg-danger-50 text-danger-800 border border-danger-200;
  }
  
  .alert-info {
    @apply alert bg-primary-50 text-primary-800 border border-primary-200;
  }
  
  /* Navigation styles */
  .nav-link {
    @apply block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors;
  }
  
  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }
  
  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-right text-sm font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-info {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }
}

/* Utility classes for RTL */
@layer utilities {
  .rtl\:text-right {
    text-align: right;
  }
  
  .rtl\:text-left {
    text-align: left;
  }
  
  .rtl\:float-right {
    float: right;
  }
  
  .rtl\:float-left {
    float: left;
  }
  
  .rtl\:mr-auto {
    margin-right: auto;
  }
  
  .rtl\:ml-auto {
    margin-left: auto;
  }
}
