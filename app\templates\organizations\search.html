{% extends "base.html" %}

{% block title %}البحث في المؤسسات والطلبات - إرشيف{% endblock %}

{% block content %}
<style>
/* Professional Search Page Styling */
.page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
}

.page-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

.search-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-form {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 24px;
}

.search-input {
    flex: 1;
    padding: 16px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    transition: border-color 0.2s;
    background: #ffffff;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
}

.search-tab {
    padding: 12px 20px;
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    transition: all 0.2s;
}

.search-tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
}

.search-tab:hover {
    color: #3b82f6;
}

.results-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    min-height: 400px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.results-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.results-count {
    font-size: 14px;
    color: #6b7280;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.result-card {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s;
    cursor: pointer;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.result-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.result-type {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.result-type.organization {
    background: #dbeafe;
    color: #1d4ed8;
}

.result-type.application {
    background: #d1fae5;
    color: #065f46;
}

.result-details {
    margin-bottom: 16px;
}

.result-detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    font-size: 14px;
    color: #374151;
}

.result-detail-icon {
    width: 14px;
    color: #9ca3af;
}

.result-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.btn {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.empty-state {
    text-align: center;
    padding: 64px 32px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.empty-state-text {
    margin-bottom: 24px;
}

.loading-state {
    text-align: center;
    padding: 64px 32px;
    color: #6b7280;
}

.loading-spinner {
    font-size: 32px;
    color: #3b82f6;
    margin-bottom: 16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.search-suggestions {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

.suggestion-title {
    font-size: 14px;
    font-weight: 600;
    color: #0c4a6e;
    margin-bottom: 8px;
}

.suggestion-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.suggestion-item {
    background: #ffffff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    color: #0369a1;
    cursor: pointer;
    transition: all 0.2s;
}

.suggestion-item:hover {
    background: #0369a1;
    color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .search-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-tabs {
        flex-wrap: wrap;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .result-header {
        flex-direction: column;
        gap: 8px;
    }
    
    .result-actions {
        justify-content: center;
    }
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">البحث المتقدم</h1>
        <p class="page-subtitle">البحث في المؤسسات والطلبات</p>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <div class="search-form">
            <input 
                type="text" 
                id="searchInput" 
                class="search-input" 
                placeholder="ابحث في المؤسسات والطلبات..."
                autocomplete="off"
            >
        </div>
        
        <div class="search-tabs">
            <button class="search-tab active" data-type="global">الكل</button>
            <button class="search-tab" data-type="organizations">المؤسسات</button>
            <button class="search-tab" data-type="applications">الطلبات</button>
        </div>
        
        <div class="search-suggestions" id="searchSuggestions" style="display: none;">
            <div class="suggestion-title">اقتراحات البحث:</div>
            <div class="suggestion-list">
                <span class="suggestion-item" onclick="performSearch('وزارة الصحة')">وزارة الصحة</span>
                <span class="suggestion-item" onclick="performSearch('مجمع 1')">مجمع 1</span>
                <span class="suggestion-item" onclick="performSearch('شارع الكويت')">شارع الكويت</span>
                <span class="suggestion-item" onclick="performSearch('APP-')">رقم الطلب</span>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="results-section">
        <div class="results-header">
            <h2 class="results-title">نتائج البحث</h2>
            <span class="results-count" id="resultsCount">ابدأ البحث لعرض النتائج</span>
        </div>
        
        <div id="resultsContainer">
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="empty-state-title">ابدأ البحث</h3>
                <p class="empty-state-text">أدخل كلمة أو عبارة للبحث في المؤسسات والطلبات</p>
            </div>
        </div>
    </div>
</div>

<script>
let currentSearchType = 'global';
let searchTimeout;

document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const searchTabs = document.querySelectorAll('.search-tab');
    const resultsContainer = document.getElementById('resultsContainer');
    const resultsCount = document.getElementById('resultsCount');
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    // Search input handler
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        clearTimeout(searchTimeout);
        
        if (query.length >= 2) {
            searchSuggestions.style.display = 'block';
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 500);
        } else {
            searchSuggestions.style.display = 'none';
            showEmptyState();
        }
    });
    
    // Tab switching
    searchTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            searchTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            currentSearchType = this.dataset.type;
            
            const query = searchInput.value.trim();
            if (query.length >= 2) {
                performSearch(query);
            }
        });
    });
    
    // Enter key search
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const query = this.value.trim();
            if (query.length >= 2) {
                performSearch(query);
            }
        }
    });
});

function performSearch(query) {
    if (typeof query === 'string') {
        document.getElementById('searchInput').value = query;
    } else {
        query = document.getElementById('searchInput').value.trim();
    }
    
    if (query.length < 2) {
        showEmptyState();
        return;
    }
    
    showLoadingState();
    
    let endpoint;
    switch (currentSearchType) {
        case 'organizations':
            endpoint = `/organizations/api/search/organizations?search=${encodeURIComponent(query)}`;
            break;
        case 'applications':
            endpoint = `/organizations/api/search/applications?search=${encodeURIComponent(query)}`;
            break;
        default:
            endpoint = `/organizations/api/search/global?search=${encodeURIComponent(query)}`;
    }
    
    fetch(endpoint)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayResults(data);
            } else {
                showErrorState(data.error);
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            showErrorState('حدث خطأ أثناء البحث');
        });
}

function showLoadingState() {
    document.getElementById('resultsContainer').innerHTML = `
        <div class="loading-state">
            <div class="loading-spinner">
                <i class="fas fa-spinner"></i>
            </div>
            <h3>جاري البحث...</h3>
        </div>
    `;
    document.getElementById('resultsCount').textContent = 'جاري البحث...';
}

function showEmptyState() {
    document.getElementById('resultsContainer').innerHTML = `
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3 class="empty-state-title">ابدأ البحث</h3>
            <p class="empty-state-text">أدخل كلمة أو عبارة للبحث في المؤسسات والطلبات</p>
        </div>
    `;
    document.getElementById('resultsCount').textContent = 'ابدأ البحث لعرض النتائج';
}

function showErrorState(error) {
    document.getElementById('resultsContainer').innerHTML = `
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-exclamation-triangle" style="color: #ef4444;"></i>
            </div>
            <h3 class="empty-state-title">خطأ في البحث</h3>
            <p class="empty-state-text">${error}</p>
        </div>
    `;
    document.getElementById('resultsCount').textContent = 'حدث خطأ';
}

function displayResults(data) {
    const resultsContainer = document.getElementById('resultsContainer');
    const resultsCount = document.getElementById('resultsCount');
    
    let totalResults = 0;
    let html = '';
    
    if (currentSearchType === 'global') {
        totalResults = (data.total_organizations || 0) + (data.total_applications || 0);
        
        // Display organizations
        if (data.organizations && data.organizations.length > 0) {
            data.organizations.forEach(org => {
                html += createOrganizationCard(org);
            });
        }
        
        // Display applications
        if (data.applications && data.applications.length > 0) {
            data.applications.forEach(app => {
                html += createApplicationCard(app);
            });
        }
    } else if (currentSearchType === 'organizations') {
        totalResults = data.total || 0;
        if (data.organizations && data.organizations.length > 0) {
            data.organizations.forEach(org => {
                html += createOrganizationCard(org);
            });
        }
    } else if (currentSearchType === 'applications') {
        totalResults = data.total || 0;
        if (data.applications && data.applications.length > 0) {
            data.applications.forEach(app => {
                html += createApplicationCard(app);
            });
        }
    }
    
    if (totalResults === 0) {
        resultsContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="empty-state-title">لا توجد نتائج</h3>
                <p class="empty-state-text">لم يتم العثور على نتائج تطابق البحث</p>
            </div>
        `;
        resultsCount.textContent = 'لا توجد نتائج';
    } else {
        resultsContainer.innerHTML = `<div class="results-grid">${html}</div>`;
        resultsCount.textContent = `${totalResults} نتيجة`;
    }
}

function createOrganizationCard(org) {
    return `
        <div class="result-card" onclick="window.location.href='/organizations/${org.id}'">
            <div class="result-header">
                <h3 class="result-title">${org.name}</h3>
                <span class="result-type organization">مؤسسة</span>
            </div>
            <div class="result-details">
                <div class="result-detail-item">
                    <i class="fas fa-map-marker-alt result-detail-icon"></i>
                    <span>${org.full_address}</span>
                </div>
                <div class="result-detail-item">
                    <i class="fas fa-file-alt result-detail-icon"></i>
                    <span>${org.applications_count} طلب</span>
                </div>
                ${org.contact_person ? `
                <div class="result-detail-item">
                    <i class="fas fa-user result-detail-icon"></i>
                    <span>${org.contact_person}</span>
                </div>
                ` : ''}
            </div>
            <div class="result-actions">
                <a href="/organizations/${org.id}" class="btn btn-primary" onclick="event.stopPropagation();">
                    <i class="fas fa-eye"></i>
                    عرض
                </a>
            </div>
        </div>
    `;
}

function createApplicationCard(app) {
    return `
        <div class="result-card" onclick="window.location.href='/organizations/${app.organization_id}/applications/${app.id}'">
            <div class="result-header">
                <h3 class="result-title">${app.application_id}</h3>
                <span class="result-type application">طلب</span>
            </div>
            <div class="result-details">
                <div class="result-detail-item">
                    <i class="fas fa-user result-detail-icon"></i>
                    <span>${app.name}</span>
                </div>
                <div class="result-detail-item">
                    <i class="fas fa-phone result-detail-icon"></i>
                    <span>${app.mobile}</span>
                </div>
                <div class="result-detail-item">
                    <i class="fas fa-building result-detail-icon"></i>
                    <span>${app.organization_name || 'غير محدد'}</span>
                </div>
                <div class="result-detail-item">
                    <i class="fas fa-paperclip result-detail-icon"></i>
                    <span>${app.attachment_count} مرفق</span>
                </div>
            </div>
            <div class="result-actions">
                <a href="/organizations/${app.organization_id}/applications/${app.id}" class="btn btn-primary" onclick="event.stopPropagation();">
                    <i class="fas fa-eye"></i>
                    عرض
                </a>
            </div>
        </div>
    `;
}
</script>
{% endblock %}
