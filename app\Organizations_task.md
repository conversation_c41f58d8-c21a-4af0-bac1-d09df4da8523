Your task is to build a complete module for managing client organizations and their service applications into our app. The goal is to allow storing fixed organization details and linking them to multiple service application forms with file attachments.

🎯 Feature Overview:
You are building a two-layer archive system:

Organizations (with official name + fixed address)

Applications (submitted under each org, with form data + files)

🪜 Follow These Steps in Order:
Step 1: Database Design
Design two SQLAlchemy models:

Organization: name (unique), building, street, block

Application: unique ID, name, benayat, mobile, notes, 4 attachment fields, foreign key to organization

📌 Store uploaded files on disk under:
/uploads/{organization_name}/{application_id}/

Step 2: Create Organization Feature
Implement:

A form page to create new organizations

A page to list all organizations as cards or table rows

Store them in DB with validation (no duplicates)

Step 3: Application Submission Feature
Implement:

A dynamic form to create a new Application under a specific organization

Fields: unique ID, name, benayat, mobile, notes, 4 file inputs

On submit:

Store form data in DB

Save each file to a folder path based on org and app ID

Step 4: File Handling
Ensure secure file storage under structured folders:

e.g. uploads/Ministry of Health/APP-12345/attachment1.pdf

Store paths in the database

Step 5: Application Viewing
Create a detail view for each application (all fields + files)

List all applications for an organization (summary: ID, name, mobile)

Step 6: Search Function
Add a search bar that looks up organizations by:

Name

Building / Street / Block

Show results with links to applications

Step 7: UI/UX Design
Use HTMX + TailwindCSS to design clean, responsive forms and cards:

Organization list: styled as cards or tables

Application form: grouped inputs + file preview labels

Submit button with loading state

Step 8: Testing
Test full flow:

Create org → Add app → Upload files → View all

Try edge cases (missing fields, empty uploads)

Step 9: Backup Strategy
Store uploaded files outside of static folder

Add notes to later schedule backups (e.g. cron + rsync or zip)


🧠 Remember: You are building a modular, clean archive system with clear structure and UX.
Each organization is a “folder.” Each service request is a file with form + attachments.