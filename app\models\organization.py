from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Foreign<PERSON><PERSON>, Text, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import uuid
from datetime import datetime
from typing import Optional


class Organization(Base):
    """Organization model for managing client organizations"""
    __tablename__ = "organizations"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, nullable=False)  # Organization name (unique)
    building = Column(String(200), nullable=False)  # Building name/number
    street = Column(String(200), nullable=False)   # Street name
    block = Column(String(100), nullable=False)    # Block number/name
    
    # Additional optional fields for better organization management
    description = Column(Text, nullable=True)      # Optional description
    contact_person = Column(String(200), nullable=True)  # Contact person name
    contact_phone = Column(String(15), nullable=True)    # Contact phone number
    contact_email = Column(String(100), nullable=True)   # Contact email
    
    # Status and metadata
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    applications = relationship("Application", back_populates="organization", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Organization(name='{self.name}', building='{self.building}')>"

    @property
    def full_address(self) -> str:
        """Get formatted full address"""
        return f"{self.building}, {self.street}, {self.block}"

    @property
    def applications_count(self) -> int:
        """Get count of applications for this organization"""
        return len(self.applications) if self.applications else 0

    def to_dict(self):
        """Convert organization to dictionary for API responses"""
        return {
            "id": self.id,
            "name": self.name,
            "building": self.building,
            "street": self.street,
            "block": self.block,
            "full_address": self.full_address,
            "description": self.description,
            "contact_person": self.contact_person,
            "contact_phone": self.contact_phone,
            "contact_email": self.contact_email,
            "is_active": self.is_active,
            "applications_count": self.applications_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class Application(Base):
    """Application model for service applications under organizations"""
    __tablename__ = "applications"

    id = Column(Integer, primary_key=True, index=True)
    application_id = Column(String(50), unique=True, index=True, nullable=False)  # Unique application ID
    organization_id = Column(Integer, ForeignKey("organizations.id"), nullable=False)
    
    # Application details
    name = Column(String(200), nullable=False)      # Applicant name
    benayat = Column(String(200), nullable=True)    # Benayat field
    mobile = Column(String(15), nullable=False)     # Mobile number
    notes = Column(Text, nullable=True)             # Additional notes
    
    # File attachment paths (4 attachment fields as specified)
    attachment1_path = Column(String(500), nullable=True)  # Path to first attachment
    attachment2_path = Column(String(500), nullable=True)  # Path to second attachment
    attachment3_path = Column(String(500), nullable=True)  # Path to third attachment
    attachment4_path = Column(String(500), nullable=True)  # Path to fourth attachment
    
    # Original filenames for display purposes
    attachment1_filename = Column(String(255), nullable=True)
    attachment2_filename = Column(String(255), nullable=True)
    attachment3_filename = Column(String(255), nullable=True)
    attachment4_filename = Column(String(255), nullable=True)
    
    # Status and metadata
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    organization = relationship("Organization", back_populates="applications")

    def __repr__(self):
        return f"<Application(application_id='{self.application_id}', name='{self.name}')>"

    @classmethod
    def generate_application_id(cls) -> str:
        """Generate unique application ID"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"APP-{timestamp}"

    @classmethod
    def generate_unique_code(cls) -> str:
        """Generate unique identification code"""
        return str(uuid.uuid4()).replace("-", "").upper()[:12]

    @property
    def attachments(self) -> list:
        """Get list of attachments with their details"""
        attachments = []
        for i in range(1, 5):
            path_attr = f"attachment{i}_path"
            filename_attr = f"attachment{i}_filename"
            path = getattr(self, path_attr)
            filename = getattr(self, filename_attr)
            
            if path and filename:
                attachments.append({
                    "index": i,
                    "path": path,
                    "filename": filename,
                    "exists": True
                })
            else:
                attachments.append({
                    "index": i,
                    "path": None,
                    "filename": None,
                    "exists": False
                })
        return attachments

    @property
    def attachment_count(self) -> int:
        """Get count of uploaded attachments"""
        count = 0
        for i in range(1, 5):
            path_attr = f"attachment{i}_path"
            if getattr(self, path_attr):
                count += 1
        return count

    def to_dict(self):
        """Convert application to dictionary for API responses"""
        return {
            "id": self.id,
            "application_id": self.application_id,
            "organization_id": self.organization_id,
            "organization_name": self.organization.name if self.organization else None,
            "name": self.name,
            "benayat": self.benayat,
            "mobile": self.mobile,
            "notes": self.notes,
            "attachments": self.attachments,
            "attachment_count": self.attachment_count,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
