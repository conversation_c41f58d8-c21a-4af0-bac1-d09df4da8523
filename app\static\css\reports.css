/* 
 * CMSVS Reports CSS
 * Mobile-optimized styling for report action buttons
 * Provides responsive design for better mobile experience
 */

/* Report action buttons - Base styles */
.report-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border-radius: 0.5rem;
    color: white;
    text-align: center;
    transition: all 0.2s ease;
    height: 100%;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.report-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: white;
}

.report-action-btn:active {
    transform: translateY(0);
}

.report-action-btn i,
.report-action-btn img,
.report-action-btn svg {
    font-size: 2rem;
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.report-action-btn span {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.3;
}

/* Report actions grid container */
.report-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    width: 100%;
}

/* Color variants for different button types */
.report-action-btn.csv {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
}

.report-action-btn.pdf {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.report-action-btn.generate {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.report-action-btn.arabic {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
}

.report-action-btn.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.report-action-btn.secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
}

/* Mobile optimization for report buttons */
@media (max-width: 768px) {
    .report-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .report-action-btn {
        padding: 1rem;
        min-height: 100px;
        border-radius: 0.75rem;
    }
    
    .report-action-btn i,
    .report-action-btn img,
    .report-action-btn svg {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .report-action-btn span {
        font-size: 0.9rem;
        font-weight: 600;
    }
}

/* Extra small screens optimization */
@media (max-width: 480px) {
    .report-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .report-action-btn {
        padding: 0.75rem;
        min-height: 80px;
        border-radius: 0.5rem;
    }
    
    .report-action-btn i,
    .report-action-btn img,
    .report-action-btn svg {
        font-size: 1.25rem;
        margin-bottom: 0.375rem;
    }
    
    .report-action-btn span {
        font-size: 0.8rem;
        font-weight: 600;
        line-height: 1.2;
    }
}

/* Legacy button styles compatibility */
.report-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border-radius: 12px;
    min-width: 120px;
    text-align: center;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.report-btn i {
    font-size: 24px;
    margin-bottom: 8px;
}

.report-btn:hover {
    transform: translateY(-2px);
    text-decoration: none;
    color: white;
}

/* Legacy button color variants */
.report-btn-pdf {
    background: linear-gradient(135deg, #4b5563, #1e40af);
    color: white;
}

.report-btn-csv {
    background: linear-gradient(135deg, #16a34a, #15803d);
    color: white;
}

.report-btn-create {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
}

.report-btn-print {
    background: linear-gradient(135deg, #1e3a8a, #1e40af);
    color: white;
}

/* Mobile optimization for legacy buttons */
@media (max-width: 768px) {
    .button-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        width: 100%;
    }

    .report-btn {
        padding: 12px;
        min-height: 80px;
        width: 100%;
        min-width: unset;
        border-radius: 8px;
    }

    .report-btn i {
        font-size: 20px;
        margin-bottom: 6px;
    }

    .report-btn span {
        font-size: 14px;
        line-height: 1.2;
    }
}

@media (max-width: 480px) {
    .report-btn {
        padding: 10px;
        min-height: 70px;
    }

    .report-btn i {
        font-size: 18px;
        margin-bottom: 4px;
    }

    .report-btn span {
        font-size: 12px;
    }
}

/* Export buttons styling */
.export-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    min-height: 80px;
    color: white;
}

.export-btn i {
    font-size: 20px;
    margin-bottom: 6px;
}

.export-btn.csv {
    background: linear-gradient(135deg, #16a34a, #15803d);
}

.export-btn.pdf {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.export-btn.print {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

.export-btn:hover {
    transform: translateY(-2px);
    text-decoration: none;
    color: white;
}

/* Mobile optimization for export buttons */
@media (max-width: 768px) {
    .export-buttons {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }
    
    .export-btn {
        padding: 10px;
        min-height: 70px;
    }
    
    .export-btn i {
        font-size: 18px;
        margin-bottom: 4px;
    }
    
    .export-btn span {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .export-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }
    
    .export-btn {
        padding: 8px;
        min-height: 60px;
    }
    
    .export-btn i {
        font-size: 16px;
        margin-bottom: 3px;
    }
    
    .export-btn span {
        font-size: 11px;
    }
}

/* Ensure touch targets meet accessibility guidelines */
@media (max-width: 768px) {
    .report-action-btn,
    .report-btn,
    .export-btn {
        min-height: 44px;
        min-width: 44px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .report-action-btn,
    .report-btn,
    .export-btn {
        border: 2px solid;
        box-shadow: none;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .report-action-btn,
    .report-btn,
    .export-btn {
        transition: none;
    }
    
    .report-action-btn:hover,
    .report-btn:hover,
    .export-btn:hover {
        transform: none;
    }
}
