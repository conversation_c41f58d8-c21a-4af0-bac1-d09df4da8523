#!/usr/bin/env python3
"""
Test script to debug organization creation issues
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_organization_creation():
    """Test organization creation to identify the error source"""
    print("🔍 Testing Organization Creation...")
    print("=" * 50)
    
    try:
        # Test database connection
        print("📊 Testing database connection...")
        from app.database import get_db, SessionLocal
        db = SessionLocal()
        print("✅ Database connection successful")
        
        # Test organization service
        print("\n🏢 Testing OrganizationService...")
        from app.services.organization_service import OrganizationService
        
        # Test creating an organization
        test_org = OrganizationService.create_organization(
            db=db,
            name=f"Test Organization {int(time.time())}",
            building="Test Building",
            street="Test Street", 
            block="Test Block",
            description="Test Description"
        )
        print(f"✅ Organization created successfully: {test_org.name}")
        print(f"   ID: {test_org.id}")
        print(f"   Full Address: {test_org.full_address}")
        
        # Test activity service
        print("\n📝 Testing ActivityService...")
        from app.services.activity_service import ActivityService
        from app.models.user import User, UserRole
        
        # Get or create a test admin user
        admin_user = db.query(User).filter(User.role == UserRole.ADMIN).first()
        if not admin_user:
            print("⚠️ No admin user found, skipping activity logging test")
        else:
            try:
                ActivityService.log_activity(
                    db=db,
                    user_id=admin_user.id,
                    activity_type="organization_created",
                    description=f"Test activity for: {test_org.name}",
                    metadata={"organization_id": test_org.id, "organization_name": test_org.name}
                )
                print("✅ Activity logging successful")
            except Exception as activity_error:
                print(f"❌ Activity logging failed: {str(activity_error)}")
        
        # Test template rendering
        print("\n🎨 Testing template rendering...")
        from app.utils.templates import templates
        from fastapi import Request
        
        # Create a mock request object
        class MockRequest:
            def __init__(self):
                self.url = "http://localhost/test"
                self.method = "GET"
                self.headers = {}
        
        mock_request = MockRequest()
        
        try:
            # Test organization detail template
            template_response = templates.TemplateResponse(
                "organizations/detail.html",
                {
                    "request": mock_request,
                    "current_user": admin_user,
                    "organization": test_org,
                    "applications": []
                }
            )
            print("✅ Template rendering successful")
        except Exception as template_error:
            print(f"❌ Template rendering failed: {str(template_error)}")
            import traceback
            print(f"Template error details: {traceback.format_exc()}")
        
        # Clean up test organization
        print("\n🧹 Cleaning up test data...")
        db.delete(test_org)
        db.commit()
        print("✅ Test organization deleted")
        
        db.close()
        
        print("\n🎉 Organization creation test completed!")
        print("\nIf you're still seeing errors in the web interface:")
        print("1. Check the browser console for JavaScript errors")
        print("2. Check the server logs for detailed error messages")
        print("3. Verify that all required fields are filled correctly")
        print("4. Try creating an organization with minimal data first")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    import time
    success = test_organization_creation()
    sys.exit(0 if success else 1)
