{% extends "base.html" %}

{% block title %}الإنجازات والمسابقات - CMSVS{% endblock %}

{% block content %}
<div class="space-y-6">
    {% if current_user.role.value == 'admin' %}
    <!-- Enhanced Admin Header with Quick Stats -->
    <div class="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 rounded-2xl shadow-xl text-white p-8 mb-8">
        <div class="flex justify-between items-start mb-6">
            <div class="flex-1">
                <div class="flex items-center mb-3">
                    <div class="bg-white/20 p-3 rounded-xl mr-4">
                        <span class="text-3xl">📊</span>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold mb-1">لوحة إدارة الإنجازات</h1>
                        <p class="text-blue-100 text-lg">مراقبة شاملة وإدارة ذكية لأداء جميع المستخدمين</p>
                    </div>
                </div>

                <!-- Quick Action Buttons -->
                <div class="flex flex-wrap gap-3 mt-6">
                    <button onclick="refreshAllUsersData()" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 text-sm backdrop-blur-sm border border-white/20 hover:border-white/40 hover:scale-105">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">🔄</span>
                            تحديث البيانات
                        </span>
                    </button>
                    <button onclick="exportUsersData()" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 text-sm backdrop-blur-sm border border-white/20 hover:border-white/40 hover:scale-105">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">📊</span>
                            تصدير التقرير
                        </span>
                    </button>
                    <a href="/competitions" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 text-sm backdrop-blur-sm border border-white/20 hover:border-white/40 hover:scale-105">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">🏅</span>
                            إدارة المسابقات
                        </span>
                    </a>
                    <button onclick="debugChartState()" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 text-sm backdrop-blur-sm border border-white/20 hover:border-white/40 hover:scale-105">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">🔍</span>
                            فحص المخطط
                        </span>
                    </button>
                    <button onclick="forceShowChart()" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 text-sm backdrop-blur-sm border border-white/20 hover:border-white/40 hover:scale-105">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">👁️</span>
                            إظهار المخطط
                        </span>
                    </button>
                </div>
            </div>

            <!-- Enhanced Stats Cards -->
            <div class="grid grid-cols-2 gap-4 ml-8">
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20">
                    <div class="text-2xl font-bold">{{ all_users_progress|length if all_users_progress else 0 }}</div>
                    <div class="text-xs text-blue-200 mt-1">إجمالي المستخدمين</div>
                </div>
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20">
                    {% set active_today = all_users_progress | selectattr('performance.daily_completed', 'gt', 0) | list | length if all_users_progress else 0 %}
                    <div class="text-2xl font-bold">{{ active_today }}</div>
                    <div class="text-xs text-blue-200 mt-1">نشط اليوم</div>
                </div>
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20">
                    {% set avg_performance = (all_users_progress | map(attribute='progress_scores.overall') | list | sum / all_users_progress|length) if all_users_progress else 0 %}
                    <div class="text-2xl font-bold">{{ "%.0f"|format(avg_performance) }}%</div>
                    <div class="text-xs text-blue-200 mt-1">متوسط الأداء</div>
                </div>
                <div class="bg-white/15 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20">
                    {% set excellent_users = all_users_progress | selectattr('performance_level.text', 'equalto', 'ممتاز') | list | length if all_users_progress else 0 %}
                    <div class="text-2xl font-bold">{{ excellent_users }}</div>
                    <div class="text-xs text-blue-200 mt-1">أداء ممتاز</div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- User Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg text-white p-8">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-4xl font-bold mb-2">🏆 الإنجازات والمسابقات</h1>
                <p class="text-blue-100 text-lg">تتبع تقدمك وتنافس مع الآخرين لتحقيق أهدافك اليومية والأسبوعية والشهرية</p>
                <div class="flex space-x-3 rtl:space-x-reverse mt-4">
                    <button onclick="syncProgress()" class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm backdrop-blur-sm">
                        🔄 تحديث التقدم
                    </button>
                    <a href="/competitions" class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm backdrop-blur-sm">
                        🏅 المسابقات
                    </a>
                </div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold">{{ user_data.user_stats.global_rank }}</div>
                <div class="text-sm text-blue-200">ترتيبك العام</div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if current_user.role.value != 'admin' %}
    <!-- Enhanced User Dashboard Header -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full text-white text-2xl font-bold mb-4">
                {{ current_user.full_name[0] if current_user.full_name else 'U' }}
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">مرحباً، {{ current_user.full_name }}</h2>
            <p class="text-gray-600 text-lg">تتبع تقدمك وحقق أهدافك اليومية والأسبوعية والشهرية</p>

            <!-- Global Rank Badge -->
            <div class="inline-flex items-center bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-6 py-3 rounded-full font-bold text-lg mt-4 shadow-lg">
                <span class="text-xl mr-2">🏆</span>
                المركز العام: #{{ user_data.user_stats.global_rank }}
            </div>
        </div>

        <!-- Quick Performance Insights -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
            <h3 class="text-lg font-bold text-gray-900 mb-4 text-center">⚡ نظرة سريعة على أدائك</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold" style="color: {{ user_data.performance_stats.today.level.color }}">
                        {{ user_data.performance_stats.today.level.icon }}
                    </div>
                    <div class="text-sm font-medium text-gray-700">اليوم</div>
                    <div class="text-xs" style="color: {{ user_data.performance_stats.today.level.color }}">
                        {{ user_data.performance_stats.today.level.text }}
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold" style="color: {{ user_data.performance_stats.week.level.color }}">
                        {{ user_data.performance_stats.week.level.icon }}
                    </div>
                    <div class="text-sm font-medium text-gray-700">الأسبوع</div>
                    <div class="text-xs" style="color: {{ user_data.performance_stats.week.level.color }}">
                        {{ user_data.performance_stats.week.level.text }}
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold" style="color: {{ user_data.performance_stats.month.level.color }}">
                        {{ user_data.performance_stats.month.level.icon }}
                    </div>
                    <div class="text-sm font-medium text-gray-700">الشهر</div>
                    <div class="text-xs" style="color: {{ user_data.performance_stats.month.level.color }}">
                        {{ user_data.performance_stats.month.level.text }}
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">{{ "%.0f"|format(user_data.performance_stats.efficiency_score) }}</div>
                    <div class="text-sm font-medium text-gray-700">الكفاءة</div>
                    <div class="text-xs text-indigo-600">من 100</div>
                </div>
            </div>
        </div>

        <!-- Enhanced User Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
            <!-- Total Points -->
            <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-6 border border-yellow-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-yellow-700 mb-1">إجمالي النقاط</p>
                        <p class="text-3xl font-bold text-yellow-900">{{ "{:,}".format(user_data.user_stats.total_points) }}</p>
                        <p class="text-xs text-yellow-600 mt-1">نقطة مكتسبة</p>
                    </div>
                    <div class="p-3 bg-yellow-200 rounded-lg">
                        <span class="text-2xl">⭐</span>
                    </div>
                </div>
            </div>

            <!-- Current Streak -->
            <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-6 border border-red-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-red-700 mb-1">الإنجاز المتتالي</p>
                        <p class="text-3xl font-bold text-red-900">{{ user_data.user_stats.current_daily_streak }}</p>
                        <p class="text-xs text-red-600 mt-1">يوم متتالي</p>
                    </div>
                    <div class="p-3 bg-red-200 rounded-lg">
                        <span class="text-2xl">🔥</span>
                    </div>
                </div>
            </div>

            <!-- Total Achievements -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-700 mb-1">الإنجازات المكتملة</p>
                        <p class="text-3xl font-bold text-green-900">{{ user_data.user_stats.total_achievements }}</p>
                        <p class="text-xs text-green-600 mt-1">إنجاز مكتمل</p>
                    </div>
                    <div class="p-3 bg-green-200 rounded-lg">
                        <span class="text-2xl">🏅</span>
                    </div>
                </div>
            </div>

            <!-- Longest Streak -->
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-700 mb-1">أطول إنجاز متتالي</p>
                        <p class="text-3xl font-bold text-purple-900">{{ user_data.user_stats.longest_daily_streak }}</p>
                        <p class="text-xs text-purple-600 mt-1">يوم متتالي</p>
                    </div>
                    <div class="p-3 bg-purple-200 rounded-lg">
                        <span class="text-2xl">💪</span>
                    </div>
                </div>
            </div>

            <!-- Completion Rate -->
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-700 mb-1">معدل الإنجاز</p>
                        <p class="text-3xl font-bold text-blue-900">{{ "%.1f"|format(user_data.performance_stats.completion_rate) }}%</p>
                        <p class="text-xs text-blue-600 mt-1">نسبة النجاح</p>
                    </div>
                    <div class="p-3 bg-blue-200 rounded-lg">
                        <span class="text-2xl">📊</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Enhanced Progress Tracking Section -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
        <div class="text-center mb-8">
            <h3 class="text-3xl font-bold text-gray-900 mb-2">📈 تتبع التقدم الفعلي</h3>
            <p class="text-gray-600 text-lg">مراقبة أدائك اليومي والأسبوعي والشهري في الوقت الفعلي</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Today's Performance -->
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div class="text-center mb-6">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-500 rounded-full text-white text-2xl mb-4">
                        📅
                    </div>
                    <h4 class="text-2xl font-bold text-blue-900 mb-2">الأداء اليومي</h4>
                    <p class="text-blue-700 text-sm">الهدف: {{ user_data.performance_stats.today.target }} طلب</p>
                </div>

                <div class="space-y-4">
                    <!-- Enhanced Stats Display -->
                    <div class="bg-white/50 rounded-xl p-4">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-blue-700 font-medium">الطلبات المكتملة</span>
                            <span class="text-3xl font-bold text-blue-900">{{ user_data.performance_stats.today.completed }}</span>
                        </div>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center bg-blue-50 rounded-lg p-2">
                                <div class="text-blue-800 font-semibold">{{ user_data.performance_stats.today.total }}</div>
                                <div class="text-blue-600 text-xs">إجمالي اليوم</div>
                            </div>
                            <div class="text-center bg-blue-50 rounded-lg p-2">
                                <div class="text-blue-800 font-semibold">{{ user_data.performance_stats.today.remaining }}</div>
                                <div class="text-blue-600 text-xs">متبقي للهدف</div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Progress Bar -->
                    <div class="w-full bg-blue-200 rounded-full h-6 relative overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-6 rounded-full transition-all duration-500 flex items-center justify-center relative"
                             style="width: {{ user_data.performance_stats.today.progress_percentage }}%">
                            <span class="text-white text-sm font-bold z-10">{{ "%.0f"|format(user_data.performance_stats.today.progress_percentage) }}%</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                        </div>
                        {% if user_data.performance_stats.today.progress_percentage < 100 %}
                        <div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-700 text-xs font-medium">
                            الهدف: {{ user_data.performance_stats.today.target }}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Enhanced Status Display -->
                    <div class="text-center space-y-2">
                        <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium shadow-sm"
                             style="background-color: {{ user_data.performance_stats.today.level.color }}20; color: {{ user_data.performance_stats.today.level.color }}; border: 2px solid {{ user_data.performance_stats.today.level.color }}40;">
                            <span class="text-lg mr-2">{{ user_data.performance_stats.today.level.icon }}</span>
                            {{ user_data.performance_stats.today.level.text }}
                        </div>
                        <div class="text-xs text-blue-600 font-medium">
                            {{ user_data.performance_stats.today.status.icon }} {{ user_data.performance_stats.today.status.text }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- This Week's Performance -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div class="text-center mb-6">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-full text-white text-2xl mb-4">
                        📊
                    </div>
                    <h4 class="text-2xl font-bold text-green-900 mb-2">الأداء الأسبوعي</h4>
                    <p class="text-green-700 text-sm">الهدف: {{ user_data.performance_stats.week.target }} طلب</p>
                </div>

                <div class="space-y-4">
                    <!-- Enhanced Weekly Stats Display -->
                    <div class="bg-white/50 rounded-xl p-4">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-green-700 font-medium">الطلبات المكتملة</span>
                            <span class="text-3xl font-bold text-green-900">{{ user_data.performance_stats.week.completed }}</span>
                        </div>
                        <div class="grid grid-cols-3 gap-2 text-sm">
                            <div class="text-center bg-green-50 rounded-lg p-2">
                                <div class="text-green-800 font-semibold">{{ user_data.performance_stats.week.total }}</div>
                                <div class="text-green-600 text-xs">إجمالي الأسبوع</div>
                            </div>
                            <div class="text-center bg-green-50 rounded-lg p-2">
                                <div class="text-green-800 font-semibold">{{ user_data.performance_stats.week.remaining }}</div>
                                <div class="text-green-600 text-xs">متبقي للهدف</div>
                            </div>
                            <div class="text-center bg-green-50 rounded-lg p-2">
                                <div class="text-green-800 font-semibold">{{ user_data.performance_stats.week.days_left }}</div>
                                <div class="text-green-600 text-xs">أيام متبقية</div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Weekly Progress Bar -->
                    <div class="w-full bg-green-200 rounded-full h-6 relative overflow-hidden">
                        <div class="bg-gradient-to-r from-green-500 to-green-600 h-6 rounded-full transition-all duration-500 flex items-center justify-center relative"
                             style="width: {{ user_data.performance_stats.week.progress_percentage }}%">
                            <span class="text-white text-sm font-bold z-10">{{ "%.0f"|format(user_data.performance_stats.week.progress_percentage) }}%</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                        </div>
                        {% if user_data.performance_stats.week.progress_percentage < 100 %}
                        <div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-700 text-xs font-medium">
                            الهدف: {{ user_data.performance_stats.week.target }}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Enhanced Weekly Status Display -->
                    <div class="text-center space-y-2">
                        <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium shadow-sm"
                             style="background-color: {{ user_data.performance_stats.week.level.color }}20; color: {{ user_data.performance_stats.week.level.color }}; border: 2px solid {{ user_data.performance_stats.week.level.color }}40;">
                            <span class="text-lg mr-2">{{ user_data.performance_stats.week.level.icon }}</span>
                            {{ user_data.performance_stats.week.level.text }}
                        </div>
                        <div class="text-xs text-green-600 font-medium">
                            {{ user_data.performance_stats.week.status.icon }} {{ user_data.performance_stats.week.status.text }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- This Month's Performance -->
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div class="text-center mb-6">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-purple-500 rounded-full text-white text-2xl mb-4">
                        📈
                    </div>
                    <h4 class="text-2xl font-bold text-purple-900 mb-2">الأداء الشهري</h4>
                    <p class="text-purple-700 text-sm">الهدف: {{ user_data.performance_stats.month.target }} طلب</p>
                </div>

                <div class="space-y-4">
                    <!-- Enhanced Monthly Stats Display -->
                    <div class="bg-white/50 rounded-xl p-4">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-purple-700 font-medium">الطلبات المكتملة</span>
                            <span class="text-3xl font-bold text-purple-900">{{ user_data.performance_stats.month.completed }}</span>
                        </div>
                        <div class="grid grid-cols-3 gap-2 text-sm">
                            <div class="text-center bg-purple-50 rounded-lg p-2">
                                <div class="text-purple-800 font-semibold">{{ user_data.performance_stats.month.total }}</div>
                                <div class="text-purple-600 text-xs">إجمالي الشهر</div>
                            </div>
                            <div class="text-center bg-purple-50 rounded-lg p-2">
                                <div class="text-purple-800 font-semibold">{{ user_data.performance_stats.month.remaining }}</div>
                                <div class="text-purple-600 text-xs">متبقي للهدف</div>
                            </div>
                            <div class="text-center bg-purple-50 rounded-lg p-2">
                                <div class="text-purple-800 font-semibold">{{ user_data.performance_stats.month.days_left }}</div>
                                <div class="text-purple-600 text-xs">أيام متبقية</div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Monthly Progress Bar -->
                    <div class="w-full bg-purple-200 rounded-full h-6 relative overflow-hidden">
                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 h-6 rounded-full transition-all duration-500 flex items-center justify-center relative"
                             style="width: {{ user_data.performance_stats.month.progress_percentage }}%">
                            <span class="text-white text-sm font-bold z-10">{{ "%.0f"|format(user_data.performance_stats.month.progress_percentage) }}%</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                        </div>
                        {% if user_data.performance_stats.month.progress_percentage < 100 %}
                        <div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-700 text-xs font-medium">
                            الهدف: {{ user_data.performance_stats.month.target }}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Enhanced Monthly Status Display -->
                    <div class="text-center space-y-2">
                        <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium shadow-sm"
                             style="background-color: {{ user_data.performance_stats.month.level.color }}20; color: {{ user_data.performance_stats.month.level.color }}; border: 2px solid {{ user_data.performance_stats.month.level.color }}40;">
                            <span class="text-lg mr-2">{{ user_data.performance_stats.month.level.icon }}</span>
                            {{ user_data.performance_stats.month.level.text }}
                        </div>
                        <div class="text-xs text-purple-600 font-medium">
                            {{ user_data.performance_stats.month.status.icon }} {{ user_data.performance_stats.month.status.text }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Overall Performance Summary -->
        <div class="mt-8 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-8 border border-gray-200">
            <h4 class="text-2xl font-bold text-gray-900 text-center mb-8">📊 ملخص الأداء الشامل</h4>

            <!-- Main Performance Metrics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                <div class="text-center bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="text-4xl font-bold text-gray-900 mb-2">{{ user_data.performance_stats.total_requests }}</div>
                    <div class="text-sm text-gray-600 font-medium mb-1">إجمالي الطلبات</div>
                    <div class="text-xs text-gray-500">منذ التسجيل</div>
                </div>
                <div class="text-center bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="text-4xl font-bold text-green-600 mb-2">{{ user_data.performance_stats.completed_requests }}</div>
                    <div class="text-sm text-gray-600 font-medium mb-1">الطلبات المكتملة</div>
                    <div class="text-xs text-gray-500">تم إنجازها</div>
                </div>
                <div class="text-center bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="text-4xl font-bold text-blue-600 mb-2">{{ "%.1f"|format(user_data.performance_stats.completion_rate) }}%</div>
                    <div class="text-sm text-gray-600 font-medium mb-1">معدل الإنجاز</div>
                    <div class="text-xs text-gray-500">نسبة النجاح</div>
                </div>
                <div class="text-center bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div class="text-4xl font-bold text-orange-600 mb-2">{{ user_data.performance_stats.avg_completion_days }}</div>
                    <div class="text-sm text-gray-600 font-medium mb-1">متوسط أيام الإنجاز</div>
                    <div class="text-xs text-gray-500">سرعة الإنجاز</div>
                </div>
            </div>

            <!-- Advanced Performance Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Productivity Score -->
                <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-6 border border-indigo-200">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-indigo-600 mb-2">{{ "%.1f"|format(user_data.performance_stats.efficiency_score) }}</div>
                        <div class="text-sm text-indigo-700 font-medium mb-1">نقاط الكفاءة</div>
                        <div class="text-xs text-indigo-600">من 100</div>
                        <div class="w-full bg-indigo-200 rounded-full h-2 mt-3">
                            <div class="bg-indigo-600 h-2 rounded-full transition-all duration-500"
                                 style="width: {{ user_data.performance_stats.efficiency_score }}%"></div>
                        </div>
                    </div>
                </div>

                <!-- Daily Average -->
                <div class="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-xl p-6 border border-emerald-200">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-emerald-600 mb-2">{{ "%.1f"|format(user_data.performance_stats.daily_average) }}</div>
                        <div class="text-sm text-emerald-700 font-medium mb-1">المتوسط اليومي</div>
                        <div class="text-xs text-emerald-600">طلب/يوم</div>
                        <div class="text-xs text-emerald-500 mt-2">
                            منذ {{ user_data.performance_stats.days_since_registration }} يوم
                        </div>
                    </div>
                </div>

                <!-- Current Status -->
                <div class="bg-gradient-to-br from-amber-50 to-amber-100 rounded-xl p-6 border border-amber-200">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-amber-600 mb-2">{{ user_data.performance_stats.pending_requests + user_data.performance_stats.in_progress_requests }}</div>
                        <div class="text-sm text-amber-700 font-medium mb-1">طلبات نشطة</div>
                        <div class="text-xs text-amber-600 space-y-1">
                            <div>{{ user_data.performance_stats.pending_requests }} في الانتظار</div>
                            <div>{{ user_data.performance_stats.in_progress_requests }} قيد المعالجة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Guide -->
            <div class="mt-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                <h4 class="text-lg font-bold text-gray-900 mb-4 text-center">📚 دليل فهم الإحصائيات</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3 rtl:space-x-reverse">
                            <span class="text-lg">🎯</span>
                            <div>
                                <div class="font-semibold text-gray-900">الأهداف اليومية/الأسبوعية/الشهرية</div>
                                <div class="text-gray-600">10 طلبات يومياً، 50 أسبوعياً، 200 شهرياً</div>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3 rtl:space-x-reverse">
                            <span class="text-lg">⚡</span>
                            <div>
                                <div class="font-semibold text-gray-900">نقاط الكفاءة</div>
                                <div class="text-gray-600">مقياس شامل يجمع معدل الإنجاز والسرعة والإنتاجية</div>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3 rtl:space-x-reverse">
                            <span class="text-lg">📊</span>
                            <div>
                                <div class="font-semibold text-gray-900">المتوسط اليومي</div>
                                <div class="text-gray-600">عدد الطلبات المكتملة يومياً منذ التسجيل</div>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3 rtl:space-x-reverse">
                            <span class="text-lg">🏆</span>
                            <div>
                                <div class="font-semibold text-gray-900">مستويات الأداء</div>
                                <div class="text-gray-600">ممتاز (90%+) • جيد جداً (80-89%) • جيد (70-79%) • مقبول (60-69%) • يحتاج تحسين (<60%)</div>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3 rtl:space-x-reverse">
                            <span class="text-lg">⏱️</span>
                            <div>
                                <div class="font-semibold text-gray-900">متوسط أيام الإنجاز</div>
                                <div class="text-gray-600">الوقت المتوسط لإكمال الطلبات من الإنشاء للإنجاز</div>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3 rtl:space-x-reverse">
                            <span class="text-lg">🔥</span>
                            <div>
                                <div class="font-semibold text-gray-900">الطلبات النشطة</div>
                                <div class="text-gray-600">الطلبات في الانتظار أو قيد المعالجة حالياً</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Progress Tracking -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Daily Progress -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">🌟 الهدف اليومي</h3>
                <span class="text-sm text-gray-500">{{ user_data.current_progress.daily.status }}</span>
            </div>
            
            <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>{{ user_data.current_progress.daily.current }} / {{ user_data.current_progress.daily.target }}</span>
                    <span>{{ user_data.current_progress.daily.percentage }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-3 rounded-full transition-all duration-300" 
                         style="width: {{ user_data.current_progress.daily.percentage }}%"></div>
                </div>
            </div>
            
            <div class="space-y-2">
                {% for achievement in user_data.current_progress.daily.achievements %}
                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="text-lg ml-2">{{ achievement.badge_icon }}</span>
                        <span class="text-sm font-medium">{{ achievement.name }}</span>
                    </div>
                    {% if achievement.completed %}
                    <span class="text-green-600 text-sm">✅</span>
                    {% else %}
                    <span class="text-gray-400 text-xs">{{ achievement.progress }}/{{ achievement.target }}</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Weekly Progress -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">🏆 الهدف الأسبوعي</h3>
                <span class="text-sm text-gray-500">{{ user_data.current_progress.weekly.status }}</span>
            </div>
            
            <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>{{ user_data.current_progress.weekly.current }} / {{ user_data.current_progress.weekly.target }}</span>
                    <span>{{ user_data.current_progress.weekly.percentage }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full transition-all duration-300" 
                         style="width: {{ user_data.current_progress.weekly.percentage }}%"></div>
                </div>
            </div>
            
            <div class="space-y-2">
                {% for achievement in user_data.current_progress.weekly.achievements %}
                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="text-lg ml-2">{{ achievement.badge_icon }}</span>
                        <span class="text-sm font-medium">{{ achievement.name }}</span>
                    </div>
                    {% if achievement.completed %}
                    <span class="text-green-600 text-sm">✅</span>
                    {% else %}
                    <span class="text-gray-400 text-xs">{{ achievement.progress }}/{{ achievement.target }}</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Monthly Progress -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">👑 الهدف الشهري</h3>
                <span class="text-sm text-gray-500">{{ user_data.current_progress.monthly.status }}</span>
            </div>
            
            <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>{{ user_data.current_progress.monthly.current }} / {{ user_data.current_progress.monthly.target }}</span>
                    <span>{{ user_data.current_progress.monthly.percentage }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="bg-gradient-to-r from-purple-400 to-purple-600 h-3 rounded-full transition-all duration-300" 
                         style="width: {{ user_data.current_progress.monthly.percentage }}%"></div>
                </div>
            </div>
            
            <div class="space-y-2">
                {% for achievement in user_data.current_progress.monthly.achievements %}
                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="text-lg ml-2">{{ achievement.badge_icon }}</span>
                        <span class="text-sm font-medium">{{ achievement.name }}</span>
                    </div>
                    {% if achievement.completed %}
                    <span class="text-green-600 text-sm">✅</span>
                    {% else %}
                    <span class="text-gray-400 text-xs">{{ achievement.progress }}/{{ achievement.target }}</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Leaderboard and Competitions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Leaderboard -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">🏅 لوحة المتصدرين</h3>
                <div class="flex space-x-2 rtl:space-x-reverse">
                    <button onclick="switchLeaderboard('global')" 
                            class="px-3 py-1 text-sm rounded-lg {{ 'bg-blue-100 text-blue-700' if current_period == 'global' else 'text-gray-500 hover:bg-gray-100' }}">
                        عام
                    </button>
                    <button onclick="switchLeaderboard('daily')" 
                            class="px-3 py-1 text-sm rounded-lg {{ 'bg-blue-100 text-blue-700' if current_period == 'daily' else 'text-gray-500 hover:bg-gray-100' }}">
                        يومي
                    </button>
                    <button onclick="switchLeaderboard('weekly')" 
                            class="px-3 py-1 text-sm rounded-lg {{ 'bg-blue-100 text-blue-700' if current_period == 'weekly' else 'text-gray-500 hover:bg-gray-100' }}">
                        أسبوعي
                    </button>
                    <button onclick="switchLeaderboard('monthly')" 
                            class="px-3 py-1 text-sm rounded-lg {{ 'bg-blue-100 text-blue-700' if current_period == 'monthly' else 'text-gray-500 hover:bg-gray-100' }}">
                        شهري
                    </button>
                </div>
            </div>
            
            <div id="leaderboard-content" class="space-y-3">
                {% for entry in leaderboard_data[:10] %}
                <div class="flex items-center justify-between p-3 {{ 'bg-yellow-50 border border-yellow-200' if entry.user_id == current_user.id else 'bg-gray-50' }} rounded-lg">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full {{ 'bg-yellow-500' if entry.rank == 1 else 'bg-gray-400' if entry.rank == 2 else 'bg-orange-500' if entry.rank == 3 else 'bg-gray-300' }} 
                                    flex items-center justify-center text-white text-sm font-bold ml-3">
                            {{ entry.rank }}
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{{ entry.full_name }}</div>
                            <div class="text-sm text-gray-500">@{{ entry.username }}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        {% if current_period == 'global' %}
                        <div class="font-bold text-gray-900">{{ entry.total_points }} نقطة</div>
                        <div class="text-sm text-gray-500">{{ entry.total_achievements }} إنجاز</div>
                        {% elif current_period == 'daily' %}
                        <div class="font-bold text-gray-900">{{ entry.daily_progress }}/10</div>
                        <div class="text-sm text-gray-500">{{ entry.percentage|round(1) }}%</div>
                        {% elif current_period == 'weekly' %}
                        <div class="font-bold text-gray-900">{{ entry.weekly_progress }}/50</div>
                        <div class="text-sm text-gray-500">{{ entry.percentage|round(1) }}%</div>
                        {% elif current_period == 'monthly' %}
                        <div class="font-bold text-gray-900">{{ entry.monthly_progress }}/200</div>
                        <div class="text-sm text-gray-500">{{ entry.percentage|round(1) }}%</div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <div class="mt-4 text-center">
                <a href="/achievements?period={{ current_period }}" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                    عرض المزيد من المتصدرين ←
                </a>
            </div>
        </div>

        <!-- Active Competitions -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">🎯 المسابقات النشطة</h3>
                <a href="/competitions" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                    عرض الكل ←
                </a>
            </div>
            
            {% if active_competitions %}
            <div class="space-y-4">
                {% for competition in active_competitions[:3] %}
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                        <h4 class="font-semibold text-gray-900">{{ competition.name }}</h4>
                        {% if competition.is_participating %}
                        <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">مشارك</span>
                        {% else %}
                        <button onclick="joinCompetition({{ competition.id }})" 
                                class="px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700">
                            انضم
                        </button>
                        {% endif %}
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-3">{{ competition.description }}</p>
                    
                    {% if competition.is_participating %}
                    <div class="mb-2">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>تقدمك: {{ competition.user_progress }}/{{ competition.target }}</span>
                            <span>المركز: {{ competition.user_rank or 'غير محدد' }}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" 
                                 style="width: {{ (competition.user_progress / competition.target * 100)|round(1) }}%"></div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="flex justify-between text-xs text-gray-500">
                        <span>{{ competition.total_participants }} مشارك</span>
                        <span>ينتهي في {{ competition.time_remaining.days }} يوم</span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-8">
                <div class="text-gray-400 text-4xl mb-3">🎯</div>
                <p class="text-gray-500">لا توجد مسابقات نشطة حالياً</p>
                <p class="text-sm text-gray-400 mt-1">ترقب المسابقات الجديدة قريباً!</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Recent Achievements -->
    {% if user_data.recent_achievements %}
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-6">🏆 آخر الإنجازات</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            {% for achievement in user_data.recent_achievements %}
            <div class="flex items-center p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                <div class="text-3xl ml-4">{{ achievement.badge_icon }}</div>
                <div>
                    <h4 class="font-semibold text-gray-900">{{ achievement.name }}</h4>
                    <p class="text-sm text-gray-600">{{ achievement.description }}</p>
                    <div class="flex items-center mt-1">
                        <span class="text-xs text-green-600 font-medium">+{{ achievement.points }} نقطة</span>
                        <span class="text-xs text-gray-400 mr-2">{{ achievement.completed_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endif %}

    <!-- Enhanced Admin Analytics Dashboard -->
    {% if current_user.role.value == 'admin' %}
    <div class="space-y-8">
        <!-- Admin Section Header -->
        <div class="bg-gradient-to-r from-slate-50 to-slate-100 rounded-2xl p-8 border border-slate-200">
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-slate-600 to-slate-700 rounded-full text-white text-3xl mb-4">
                    📊
                </div>
                <h2 class="text-4xl font-bold text-slate-900 mb-3">تحليلات أداء المستخدمين</h2>
                <p class="text-slate-600 text-lg max-w-2xl mx-auto">مراقبة شاملة وتحليل متقدم لأداء وتقدم جميع المستخدمين في النظام مع إمكانيات التصدير والتقارير</p>
            </div>

            <!-- Quick Action Center -->
            <div class="flex justify-center space-x-4 rtl:space-x-reverse">
                <button onclick="refreshAllUsersData()" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 text-sm shadow-lg hover:shadow-xl hover:scale-105">
                    <span class="flex items-center">
                        <span class="text-lg mr-2">🔄</span>
                        تحديث البيانات
                    </span>
                </button>
                <button onclick="exportUsersData()" class="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 text-sm shadow-lg hover:shadow-xl hover:scale-105">
                    <span class="flex items-center">
                        <span class="text-lg mr-2">📊</span>
                        تصدير التقرير
                    </span>
                </button>
                <a href="/competitions" class="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 text-sm shadow-lg hover:shadow-xl hover:scale-105">
                    <span class="flex items-center">
                        <span class="text-lg mr-2">🏅</span>
                        إدارة المسابقات
                    </span>
                </a>
            </div>
        </div>

        <!-- Main Analytics Container -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">

            <!-- Enhanced Performance Overview Cards -->
            <div class="mb-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">📊 مؤشرات الأداء الرئيسية</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {% set top_performers = all_users_progress[:3] %}
                    {% set avg_completion = (all_users_progress | map(attribute='performance.completion_rate') | list | sum / all_users_progress|length) if all_users_progress else 0 %}
                    {% set total_points = all_users_progress | map(attribute='stats.total_points') | list | sum %}
                    {% set active_users = all_users_progress | selectattr('performance.daily_completed', 'gt', 0) | list | length %}

                    <!-- Best Performance Card -->
                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                        <div class="text-center">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-full text-white text-2xl mb-4">
                                🏆
                            </div>
                            <h4 class="text-lg font-bold text-green-900 mb-2">أفضل أداء</h4>
                            <div class="text-3xl font-bold text-green-900 mb-1">{{ "%.1f"|format(all_users_progress[0].progress_scores.overall) if all_users_progress else 0 }}%</div>
                            <p class="text-sm text-green-700">أعلى نسبة إنجاز</p>
                        </div>
                    </div>

                    <!-- Average Completion Card -->
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                        <div class="text-center">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-500 rounded-full text-white text-2xl mb-4">
                                📈
                            </div>
                            <h4 class="text-lg font-bold text-blue-900 mb-2">متوسط الإنجاز</h4>
                            <div class="text-3xl font-bold text-blue-900 mb-1">{{ "%.1f"|format(avg_completion) }}%</div>
                            <p class="text-sm text-blue-700">معدل النظام العام</p>
                        </div>
                    </div>

                    <!-- Total Points Card -->
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                        <div class="text-center">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-purple-500 rounded-full text-white text-2xl mb-4">
                                ⭐
                            </div>
                            <h4 class="text-lg font-bold text-purple-900 mb-2">إجمالي النقاط</h4>
                            <div class="text-3xl font-bold text-purple-900 mb-1">{{ "{:,}".format(total_points) }}</div>
                            <p class="text-sm text-purple-700">نقاط جميع المستخدمين</p>
                        </div>
                    </div>
                    <!-- Active Users Card -->
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200 hover:shadow-lg transition-all duration-300 hover:scale-105">
                        <div class="text-center">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-orange-500 rounded-full text-white text-2xl mb-4">
                                🔥
                            </div>
                            <h4 class="text-lg font-bold text-orange-900 mb-2">نشط اليوم</h4>
                            <div class="text-3xl font-bold text-orange-900 mb-1">{{ active_users }}</div>
                            <p class="text-sm text-orange-700">مستخدم نشط</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if all_users_progress %}
            <!-- Enhanced Interactive Analytics Chart -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-8 border border-gray-200 mb-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">📊 مخطط التحليلات التفاعلي</h3>
                    <p class="text-gray-600">تصور بياني شامل لأداء جميع المستخدمين مع إمكانية التصفية والتحليل</p>
                </div>

                <!-- Enhanced Filter Buttons -->
                <div class="flex justify-center space-x-3 rtl:space-x-reverse mb-8">
                    <button onclick="showUsersChart('overall')" class="users-chart-filter-btn active bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg" data-chart="overall">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">📈</span>
                            الأداء العام
                        </span>
                    </button>
                    <button onclick="showUsersChart('daily')" class="users-chart-filter-btn bg-white text-gray-700 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-md border border-gray-200" data-chart="daily">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">📅</span>
                            يومي
                        </span>
                    </button>
                    <button onclick="showUsersChart('weekly')" class="users-chart-filter-btn bg-white text-gray-700 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-md border border-gray-200" data-chart="weekly">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">📊</span>
                            أسبوعي
                        </span>
                    </button>
                    <button onclick="showUsersChart('monthly')" class="users-chart-filter-btn bg-white text-gray-700 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-md border border-gray-200" data-chart="monthly">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">📈</span>
                            شهري
                        </span>
                    </button>
                </div>

                <!-- Enhanced Chart Container -->
                <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 shadow-xl border border-gray-100 relative overflow-hidden">
                    <!-- Subtle background pattern -->
                    <div class="absolute inset-0 opacity-5">
                        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #3B82F6 2px, transparent 2px), radial-gradient(circle at 75% 75%, #8B5CF6 2px, transparent 2px); background-size: 50px 50px;"></div>
                    </div>

                    <div class="h-80 relative chart-container" id="chartContainer">
                        <!-- Enhanced Loading State -->
                        <div id="chartLoading" class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg z-10">
                            <div class="text-center">
                                <div class="relative">
                                    <div class="animate-spin text-5xl mb-4">📊</div>
                                    <div class="absolute inset-0 animate-ping text-5xl mb-4 opacity-30">📊</div>
                                </div>
                                <p class="text-gray-700 font-semibold text-lg">جاري تحميل المخطط...</p>
                                <div class="mt-3 flex justify-center space-x-1">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                                    <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                                    <div class="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                                </div>
                            </div>
                        </div>
                        <canvas id="usersProgressChart" class="w-full h-full transition-all duration-500 relative z-20" style="opacity: 0;"></canvas>
                    </div>

                    <!-- Enhanced Chart Legend -->
                    <div class="mt-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                        <h4 class="text-center text-lg font-bold text-gray-900 mb-4">🎯 مستويات الأداء</h4>
                        <div class="flex flex-wrap justify-center gap-4 text-sm">
                            <div class="flex items-center bg-white rounded-lg px-3 py-2 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 border border-green-200">
                                <div class="w-5 h-5 bg-gradient-to-br from-green-400 to-green-600 rounded-full mr-3 shadow-sm"></div>
                                <div>
                                    <div class="font-semibold text-green-700">🏆 ممتاز</div>
                                    <div class="text-xs text-green-600">(90%+)</div>
                                </div>
                            </div>
                            <div class="flex items-center bg-white rounded-lg px-3 py-2 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 border border-blue-200">
                                <div class="w-5 h-5 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full mr-3 shadow-sm"></div>
                                <div>
                                    <div class="font-semibold text-blue-700">⭐ جيد جداً</div>
                                    <div class="text-xs text-blue-600">(80-89%)</div>
                                </div>
                            </div>
                            <div class="flex items-center bg-white rounded-lg px-3 py-2 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 border border-yellow-200">
                                <div class="w-5 h-5 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full mr-3 shadow-sm"></div>
                                <div>
                                    <div class="font-semibold text-yellow-700">👍 جيد</div>
                                    <div class="text-xs text-yellow-600">(70-79%)</div>
                                </div>
                            </div>
                            <div class="flex items-center bg-white rounded-lg px-3 py-2 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 border border-orange-200">
                                <div class="w-5 h-5 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full mr-3 shadow-sm"></div>
                                <div>
                                    <div class="font-semibold text-orange-700">📈 مقبول</div>
                                    <div class="text-xs text-orange-600">(60-69%)</div>
                                </div>
                            </div>
                            <div class="flex items-center bg-white rounded-lg px-3 py-2 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 border border-red-200">
                                <div class="w-5 h-5 bg-gradient-to-br from-red-400 to-red-600 rounded-full mr-3 shadow-sm"></div>
                                <div>
                                    <div class="font-semibold text-red-700">🎯 يحتاج تحسين</div>
                                    <div class="text-xs text-red-600">(<60%)</div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Tips -->
                        <div class="mt-4 text-center">
                            <p class="text-xs text-gray-600 italic">💡 انقر على أي عمود في المخطط لعرض تفاصيل المستخدم</p>
                        </div>
                    </div>
                </div>
            </div>

        <!-- Compact User Progress Table -->
        <div class="overflow-hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">👥 تفاصيل تقدم المستخدمين</h3>
                <div class="flex space-x-2 rtl:space-x-reverse">
                    <input type="text" id="searchUsersInput" placeholder="البحث عن مستخدم..."
                           class="px-3 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <select id="filterUsersPerformance" class="px-3 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">جميع المستويات</option>
                        <option value="ممتاز">ممتاز</option>
                        <option value="جيد جداً">جيد جداً</option>
                        <option value="جيد">جيد</option>
                        <option value="مقبول">مقبول</option>
                        <option value="يحتاج تحسين">يحتاج تحسين</option>
                    </select>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستخدم</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">مستوى الأداء</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الأداء العام</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">يومي</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">أسبوعي</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">شهري</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النقاط</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">معدل الإنجاز</th>
                        </tr>
                    </thead>
                    <tbody id="usersProgressTableBody" class="bg-white divide-y divide-gray-200">
                        {% for user in all_users_progress %}
                        <tr class="users-progress-row hover:bg-gray-50 transition-colors"
                            data-user-name="{{ user.user_info.full_name|lower }}"
                            data-performance="{{ user.performance_level.text }}">
                            <!-- User Info -->
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center text-white text-sm font-bold">
                                            {{ user.user_info.full_name[0] }}
                                        </div>
                                    </div>
                                    <div class="mr-3">
                                        <div class="text-sm font-medium text-gray-900">{{ user.user_info.full_name }}</div>
                                        <div class="text-xs text-gray-500">@{{ user.user_info.username }}</div>
                                    </div>
                                </div>
                            </td>

                            <!-- Performance Level -->
                            <td class="px-4 py-3 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                      style="background-color: {{ user.performance_level.color }}20; color: {{ user.performance_level.color }};">
                                    {{ user.performance_level.icon }} {{ user.performance_level.text }}
                                </span>
                            </td>

                            <!-- Overall Progress -->
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium">{{ "%.1f"|format(user.progress_scores.overall) }}%</div>
                                        <div class="w-16 bg-gray-200 rounded-full h-1.5 mt-1">
                                            <div class="h-1.5 rounded-full transition-all duration-300"
                                                 style="width: {{ user.progress_scores.overall }}%; background-color: {{ user.performance_level.color }};"></div>
                                        </div>
                                    </div>
                                </div>
                            </td>

                            <!-- Daily Progress -->
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-xs">
                                    <div class="font-medium text-gray-900">{{ user.performance.daily_completed }}/10</div>
                                    <div class="w-12 bg-gray-200 rounded-full h-1 mt-1">
                                        <div class="bg-blue-500 h-1 rounded-full" style="width: {{ user.progress_scores.daily }}%"></div>
                                    </div>
                                </div>
                            </td>

                            <!-- Weekly Progress -->
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-xs">
                                    <div class="font-medium text-gray-900">{{ user.performance.weekly_completed }}/50</div>
                                    <div class="w-12 bg-gray-200 rounded-full h-1 mt-1">
                                        <div class="bg-green-500 h-1 rounded-full" style="width: {{ user.progress_scores.weekly }}%"></div>
                                    </div>
                                </div>
                            </td>

                            <!-- Monthly Progress -->
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-xs">
                                    <div class="font-medium text-gray-900">{{ user.performance.monthly_completed }}/200</div>
                                    <div class="w-12 bg-gray-200 rounded-full h-1 mt-1">
                                        <div class="bg-purple-500 h-1 rounded-full" style="width: {{ user.progress_scores.monthly }}%"></div>
                                    </div>
                                </div>
                            </td>

                            <!-- Points -->
                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ "{:,}".format(user.stats.total_points) }}
                            </td>

                            <!-- Completion Rate -->
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ "%.1f"|format(user.performance.completion_rate) }}%</div>
                                <div class="text-xs text-gray-500">{{ user.performance.completed_requests }}/{{ user.performance.total_requests }}</div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% else %}
        <!-- No Users Message -->
        <div class="text-center py-12">
            <div class="text-gray-400 text-6xl mb-4">👥</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">لا يوجد مستخدمون حالياً</h3>
            <p class="text-gray-600 mb-4">لم يتم العثور على أي مستخدمين في النظام</p>
            <button onclick="refreshAllUsersData()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                🔄 تحديث البيانات
            </button>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- Chart.js Library for User Progress Chart -->
{% if current_user.role.value == 'admin' %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endif %}

<script>
// Leaderboard switching
function switchLeaderboard(period) {
    // Update button states
    document.querySelectorAll('[onclick^="switchLeaderboard"]').forEach(btn => {
        btn.className = btn.className.replace('bg-blue-100 text-blue-700', 'text-gray-500 hover:bg-gray-100');
    });
    event.target.className = event.target.className.replace('text-gray-500 hover:bg-gray-100', 'bg-blue-100 text-blue-700');
    
    // Fetch new leaderboard data
    fetch(`/achievements/leaderboard?period=${period}`)
        .then(response => response.json())
        .then(data => {
            updateLeaderboardDisplay(data.leaderboard, period);
        })
        .catch(error => console.error('Error:', error));
}

function updateLeaderboardDisplay(leaderboard, period) {
    const content = document.getElementById('leaderboard-content');
    content.innerHTML = leaderboard.map((entry, index) => {
        const isCurrentUser = entry.user_id === {{ current_user.id }};
        const bgClass = isCurrentUser ? 'bg-yellow-50 border border-yellow-200' : 'bg-gray-50';
        const rankClass = entry.rank === 1 ? 'bg-yellow-500' : 
                         entry.rank === 2 ? 'bg-gray-400' : 
                         entry.rank === 3 ? 'bg-orange-500' : 'bg-gray-300';
        
        let scoreDisplay = '';
        if (period === 'global') {
            scoreDisplay = `<div class="font-bold text-gray-900">${entry.total_points} نقطة</div>
                           <div class="text-sm text-gray-500">${entry.total_achievements} إنجاز</div>`;
        } else if (period === 'daily') {
            scoreDisplay = `<div class="font-bold text-gray-900">${entry.daily_progress}/10</div>
                           <div class="text-sm text-gray-500">${entry.percentage.toFixed(1)}%</div>`;
        } else if (period === 'weekly') {
            scoreDisplay = `<div class="font-bold text-gray-900">${entry.weekly_progress}/50</div>
                           <div class="text-sm text-gray-500">${entry.percentage.toFixed(1)}%</div>`;
        } else if (period === 'monthly') {
            scoreDisplay = `<div class="font-bold text-gray-900">${entry.monthly_progress}/200</div>
                           <div class="text-sm text-gray-500">${entry.percentage.toFixed(1)}%</div>`;
        }
        
        return `
            <div class="flex items-center justify-between p-3 ${bgClass} rounded-lg">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full ${rankClass} flex items-center justify-center text-white text-sm font-bold ml-3">
                        ${entry.rank}
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">${entry.full_name}</div>
                        <div class="text-sm text-gray-500">@${entry.username}</div>
                    </div>
                </div>
                <div class="text-right">
                    ${scoreDisplay}
                </div>
            </div>
        `;
    }).join('');
}

// Join competition
function joinCompetition(competitionId) {
    fetch(`/competitions/${competitionId}/join`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            location.reload(); // Refresh to show updated status
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء الانضمام للمسابقة');
    });
}

// Auto-refresh progress every 30 seconds
setInterval(() => {
    fetch('/achievements/stats')
        .then(response => response.json())
        .then(data => {
            // Update progress bars and stats
            updateProgressBars(data);
        })
        .catch(error => console.error('Auto-refresh error:', error));
}, 30000);

function updateProgressBars(data) {
    // Update daily progress
    const dailyBar = document.querySelector('.bg-gradient-to-r.from-yellow-400');
    if (dailyBar) {
        dailyBar.style.width = `${data.daily_progress.percentage}%`;
    }
    
    // Update weekly progress
    const weeklyBar = document.querySelector('.bg-gradient-to-r.from-blue-400');
    if (weeklyBar) {
        weeklyBar.style.width = `${data.weekly_progress.percentage}%`;
    }
    
    // Update monthly progress
    const monthlyBar = document.querySelector('.bg-gradient-to-r.from-purple-400');
    if (monthlyBar) {
        monthlyBar.style.width = `${data.monthly_progress.percentage}%`;
    }
}

// Sync achievement progress with actual request data
async function syncProgress() {
    const button = event.target;
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '⏳ جاري التحديث...';
    button.disabled = true;

    try {
        const response = await fetch('/achievements/sync-progress', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        });

        const result = await response.json();

        if (result.status === 'success') {
            // Show success message
            button.innerHTML = '✅ تم التحديث';
            button.classList.remove('bg-white/20', 'hover:bg-white/30');
            button.classList.add('bg-green-500', 'hover:bg-green-600');

            // Reload page after a short delay to show updated data
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        // Show error message
        button.innerHTML = '❌ خطأ في التحديث';
        button.classList.remove('bg-white/20', 'hover:bg-white/30');
        button.classList.add('bg-red-500', 'hover:bg-red-600');

        console.error('Error syncing progress:', error);

        // Reset button after delay
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('bg-red-500', 'hover:bg-red-600');
            button.classList.add('bg-white/20', 'hover:bg-white/30');
            button.disabled = false;
        }, 3000);
    }
}

// Enhanced User Progress Chart Functions
{% if current_user.role.value == 'admin' %}
let usersProgressChart = null;
let usersData = {{ all_users_progress | tojson if all_users_progress else '[]' }};

// Debug logging
console.log('Users Data:', usersData);
console.log('Users Data Length:', usersData ? usersData.length : 0);

// Create sample data if no real data exists (for demonstration)
if (!usersData || usersData.length === 0) {
    console.log('No real data found, creating sample data for demonstration');
    usersData = [
        {
            user_info: { full_name: 'أحمد محمد', email: '<EMAIL>' },
            progress_scores: { overall: 95, daily: 90, weekly: 85, monthly: 100 },
            stats: { total_points: 1250 },
            performance_level: { text: 'ممتاز', color: 'rgb(34, 197, 94)' },
            performance: { daily_completed: 9, weekly_completed: 42, monthly_completed: 190, total_completed: 95, completion_rate: 95 }
        },
        {
            user_info: { full_name: 'فاطمة علي', email: '<EMAIL>' },
            progress_scores: { overall: 88, daily: 95, weekly: 80, monthly: 90 },
            stats: { total_points: 1100 },
            performance_level: { text: 'جيد جداً', color: 'rgb(59, 130, 246)' },
            performance: { daily_completed: 8, weekly_completed: 40, monthly_completed: 176, total_completed: 88, completion_rate: 88 }
        },
        {
            user_info: { full_name: 'محمد سالم', email: '<EMAIL>' },
            progress_scores: { overall: 82, daily: 75, weekly: 85, monthly: 85 },
            stats: { total_points: 950 },
            performance_level: { text: 'جيد', color: 'rgb(245, 158, 11)' },
            performance: { daily_completed: 7, weekly_completed: 35, monthly_completed: 164, total_completed: 82, completion_rate: 82 }
        },
        {
            user_info: { full_name: 'نورا أحمد', email: '<EMAIL>' },
            progress_scores: { overall: 76, daily: 80, weekly: 70, monthly: 80 },
            stats: { total_points: 800 },
            performance_level: { text: 'مقبول', color: 'rgb(239, 68, 68)' },
            performance: { daily_completed: 6, weekly_completed: 30, monthly_completed: 152, total_completed: 76, completion_rate: 76 }
        },
        {
            user_info: { full_name: 'خالد يوسف', email: '<EMAIL>' },
            progress_scores: { overall: 70, daily: 65, weekly: 75, monthly: 70 },
            stats: { total_points: 650 },
            performance_level: { text: 'يحتاج تحسين', color: 'rgb(139, 92, 246)' },
            performance: { daily_completed: 5, weekly_completed: 25, monthly_completed: 140, total_completed: 70, completion_rate: 70 }
        }
    ];
    console.log('Sample data created:', usersData);
}

// Initialize Users Progress Chart
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Initializing chart...');

    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded!');
        return;
    }

    // Check if canvas element exists
    const canvas = document.getElementById('usersProgressChart');
    if (!canvas) {
        console.error('Chart canvas element not found!');
        // Try to find the chart container and show error
        const chartSection = document.querySelector('.bg-white.rounded-2xl.shadow-xl');
        if (chartSection) {
            chartSection.innerHTML = `
                <div class="text-center p-8">
                    <div class="text-6xl mb-4">⚠️</div>
                    <h3 class="text-xl font-semibold mb-2 text-red-600">خطأ في تحميل المخطط</h3>
                    <p class="text-gray-600">لم يتم العثور على عنصر المخطط في الصفحة</p>
                    <button onclick="location.reload()" class="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        إعادة تحميل الصفحة
                    </button>
                </div>
            `;
        }
        return;
    }

    if (usersData && usersData.length > 0) {
        console.log('Initializing chart with', usersData.length, 'users');

        // Hide loading immediately if we have data
        const loading = document.getElementById('chartLoading');
        if (loading) {
            loading.style.opacity = '0.5';
        }

        initializeUsersProgressChart();
        setupUsersFiltering();
    } else {
        console.warn('No users data available for chart');
        showNoDataMessage();
    }
});

function initializeUsersProgressChart() {
    try {
        const canvas = document.getElementById('usersProgressChart');
        const ctx = canvas.getContext('2d');
        console.log('Chart context obtained, showing overall chart...');

        // Debug chart state
        console.log('Canvas element:', canvas);
        console.log('Canvas dimensions:', canvas.width, 'x', canvas.height);
        console.log('Canvas style:', canvas.style.cssText);

        // Initial chart with overall progress
        showUsersChart('overall');
    } catch (error) {
        console.error('Error initializing chart:', error);
        showChartError();
    }
}

// Debug function to check chart state
function debugChartState() {
    const canvas = document.getElementById('usersProgressChart');
    const loading = document.getElementById('chartLoading');
    const container = document.getElementById('chartContainer');

    console.log('=== Chart Debug Info ===');
    console.log('Canvas:', canvas);
    console.log('Canvas visible:', canvas ? window.getComputedStyle(canvas).visibility : 'N/A');
    console.log('Canvas opacity:', canvas ? window.getComputedStyle(canvas).opacity : 'N/A');
    console.log('Loading element:', loading);
    console.log('Loading display:', loading ? window.getComputedStyle(loading).display : 'N/A');
    console.log('Container:', container);
    console.log('Users data length:', usersData ? usersData.length : 0);
    console.log('Chart instance:', usersProgressChart);
    console.log('========================');
}

// Force show chart function for debugging
function forceShowChart() {
    console.log('Forcing chart to show...');

    const canvas = document.getElementById('usersProgressChart');
    const loading = document.getElementById('chartLoading');

    if (loading) {
        loading.style.display = 'none';
        console.log('Loading hidden');
    }

    if (canvas) {
        canvas.style.opacity = '1';
        canvas.style.visibility = 'visible';
        canvas.classList.remove('opacity-0');
        canvas.classList.add('opacity-100');
        console.log('Canvas made visible');
    }

    // Try to reinitialize chart if it doesn't exist
    if (!usersProgressChart && usersData && usersData.length > 0) {
        console.log('Reinitializing chart...');
        showUsersChart('overall');
    }

    // Debug state after forcing
    setTimeout(() => {
        debugChartState();
    }, 500);
}

function showNoDataMessage() {
    const chartContainer = document.getElementById('usersProgressChart').parentElement;
    chartContainer.innerHTML = `
        <div class="flex items-center justify-center h-full text-gray-500">
            <div class="text-center">
                <div class="text-6xl mb-4">📊</div>
                <h3 class="text-xl font-semibold mb-2">لا توجد بيانات للعرض</h3>
                <p class="text-sm">لم يتم العثور على بيانات المستخدمين لعرض المخطط</p>
            </div>
        </div>
    `;
}

function showChartError() {
    const chartContainer = document.getElementById('usersProgressChart').parentElement;
    chartContainer.innerHTML = `
        <div class="flex items-center justify-center h-full text-red-500">
            <div class="text-center">
                <div class="text-6xl mb-4">⚠️</div>
                <h3 class="text-xl font-semibold mb-2">خطأ في تحميل المخطط</h3>
                <p class="text-sm">حدث خطأ أثناء تحميل مخطط البيانات</p>
                <button onclick="location.reload()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    إعادة تحميل الصفحة
                </button>
            </div>
        </div>
    `;
}

function showUsersChart(type) {
    try {
        console.log('Showing chart for type:', type);

        // Validate data
        if (!usersData || usersData.length === 0) {
            console.error('No users data available');
            showNoDataMessage();
            return;
        }

        // Update button states
        document.querySelectorAll('.users-chart-filter-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-blue-600', 'text-white');
            btn.classList.add('bg-white', 'text-gray-700');
        });

        const activeButton = document.querySelector(`[data-chart="${type}"]`);
        if (activeButton) {
            activeButton.classList.add('active', 'bg-blue-600', 'text-white');
            activeButton.classList.remove('bg-white', 'text-gray-700');
        }

        const canvas = document.getElementById('usersProgressChart');
        if (!canvas) {
            console.error('Chart canvas not found');
            return;
        }

        const ctx = canvas.getContext('2d');

        // Destroy existing chart
        if (usersProgressChart) {
            usersProgressChart.destroy();
        }

        // Prepare data based on type with fallback values
        let chartData, chartLabel, maxValue;

        switch(type) {
            case 'daily':
                chartData = usersData.map(user => {
                    const value = user.progress_scores?.daily || user.performance?.daily_completed || 0;
                    return Math.min(Math.max(value, 0), 100); // Ensure 0-100 range
                });
                chartLabel = 'التقدم اليومي (%)';
                maxValue = 100;
                break;
            case 'weekly':
                chartData = usersData.map(user => {
                    const value = user.progress_scores?.weekly || user.performance?.weekly_completed || 0;
                    return Math.min(Math.max(value, 0), 100);
                });
                chartLabel = 'التقدم الأسبوعي (%)';
                maxValue = 100;
                break;
            case 'monthly':
                chartData = usersData.map(user => {
                    const value = user.progress_scores?.monthly || user.performance?.monthly_completed || 0;
                    return Math.min(Math.max(value, 0), 100);
                });
                chartLabel = 'التقدم الشهري (%)';
                maxValue = 100;
                break;
            default: // overall
                chartData = usersData.map(user => {
                    const value = user.progress_scores?.overall || user.performance?.completion_rate || 0;
                    return Math.min(Math.max(value, 0), 100);
                });
                chartLabel = 'الأداء العام (%)';
                maxValue = 100;
        }

        console.log('Chart data:', chartData);

        const labels = usersData.map(user => user.user_info?.full_name || user.full_name || 'مستخدم غير معروف');

        // Enhanced gradient color scheme based on performance
        const backgroundColors = usersData.map((user, index) => {
            const value = chartData[index];

            // Performance-based gradient colors
            if (value >= 90) {
                return `linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(34, 197, 94, 0.7))`; // Excellent - Green gradient
            } else if (value >= 80) {
                return `linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(99, 102, 241, 0.7))`; // Very Good - Blue gradient
            } else if (value >= 70) {
                return `linear-gradient(135deg, rgba(245, 158, 11, 0.9), rgba(251, 191, 36, 0.7))`; // Good - Yellow gradient
            } else if (value >= 60) {
                return `linear-gradient(135deg, rgba(249, 115, 22, 0.9), rgba(251, 146, 60, 0.7))`; // Acceptable - Orange gradient
            } else {
                return `linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(248, 113, 113, 0.7))`; // Needs Improvement - Red gradient
            }
        });

        // Solid colors for fallback and borders
        const solidBackgroundColors = usersData.map((user, index) => {
            const value = chartData[index];

            if (value >= 90) {
                return 'rgba(16, 185, 129, 0.8)'; // Excellent - Green
            } else if (value >= 80) {
                return 'rgba(59, 130, 246, 0.8)'; // Very Good - Blue
            } else if (value >= 70) {
                return 'rgba(245, 158, 11, 0.8)'; // Good - Yellow
            } else if (value >= 60) {
                return 'rgba(249, 115, 22, 0.8)'; // Acceptable - Orange
            } else {
                return 'rgba(239, 68, 68, 0.8)'; // Needs Improvement - Red
            }
        });

        const borderColors = usersData.map((user, index) => {
            const value = chartData[index];

            if (value >= 90) {
                return 'rgb(16, 185, 129)'; // Excellent - Green
            } else if (value >= 80) {
                return 'rgb(59, 130, 246)'; // Very Good - Blue
            } else if (value >= 70) {
                return 'rgb(245, 158, 11)'; // Good - Yellow
            } else if (value >= 60) {
                return 'rgb(249, 115, 22)'; // Acceptable - Orange
            } else {
                return 'rgb(239, 68, 68)'; // Needs Improvement - Red
            }
        });

        // Enhanced hover colors with glow effect
        const hoverBackgroundColors = usersData.map((user, index) => {
            const value = chartData[index];

            if (value >= 90) {
                return 'rgba(16, 185, 129, 1.0)'; // Excellent - Bright Green
            } else if (value >= 80) {
                return 'rgba(59, 130, 246, 1.0)'; // Very Good - Bright Blue
            } else if (value >= 70) {
                return 'rgba(245, 158, 11, 1.0)'; // Good - Bright Yellow
            } else if (value >= 60) {
                return 'rgba(249, 115, 22, 1.0)'; // Acceptable - Bright Orange
            } else {
                return 'rgba(239, 68, 68, 1.0)'; // Needs Improvement - Bright Red
            }
        });

        console.log('Creating chart with labels:', labels);
        console.log('Background colors:', backgroundColors);
        console.log('Border colors:', borderColors);

        usersProgressChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: chartLabel,
                    data: chartData,
                    backgroundColor: solidBackgroundColors,
                    borderColor: borderColors,
                    borderWidth: 3,
                    borderRadius: {
                        topLeft: 12,
                        topRight: 12,
                        bottomLeft: 4,
                        bottomRight: 4
                    },
                    borderSkipped: false,
                    hoverBackgroundColor: hoverBackgroundColors,
                    hoverBorderColor: borderColors.map(color => color.replace('rgb', 'rgba').replace(')', ', 1.0)')),
                    hoverBorderWidth: 4,
                    shadowOffsetX: 3,
                    shadowOffsetY: 3,
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.3)',
                    // Add gradient effect
                    tension: 0.4,
                    pointBackgroundColor: borderColors,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 3,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                layout: {
                    padding: {
                        top: 20,
                        bottom: 20,
                        left: 10,
                        right: 10
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 25,
                            font: {
                                size: 16,
                                weight: 'bold',
                                family: 'Inter, system-ui, sans-serif'
                            },
                            color: '#374151',
                            boxWidth: 15,
                            boxHeight: 15,
                            generateLabels: function(chart) {
                                return [{
                                    text: chartLabel,
                                    fillStyle: 'linear-gradient(135deg, #3B82F6, #8B5CF6)',
                                    strokeStyle: '#3B82F6',
                                    lineWidth: 2,
                                    pointStyle: 'rectRounded'
                                }];
                            }
                        }
                    },
                    tooltip: {
                        enabled: true,
                        backgroundColor: function(context) {
                            const value = context.tooltip.dataPoints[0].parsed.y;
                            if (value >= 90) return 'rgba(16, 185, 129, 0.95)';
                            if (value >= 80) return 'rgba(59, 130, 246, 0.95)';
                            if (value >= 70) return 'rgba(245, 158, 11, 0.95)';
                            if (value >= 60) return 'rgba(249, 115, 22, 0.95)';
                            return 'rgba(239, 68, 68, 0.95)';
                        },
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.4)',
                        borderWidth: 3,
                        cornerRadius: 16,
                        displayColors: false,
                        titleFont: {
                            size: 18,
                            weight: 'bold',
                            family: 'Inter, system-ui, sans-serif'
                        },
                        bodyFont: {
                            size: 15,
                            family: 'Inter, system-ui, sans-serif'
                        },
                        footerFont: {
                            size: 13,
                            style: 'italic'
                        },
                        padding: 16,
                        caretSize: 8,
                        caretPadding: 12,
                        multiKeyBackground: 'rgba(255, 255, 255, 0.1)',
                        usePointStyle: true,
                        boxWidth: 12,
                        boxHeight: 12,
                        animation: {
                            duration: 300,
                            easing: 'easeOutQuart'
                        },
                        callbacks: {
                            title: function(context) {
                                return '👤 ' + context[0].label;
                            },
                            beforeBody: function(context) {
                                const value = context[0].parsed.y.toFixed(1);
                                let emoji = '🎯';
                                if (value >= 90) emoji = '🏆';
                                else if (value >= 80) emoji = '⭐';
                                else if (value >= 70) emoji = '👍';
                                else if (value >= 60) emoji = '📈';

                                return `${emoji} ${value}% - ${chartLabel}`;
                            },
                            label: function(context) {
                                const user = usersData[context.dataIndex];
                                const performanceLevel = user.performance_level?.text || 'غير محدد';
                                return [
                                    `🎯 مستوى الأداء: ${performanceLevel}`,
                                    `⭐ إجمالي النقاط: ${user.stats?.total_points || 0}`,
                                    `📊 الطلبات المكتملة: ${user.performance?.total_completed || 0}`
                                ];
                            },
                            footer: function(context) {
                                return 'انقر للمزيد من التفاصيل';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: maxValue,
                        title: {
                            display: true,
                            text: 'نسبة الإنجاز (%)',
                            font: {
                                size: 16,
                                weight: 'bold',
                                family: 'Inter, system-ui, sans-serif'
                            },
                            color: '#1F2937',
                            padding: 20
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            },
                            font: {
                                size: 13,
                                weight: '500',
                                family: 'Inter, system-ui, sans-serif'
                            },
                            color: '#4B5563',
                            padding: 8,
                            stepSize: 10
                        },
                        grid: {
                            color: function(context) {
                                if (context.tick.value === 0) return 'rgba(0, 0, 0, 0.2)';
                                if (context.tick.value === 50) return 'rgba(245, 158, 11, 0.3)';
                                if (context.tick.value === 80) return 'rgba(59, 130, 246, 0.3)';
                                if (context.tick.value === 90) return 'rgba(16, 185, 129, 0.3)';
                                return 'rgba(0, 0, 0, 0.08)';
                            },
                            lineWidth: function(context) {
                                if ([0, 50, 80, 90].includes(context.tick.value)) return 2;
                                return 1;
                            },
                            drawBorder: false,
                            borderDash: function(context) {
                                if ([50, 80, 90].includes(context.tick.value)) return [5, 5];
                                return [];
                            }
                        },
                        border: {
                            display: false
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'المستخدمون',
                            font: {
                                size: 16,
                                weight: 'bold',
                                family: 'Inter, system-ui, sans-serif'
                            },
                            color: '#1F2937',
                            padding: 20
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 0,
                            font: {
                                size: 12,
                                weight: '500',
                                family: 'Inter, system-ui, sans-serif'
                            },
                            color: '#4B5563',
                            padding: 10
                        },
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 1800,
                    easing: 'easeInOutCubic',
                    delay: function(context) {
                        return context.dataIndex * 100; // Stagger animation
                    },
                    onProgress: function(animation) {
                        const progress = animation.currentStep / animation.numSteps;
                        const canvas = document.getElementById('usersProgressChart');
                        if (canvas) {
                            canvas.style.filter = `brightness(${0.7 + (progress * 0.3)}) saturate(${0.8 + (progress * 0.2)})`;
                        }
                    },
                    onComplete: function() {
                        console.log('Chart animation completed');
                        // Hide loading state and show chart with enhanced effects
                        setTimeout(() => {
                            const loading = document.getElementById('chartLoading');
                            const canvas = document.getElementById('usersProgressChart');

                            if (loading) {
                                loading.style.opacity = '0';
                                loading.style.transition = 'opacity 0.3s ease';
                                setTimeout(() => {
                                    loading.style.display = 'none';
                                }, 300);
                            }

                            if (canvas) {
                                canvas.classList.remove('opacity-0');
                                canvas.classList.add('opacity-100');
                                canvas.style.filter = 'brightness(1) saturate(1)';
                                canvas.style.visibility = 'visible';

                                // Add subtle glow effect
                                canvas.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.2)';
                            }

                            // Add sparkle effect
                            addSparkleEffect();
                        }, 500);
                    }
                },
                onHover: function(event, elements) {
                    const canvas = event.native.target;
                    if (elements.length > 0) {
                        canvas.style.cursor = 'pointer';
                        canvas.style.filter = 'brightness(1.1) saturate(1.2)';
                        canvas.style.transform = 'scale(1.02)';
                        canvas.style.transition = 'all 0.3s ease';
                    } else {
                        canvas.style.cursor = 'default';
                        canvas.style.filter = 'brightness(1) saturate(1)';
                        canvas.style.transform = 'scale(1)';
                    }
                },
                onClick: function(event, elements) {
                    if (elements.length > 0) {
                        const index = elements[0].index;
                        const user = usersData[index];
                        console.log('Clicked user:', user);

                        // Add click animation
                        const canvas = event.native.target;
                        canvas.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            canvas.style.transform = 'scale(1)';
                        }, 150);

                        // Show detailed user info with enhanced modal
                        showUserDetailsEnhanced(user);
                    }
                }
            }
        });

        console.log('Chart created successfully');

        // Immediately show the chart if animation is disabled or for fallback
        setTimeout(() => {
            const loading = document.getElementById('chartLoading');
            const canvas = document.getElementById('usersProgressChart');

            if (loading && loading.style.display !== 'none') {
                console.log('Forcing chart visibility...');
                loading.style.display = 'none';
            }

            if (canvas) {
                canvas.style.opacity = '1';
                canvas.style.visibility = 'visible';
                canvas.classList.remove('opacity-0');
                canvas.classList.add('opacity-100');
                console.log('Chart forced to visible state');
            }
        }, 2000); // Fallback after 2 seconds

        // Additional immediate visibility check
        setTimeout(() => {
            const canvas = document.getElementById('usersProgressChart');
            if (canvas && window.getComputedStyle(canvas).opacity === '0') {
                console.log('Chart still hidden, forcing visibility immediately...');
                canvas.style.opacity = '1';
                canvas.style.visibility = 'visible';

                const loading = document.getElementById('chartLoading');
                if (loading) loading.style.display = 'none';
            }
        }, 100);

    } catch (error) {
        console.error('Error creating chart:', error);
        showChartError();
    }
}

// Add sparkle effect to chart
function addSparkleEffect() {
    const chartContainer = document.getElementById('chartContainer');
    if (!chartContainer) return;

    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const sparkle = document.createElement('div');
            sparkle.className = 'absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping';
            sparkle.style.left = Math.random() * 100 + '%';
            sparkle.style.top = Math.random() * 100 + '%';
            sparkle.style.animationDelay = Math.random() * 2 + 's';
            chartContainer.appendChild(sparkle);

            setTimeout(() => sparkle.remove(), 3000);
        }, i * 200);
    }
}

// Enhanced function to show comprehensive user performance modal
function showUserDetailsEnhanced(user) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 backdrop-blur-sm';
    modal.style.animation = 'fadeIn 0.4s ease-out';

    const performanceColor = user.performance_level?.color || '#3B82F6';

    modal.innerHTML = `
        <div class="bg-white rounded-3xl max-w-4xl w-full mx-4 shadow-2xl transform transition-all duration-300 max-h-[90vh] overflow-y-auto"
             style="background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); border: 3px solid ${performanceColor}20;">

            <!-- Modal Header -->
            <div class="sticky top-0 bg-gradient-to-r from-white to-gray-50 rounded-t-3xl p-6 border-b border-gray-200 z-10">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="relative w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
                            ${(user.user_info?.full_name || user.full_name || 'U')[0]}
                            <div class="absolute -top-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center text-xs shadow-lg"
                                 style="background: ${performanceColor};">
                                ${user.performance_level?.icon || '📊'}
                            </div>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">${user.user_info?.full_name || user.full_name || 'مستخدم غير معروف'}</h2>
                            <p class="text-gray-600">${user.user_info?.email || 'لا يوجد بريد إلكتروني'}</p>
                            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold mt-2"
                                 style="background: ${performanceColor}20; color: ${performanceColor};">
                                <span class="mr-1">${user.performance_level?.icon || '📊'}</span>
                                ${user.performance_level?.text || 'غير محدد'}
                            </div>
                        </div>
                    </div>
                    <button onclick="this.closest('.fixed').remove()"
                            class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors">
                        <span class="text-gray-600 text-xl">×</span>
                    </button>
                </div>
            </div>

            <!-- Modal Content -->
            <div class="p-8 space-y-8">

                <!-- Overall Performance Section -->
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
                    <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                        <span class="text-2xl mr-3">📊</span>
                        الأداء العام والإحصائيات
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Overall Progress -->
                        <div class="bg-white rounded-xl p-6 shadow-sm">
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-gray-700 font-medium">نسبة الإنجاز الإجمالية</span>
                                <span class="text-3xl font-bold" style="color: ${performanceColor};">${(user.progress_scores?.overall || 0).toFixed(1)}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
                                <div class="h-4 rounded-full transition-all duration-1000 relative overflow-hidden"
                                     style="width: ${user.progress_scores?.overall || 0}%; background: linear-gradient(90deg, ${performanceColor}, ${performanceColor}80);">
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Key Stats -->
                        <div class="bg-white rounded-xl p-6 shadow-sm">
                            <h4 class="font-semibold text-gray-900 mb-4">الإحصائيات الرئيسية</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">إجمالي النقاط:</span>
                                    <span class="font-bold text-yellow-600">${user.stats?.total_points || 0}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">الطلبات المكتملة:</span>
                                    <span class="font-bold text-green-600">${user.performance?.total_completed || 0}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">معدل الإنجاز:</span>
                                    <span class="font-bold text-blue-600">${(user.performance?.completion_rate || 0).toFixed(1)}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">الإنجازات المكتملة:</span>
                                    <span class="font-bold text-purple-600">${user.stats?.total_achievements || 0}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Period Performance Section -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
                    <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                        <span class="text-2xl mr-3">📈</span>
                        الأداء حسب الفترة الزمنية
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Daily Performance -->
                        <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                            <div class="text-center mb-4">
                                <div class="text-3xl mb-2">📅</div>
                                <h4 class="font-bold text-blue-900">الأداء اليومي</h4>
                            </div>
                            <div class="space-y-3">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">${(user.progress_scores?.daily || 0).toFixed(1)}%</div>
                                    <div class="text-sm text-blue-700">نسبة الإنجاز</div>
                                </div>
                                <div class="w-full bg-blue-200 rounded-full h-3">
                                    <div class="bg-blue-600 h-3 rounded-full transition-all duration-500"
                                         style="width: ${user.progress_scores?.daily || 0}%"></div>
                                </div>
                                <div class="text-xs text-center text-blue-600">
                                    ${user.performance?.daily_completed || 0} من ${user.performance?.daily_target || 10} طلبات
                                </div>
                            </div>
                        </div>

                        <!-- Weekly Performance -->
                        <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                            <div class="text-center mb-4">
                                <div class="text-3xl mb-2">📊</div>
                                <h4 class="font-bold text-green-900">الأداء الأسبوعي</h4>
                            </div>
                            <div class="space-y-3">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">${(user.progress_scores?.weekly || 0).toFixed(1)}%</div>
                                    <div class="text-sm text-green-700">نسبة الإنجاز</div>
                                </div>
                                <div class="w-full bg-green-200 rounded-full h-3">
                                    <div class="bg-green-600 h-3 rounded-full transition-all duration-500"
                                         style="width: ${user.progress_scores?.weekly || 0}%"></div>
                                </div>
                                <div class="text-xs text-center text-green-600">
                                    ${user.performance?.weekly_completed || 0} من ${user.performance?.weekly_target || 50} طلبات
                                </div>
                            </div>
                        </div>

                        <!-- Monthly Performance -->
                        <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                            <div class="text-center mb-4">
                                <div class="text-3xl mb-2">📈</div>
                                <h4 class="font-bold text-purple-900">الأداء الشهري</h4>
                            </div>
                            <div class="space-y-3">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600">${(user.progress_scores?.monthly || 0).toFixed(1)}%</div>
                                    <div class="text-sm text-purple-700">نسبة الإنجاز</div>
                                </div>
                                <div class="w-full bg-purple-200 rounded-full h-3">
                                    <div class="bg-purple-600 h-3 rounded-full transition-all duration-500"
                                         style="width: ${user.progress_scores?.monthly || 0}%"></div>
                                </div>
                                <div class="text-xs text-center text-purple-600">
                                    ${user.performance?.monthly_completed || 0} من ${user.performance?.monthly_target || 200} طلبات
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Achievements and Streaks Section -->
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6 border border-yellow-200">
                    <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                        <span class="text-2xl mr-3">🏆</span>
                        الإنجازات والسلاسل
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="bg-white rounded-xl p-6 text-center shadow-sm">
                            <div class="text-3xl mb-2">⭐</div>
                            <div class="text-2xl font-bold text-yellow-600">${user.stats?.total_points || 0}</div>
                            <div class="text-sm text-yellow-700">إجمالي النقاط</div>
                        </div>
                        <div class="bg-white rounded-xl p-6 text-center shadow-sm">
                            <div class="text-3xl mb-2">🏅</div>
                            <div class="text-2xl font-bold text-orange-600">${user.stats?.total_achievements || 0}</div>
                            <div class="text-sm text-orange-700">الإنجازات المكتملة</div>
                        </div>
                        <div class="bg-white rounded-xl p-6 text-center shadow-sm">
                            <div class="text-3xl mb-2">🔥</div>
                            <div class="text-2xl font-bold text-red-600">${user.stats?.current_daily_streak || 0}</div>
                            <div class="text-sm text-red-700">السلسلة الحالية</div>
                        </div>
                        <div class="bg-white rounded-xl p-6 text-center shadow-sm">
                            <div class="text-3xl mb-2">💪</div>
                            <div class="text-2xl font-bold text-purple-600">${user.stats?.longest_daily_streak || 0}</div>
                            <div class="text-sm text-purple-700">أطول سلسلة</div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4 rtl:space-x-reverse pt-4">
                    <button onclick="this.closest('.fixed').remove()"
                            class="px-8 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">✖️</span>
                            إغلاق
                        </span>
                    </button>
                    <button onclick="exportUserData('${user.user_info?.full_name || user.full_name || 'user'}')"
                            class="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <span class="flex items-center">
                            <span class="text-lg mr-2">📊</span>
                            تصدير البيانات
                        </span>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add entrance animation
    setTimeout(() => {
        modal.querySelector('div').style.transform = 'scale(1)';
    }, 10);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.animation = 'fadeOut 0.3s ease-in';
            setTimeout(() => modal.remove(), 300);
        }
    });

    // Add keyboard support
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            modal.style.animation = 'fadeOut 0.3s ease-in';
            setTimeout(() => modal.remove(), 300);
        }
    });
}

// Function to export individual user data
function exportUserData(userName) {
    // This would typically make an API call to get detailed user data
    console.log('Exporting data for user:', userName);

    // For now, show a success message
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    toast.innerHTML = `
        <div class="flex items-center">
            <span class="text-lg mr-2">✅</span>
            تم تصدير بيانات ${userName} بنجاح
        </div>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'fadeOut 0.3s ease-in';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// Legacy function for compatibility
function showUserDetails(user) {
    showUserDetailsEnhanced(user);
}
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
            <div class="text-center mb-6">
                <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                    ${(user.user_info?.full_name || user.full_name || 'U')[0]}
                </div>
                <h3 class="text-2xl font-bold text-gray-900">${user.user_info?.full_name || user.full_name || 'مستخدم غير معروف'}</h3>
                <p class="text-gray-600">${user.user_info?.email || 'لا يوجد بريد إلكتروني'}</p>
            </div>

            <div class="space-y-4">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-2">📊 الأداء العام</h4>
                    <div class="text-2xl font-bold text-blue-600">${(user.progress_scores?.overall || 0).toFixed(1)}%</div>
                </div>

                <div class="grid grid-cols-3 gap-3">
                    <div class="text-center bg-blue-50 rounded-lg p-3">
                        <div class="text-lg font-bold text-blue-600">${(user.progress_scores?.daily || 0).toFixed(0)}%</div>
                        <div class="text-xs text-blue-700">يومي</div>
                    </div>
                    <div class="text-center bg-green-50 rounded-lg p-3">
                        <div class="text-lg font-bold text-green-600">${(user.progress_scores?.weekly || 0).toFixed(0)}%</div>
                        <div class="text-xs text-green-700">أسبوعي</div>
                    </div>
                    <div class="text-center bg-purple-50 rounded-lg p-3">
                        <div class="text-lg font-bold text-purple-600">${(user.progress_scores?.monthly || 0).toFixed(0)}%</div>
                        <div class="text-xs text-purple-700">شهري</div>
                    </div>
                </div>

                <div class="bg-yellow-50 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-2">⭐ النقاط والإنجازات</h4>
                    <div class="flex justify-between">
                        <span class="text-gray-700">إجمالي النقاط:</span>
                        <span class="font-bold text-yellow-600">${user.stats?.total_points || 0}</span>
                    </div>
                </div>
            </div>

            <button onclick="this.parentElement.parentElement.remove()" class="w-full mt-6 bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg font-semibold transition-colors">
                إغلاق
            </button>
        </div>
    `;

    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function setupUsersFiltering() {
    const searchInput = document.getElementById('searchUsersInput');
    const performanceFilter = document.getElementById('filterUsersPerformance');

    if (searchInput) {
        searchInput.addEventListener('input', filterUsersTable);
    }

    if (performanceFilter) {
        performanceFilter.addEventListener('change', filterUsersTable);
    }
}

function filterUsersTable() {
    const searchTerm = document.getElementById('searchUsersInput').value.toLowerCase();
    const performanceFilter = document.getElementById('filterUsersPerformance').value;
    const rows = document.querySelectorAll('.users-progress-row');

    rows.forEach(row => {
        const userName = row.getAttribute('data-user-name');
        const userPerformance = row.getAttribute('data-performance');

        const matchesSearch = userName.includes(searchTerm);
        const matchesPerformance = !performanceFilter || userPerformance === performanceFilter;

        if (matchesSearch && matchesPerformance) {
            row.style.display = '';
            // Add animation
            row.style.opacity = '0';
            row.style.transform = 'translateY(10px)';
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, 50);
        } else {
            row.style.display = 'none';
        }
    });
}

function refreshAllUsersData() {
    const button = event.target;
    const originalText = button.innerHTML;

    button.innerHTML = '🔄 جاري التحديث...';
    button.disabled = true;

    // Simulate refresh (in real implementation, this would make an API call)
    setTimeout(() => {
        button.innerHTML = '✅ تم التحديث';
        button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        button.classList.add('bg-green-600', 'hover:bg-green-700');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('bg-green-600', 'hover:bg-green-700');
            button.classList.add('bg-blue-600', 'hover:bg-blue-700');
            button.disabled = false;
        }, 2000);
    }, 1500);
}

function exportUsersData() {
    const button = event.target;
    const originalText = button.innerHTML;

    button.innerHTML = '📊 جاري التصدير...';
    button.disabled = true;

    // Check if there's data to export
    if (!usersData || usersData.length === 0) {
        button.innerHTML = '❌ لا توجد بيانات للتصدير';
        button.classList.remove('bg-green-600', 'hover:bg-green-700');
        button.classList.add('bg-red-600', 'hover:bg-red-700');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('bg-red-600', 'hover:bg-red-700');
            button.classList.add('bg-green-600', 'hover:bg-green-700');
            button.disabled = false;
        }, 2000);
        return;
    }

    // Create CSV data
    const headers = ['الاسم', 'اسم المستخدم', 'مستوى الأداء', 'الأداء العام', 'التقدم اليومي', 'التقدم الأسبوعي', 'التقدم الشهري', 'النقاط', 'معدل الإنجاز'];
    const csvData = [headers];

    usersData.forEach(user => {
        csvData.push([
            user.user_info.full_name,
            user.user_info.username,
            user.performance_level.text,
            user.progress_scores.overall.toFixed(1) + '%',
            user.progress_scores.daily.toFixed(1) + '%',
            user.progress_scores.weekly.toFixed(1) + '%',
            user.progress_scores.monthly.toFixed(1) + '%',
            user.stats.total_points,
            user.performance.completion_rate.toFixed(1) + '%'
        ]);
    });

    // Convert to CSV string
    const csvString = csvData.map(row => row.join(',')).join('\n');

    // Create and download file
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `users_progress_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button
    setTimeout(() => {
        button.innerHTML = '✅ تم التصدير';
        button.classList.remove('bg-green-600', 'hover:bg-green-700');
        button.classList.add('bg-blue-600', 'hover:bg-blue-700');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 2000);
    }, 1000);
}
{% endif %}
</script>

<style>
{% if current_user.role.value == 'admin' %}
.users-chart-filter-btn {
    @apply px-6 py-3 text-sm font-semibold rounded-xl transition-all duration-300;
    @apply text-gray-700 bg-white border border-gray-200 shadow-md;
    @apply hover:scale-105 hover:shadow-lg;
}

.users-chart-filter-btn.active {
    @apply bg-blue-600 text-white border-blue-600 shadow-lg;
    @apply transform scale-105;
}

.users-chart-filter-btn:hover:not(.active) {
    @apply bg-gray-50 border-gray-300;
}

/* Enhanced Chart container animations */
#chartContainer {
    @apply transition-all duration-500;
    position: relative;
    overflow: hidden;
}

#chartContainer::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
    pointer-events: none;
    z-index: 1;
}

#usersProgressChart {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

/* Enhanced Loading animation */
@keyframes pulse-chart {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: scale(1); }
    to { opacity: 0; transform: scale(0.9); }
}

@keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
}

.chart-loading {
    animation: pulse-chart 2s infinite;
}

/* Chart hover effects */
.chart-container:hover #usersProgressChart {
    filter: brightness(1.05) saturate(1.1);
}

/* Performance level colors for chart elements */
.performance-excellent { color: #10B981; background-color: rgba(16, 185, 129, 0.1); }
.performance-very-good { color: #3B82F6; background-color: rgba(59, 130, 246, 0.1); }
.performance-good { color: #F59E0B; background-color: rgba(245, 158, 11, 0.1); }
.performance-acceptable { color: #F97316; background-color: rgba(249, 115, 22, 0.1); }
.performance-needs-improvement { color: #EF4444; background-color: rgba(239, 68, 68, 0.1); }

.users-progress-row {
    transition: all 0.2s ease;
}

.users-progress-row:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
{% endif %}
</style>

{% endblock %}
