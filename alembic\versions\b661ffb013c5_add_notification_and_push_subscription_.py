"""Add notification and push subscription tables

Revision ID: b661ffb013c5
Revises: add_user_approval_status
Create Date: 2025-07-10 03:40:48.859779

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b661ffb013c5'
down_revision = 'add_user_approval_status'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_notifications_id'), table_name='notifications')
    op.drop_table('notifications')
    op.drop_index(op.f('ix_push_subscriptions_id'), table_name='push_subscriptions')
    op.drop_table('push_subscriptions')
    op.drop_index(op.f('ix_notification_preferences_id'), table_name='notification_preferences')
    op.drop_table('notification_preferences')
    op.alter_column('users', 'approval_status',
               existing_type=postgresql.ENUM('PENDING', 'APPROVED', 'REJECTED', name='userstatus'),
               server_default=None,
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'approval_status',
               existing_type=postgresql.ENUM('PENDING', 'APPROVED', 'REJECTED', name='userstatus'),
               server_default=sa.text("'PENDING'::userstatus"),
               existing_nullable=False)
    op.create_table('notification_preferences',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('push_notifications_enabled', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('in_app_notifications_enabled', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('email_notifications_enabled', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('request_status_notifications', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('request_updates_notifications', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('admin_message_notifications', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('system_announcement_notifications', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('quiet_hours_enabled', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('quiet_hours_start', sa.VARCHAR(length=5), autoincrement=False, nullable=True),
    sa.Column('quiet_hours_end', sa.VARCHAR(length=5), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('notification_preferences_user_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('notification_preferences_pkey')),
    sa.UniqueConstraint('user_id', name=op.f('notification_preferences_user_id_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_notification_preferences_id'), 'notification_preferences', ['id'], unique=False)
    op.create_table('push_subscriptions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('endpoint', sa.VARCHAR(length=500), autoincrement=False, nullable=False),
    sa.Column('p256dh_key', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('auth_key', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('user_agent', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('device_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('last_used', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('push_subscriptions_user_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('push_subscriptions_pkey'))
    )
    op.create_index(op.f('ix_push_subscriptions_id'), 'push_subscriptions', ['id'], unique=False)
    op.create_table('notifications',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('type', postgresql.ENUM('REQUEST_STATUS_CHANGED', 'REQUEST_CREATED', 'REQUEST_UPDATED', 'REQUEST_ARCHIVED', 'REQUEST_DELETED', 'ADMIN_MESSAGE', 'SYSTEM_ANNOUNCEMENT', name='notificationtype'), autoincrement=False, nullable=False),
    sa.Column('priority', postgresql.ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT', name='notificationpriority'), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('message', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('action_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('request_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('related_user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('is_read', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('is_sent', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('sent_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('read_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('extra_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['related_user_id'], ['users.id'], name=op.f('notifications_related_user_id_fkey')),
    sa.ForeignKeyConstraint(['request_id'], ['requests.id'], name=op.f('notifications_request_id_fkey')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('notifications_user_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('notifications_pkey'))
    )
    op.create_index(op.f('ix_notifications_id'), 'notifications', ['id'], unique=False)
    # ### end Alembic commands ###
