{% extends "base.html" %}

{% block title %}تعديل الطلب {{ req.request_number }} - CMSVS{% endblock %}

{% block content %}
<style>
/* Professional Edit Request Styling */
.page-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #ffffff;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-bottom: 2px solid #e5e7eb;
    padding: 24px 0;
    margin-bottom: 32px;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 8px 0;
}

.page-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.breadcrumb {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.section {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 24px;
}

.section-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-content {
    padding: 24px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-help {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.status-pending { background: #fef3c7; color: #92400e; }
.status-in-progress { background: #dbeafe; color: #1e40af; }
.status-completed { background: #d1fae5; color: #065f46; }
.status-rejected { background: #fee2e2; color: #991b1b; }

.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background-color: #f9fafb;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.info-value {
    font-size: 14px;
    color: #111827;
    font-weight: 500;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    background: #f9fafb;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.upload-icon {
    width: 48px;
    height: 48px;
    background-color: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: #ffffff;
    font-size: 20px;
}

.file-preview {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    margin-bottom: 8px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.file-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 14px;
    flex-shrink: 0;
}

.file-icon.pdf { background-color: #dc2626; }
.file-icon.image { background-color: #7c3aed; }
.file-icon.document { background-color: #2563eb; }
.file-icon.text { background-color: #6b7280; }
.file-icon.default { background-color: #6b7280; }

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #111827;
    font-size: 14px;
    margin-bottom: 2px;
}

.file-size {
    font-size: 12px;
    color: #6b7280;
}

.existing-file {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.file-info-mobile {
    flex: 1;
    min-width: 0;
}

.file-header-mobile {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 8px;
}

.file-details-mobile {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.file-name {
    font-weight: 500;
    color: #111827;
    font-size: 14px;
    margin-bottom: 4px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
}

.file-meta-mobile {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    margin-bottom: 4px;
}

.file-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-start;
}

/* Desktop-first responsive design with mobile optimizations */

/* Desktop styles (default - 769px and above) */
.existing-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
}

.upload-areas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

/* Mobile responsive improvements for file management */
@media (max-width: 768px) {
    .existing-files-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .existing-file {
        padding: 12px;
        gap: 10px;
    }

    .file-header-mobile {
        gap: 10px;
        margin-bottom: 6px;
    }

    .file-name {
        font-size: 13px;
        margin-bottom: 3px;
    }

    .file-meta-mobile {
        gap: 6px;
        margin-bottom: 3px;
    }

    .file-meta-mobile span {
        font-size: 10px !important;
    }

    .file-actions {
        gap: 6px;
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #f3f4f6;
    }

    .file-actions button {
        font-size: 10px !important;
        padding: 4px 8px !important;
    }

    .upload-areas-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}

.section-header-mobile {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.file-info-mobile {
    display: block;
}

.file-header-mobile {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
}

.file-details-mobile {
    flex: 1;
    min-width: 0;
}

.file-meta-mobile {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 4px;
}

.file-actions {
    display: flex;
    gap: 8px;
}

/* Tablet styles (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 18px;
    }

    .page-container {
        padding: 16px;
    }

    .section-content {
        padding: 20px;
    }

    /* Tablet file grid - 2 columns */
    .existing-files-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 14px;
    }

    /* Tablet upload areas - 2 columns */
    .upload-areas-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 18px;
    }

    .file-actions {
        gap: 8px;
    }

    .file-actions button {
        padding: 7px 12px;
        font-size: 12px;
    }

    .upload-area {
        padding: 18px;
        min-height: 90px;
    }

    .section-title {
        font-size: 17px;
    }
}

/* Mobile styles (480px and below) */
@media (max-width: 480px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .page-container {
        padding: 12px;
    }

    .section-content {
        padding: 16px;
    }

    /* Mobile file grid - single column */
    .existing-files-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .existing-file {
        padding: 12px;
        margin-bottom: 8px;
    }

    .file-info-mobile {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .file-header-mobile {
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .file-details-mobile {
        flex: 1;
        min-width: 0;
    }

    .file-name {
        font-size: 13px;
        line-height: 1.3;
        word-break: break-word;
    }

    .file-meta-mobile {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 6px;
        font-size: 11px;
    }

    .file-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-top: 8px;
    }

    .file-actions button {
        flex: 1;
        min-width: 70px;
        padding: 8px 10px;
        font-size: 11px;
        text-align: center;
    }

    /* Mobile upload areas - single column */
    .upload-areas-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .upload-area {
        padding: 16px;
        min-height: 80px;
    }

    .upload-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .upload-text {
        font-size: 13px;
    }

    .upload-hint {
        font-size: 11px;
        margin-top: 4px;
    }

    /* Mobile section headers */
    .section-header-mobile {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 12px;
    }

    .section-title {
        font-size: 16px;
    }

    .section-subtitle {
        font-size: 12px;
        color: #6b7280;
    }

    /* Mobile file preview */
    .file-preview {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-top: 8px;
    }

    .file-item {
        padding: 8px;
        font-size: 12px;
    }
}

/* General responsive improvements for all screen sizes */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .page-container {
        padding: 16px;
    }

    /* Touch-friendly buttons for tablet and mobile */
    .btn {
        min-height: 44px;
        touch-action: manipulation;
    }

    /* Improved text readability */
    .form-control {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Upload area improvements for touch devices */
    .upload-area {
        border-width: 2px;
        border-style: dashed;
        border-color: #d1d5db;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        touch-action: manipulation;
    }

    .upload-area:active {
        border-color: #3b82f6;
        background-color: #f0f9ff;
        transform: scale(0.98);
    }
}

/* Mobile-specific improvements (480px and below) */
@media (max-width: 480px) {
    /* Mobile section title adjustments */
    .section-title {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .section-title span {
        margin-right: 0;
        margin-top: 4px;
    }

    /* Mobile info box */
    .mobile-info-box {
        background: #eff6ff;
        border: 1px solid #bfdbfe;
        border-radius: 6px;
        padding: 10px;
        margin-top: 12px;
        font-size: 12px;
    }

    /* Mobile file icon sizing */
    .file-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        font-size: 16px;
        flex-shrink: 0;
    }

    /* Mobile form action buttons */
    .form-actions-mobile {
        display: flex !important;
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }

    .form-actions-desktop {
        display: none !important;
    }

    .form-actions-mobile .btn {
        width: 100%;
        justify-content: center;
        min-height: 48px;
        font-size: 16px;
        padding: 12px 16px;
    }

    /* Mobile page header improvements */
    .page-header-mobile {
        display: flex !important;
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .page-header-desktop {
        display: none !important;
    }

    .page-header-mobile .page-title {
        font-size: 24px;
        line-height: 1.2;
    }

    .page-header-mobile .page-subtitle {
        font-size: 14px;
    }

    /* Mobile breadcrumb improvements */
    .breadcrumb {
        padding: 10px 12px;
        font-size: 13px;
        line-height: 1.4;
    }

    /* Mobile section header improvements */
    .section-header {
        padding: 16px 20px;
    }

    .section-header-mobile {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .section-header-mobile h3 {
        font-size: 15px !important;
    }

    .section-header-mobile .section-subtitle {
        font-size: 11px !important;
        color: #6b7280 !important;
    }

    /* Mobile form improvements */
    .form-control {
        min-height: 44px;
        font-size: 16px; /* Prevents zoom on iOS */
        border-radius: 8px;
    }

    .form-label {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .form-help {
        font-size: 12px;
        margin-top: 4px;
    }

    /* Mobile checkbox and radio improvements */
    input[type="checkbox"], input[type="radio"] {
        width: 18px;
        height: 18px;
        margin-left: 8px;
    }

    /* Mobile upload area improvements */
    .upload-area {
        min-height: 100px;
        padding: 16px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    .upload-area:active {
        transform: scale(0.98);
        background-color: #f0f9ff;
    }

    /* Mobile status badge improvements */
    .status-badge {
        font-size: 12px;
        padding: 6px 10px;
        border-radius: 6px;
    }

    /* Mobile info grid improvements */
    .info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .info-item {
        padding: 12px;
        border-radius: 6px;
    }

    .info-label {
        font-size: 12px;
        margin-bottom: 4px;
    }

    .info-value {
        font-size: 14px;
    }
}

/* Desktop enhancements (769px and above) */
@media (min-width: 769px) {
    .page-container {
        padding: 24px;
    }

    .section-content {
        padding: 24px;
    }

    /* Desktop file grid - optimal spacing */
    .existing-files-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 20px;
    }

    /* Desktop upload areas - optimal spacing */
    .upload-areas-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 24px;
    }

    .upload-area {
        padding: 24px;
        min-height: 120px;
    }

    .file-actions button {
        padding: 8px 16px;
        font-size: 13px;
    }
}
</style>

<!-- Professional Page Container -->
<div class="page-container">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb">
        <a href="/dashboard">لوحة التحكم</a> /
        <a href="/requests">الطلبات</a> /
        <a href="/requests/{{ req.id }}">{{ req.request_number }}</a> /
        <span>تعديل</span>
    </nav>

    <!-- Page Header -->
    <header class="page-header">
        <div class="page-header-desktop" style="display: flex; justify-content: space-between; align-items: flex-start; gap: 20px;">
            <div style="flex: 1;">
                <h1 class="page-title">تعديل الطلب {{ req.request_number }}</h1>
                <p class="page-subtitle">تعديل بيانات الطلب - الدفاع المدني</p>
            </div>
            <div style="display: flex; align-items: center; gap: 12px;">
                {% if req.status.value == 'PENDING' %}
                    <span class="status-badge status-pending">
                        <i class="fas fa-clock"></i>
                        قيد المراجعة
                    </span>
                {% elif req.status.value == 'IN_PROGRESS' %}
                    <span class="status-badge status-in-progress">
                        <i class="fas fa-cog"></i>
                        قيد التنفيذ
                    </span>
                {% elif req.status.value == 'COMPLETED' %}
                    <span class="status-badge status-completed">
                        <i class="fas fa-check-circle"></i>
                        مكتمل
                    </span>
                {% elif req.status.value == 'REJECTED' %}
                    <span class="status-badge status-rejected">
                        <i class="fas fa-times-circle"></i>
                        مرفوض
                    </span>
                {% endif %}
                <a href="/requests/{{ req.id }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للعرض
                </a>
            </div>
        </div>
        <div class="page-header-mobile" style="display: none;">
            <div>
                <h1 class="page-title">تعديل الطلب {{ req.request_number }}</h1>
                <p class="page-subtitle">تعديل بيانات الطلب - الدفاع المدني</p>
            </div>
            <div style="display: flex; flex-direction: column; gap: 8px; align-items: flex-start;">
                {% if req.status.value == 'PENDING' %}
                    <span class="status-badge status-pending">
                        <i class="fas fa-clock"></i>
                        قيد المراجعة
                    </span>
                {% elif req.status.value == 'IN_PROGRESS' %}
                    <span class="status-badge status-in-progress">
                        <i class="fas fa-cog"></i>
                        قيد التنفيذ
                    </span>
                {% elif req.status.value == 'COMPLETED' %}
                    <span class="status-badge status-completed">
                        <i class="fas fa-check-circle"></i>
                        مكتمل
                    </span>
                {% elif req.status.value == 'REJECTED' %}
                    <span class="status-badge status-rejected">
                        <i class="fas fa-times-circle"></i>
                        مرفوض
                    </span>
                {% endif %}
                <a href="/requests/{{ req.id }}" class="btn btn-secondary" style="width: 100%; justify-content: center;">
                    <i class="fas fa-arrow-right"></i>
                    العودة للعرض
                </a>
            </div>
        </div>
    </header>

    <!-- Request Info -->
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-info-circle" style="color: #3b82f6;"></i>
                معلومات الطلب
            </h2>
        </div>
        <div class="section-content">
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">رقم الطلب</div>
                    <div class="info-value">{{ req.request_number }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">الرمز التعريفي</div>
                    <div class="info-value">{{ req.unique_code }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">تاريخ الإنشاء</div>
                    <div class="info-value">{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">مقدم الطلب</div>
                    <div class="info-value">{{ req.user.email }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error and Success Messages -->
    {% if error %}
    <div class="section">
        <div class="section-content">
            <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <i class="fas fa-exclamation-triangle" style="color: #dc2626; margin-top: 2px; flex-shrink: 0;"></i>
                    <div>
                        <div style="font-weight: 600; color: #991b1b; font-size: 14px; margin-bottom: 4px;">خطأ في التحديث</div>
                        <div style="color: #991b1b; font-size: 13px;">{{ error }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if success %}
    <div class="section">
        <div class="section-content">
            <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <i class="fas fa-check-circle" style="color: #16a34a; margin-top: 2px; flex-shrink: 0;"></i>
                    <div>
                        <div style="font-weight: 600; color: #15803d; font-size: 14px; margin-bottom: 4px;">تم التحديث بنجاح</div>
                        <div style="color: #15803d; font-size: 13px;">{{ success }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if file_upload_warnings %}
    <div class="section">
        <div class="section-content">
            <div style="background: #fffbeb; border: 1px solid #fed7aa; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <i class="fas fa-exclamation-triangle" style="color: #f59e0b; margin-top: 2px; flex-shrink: 0;"></i>
                    <div>
                        <div style="font-weight: 600; color: #92400e; font-size: 14px; margin-bottom: 8px;">تحذيرات رفع الملفات</div>
                        {% for warning in file_upload_warnings %}
                        <div style="color: #92400e; font-size: 13px; margin-bottom: 4px;">• {{ warning }}</div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Edit Form -->
    <form method="post" action="/requests/{{ req.id }}/edit" id="editRequestForm" enctype="multipart/form-data">
        <!-- Hidden fields for service sections (read-only but need to be submitted) -->
        <input type="hidden" name="licenses_section" value="{{ 'true' if req.licenses_section else 'false' }}">
        <input type="hidden" name="fire_equipment_section" value="{{ 'true' if req.fire_equipment_section else 'false' }}">
        <input type="hidden" name="commercial_records_section" value="{{ 'true' if req.commercial_records_section else 'false' }}">
        <input type="hidden" name="engineering_offices_section" value="{{ 'true' if req.engineering_offices_section else 'false' }}">
        <input type="hidden" name="hazardous_materials_section" value="{{ 'true' if req.hazardous_materials_section else 'false' }}">
        <!-- Personal Information Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-user" style="color: #10b981;"></i>
                    المعلومات الشخصية
                </h2>
            </div>
            <div class="section-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="full_name" class="form-label">
                            الاسم الثلاثي <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="text"
                               id="full_name"
                               name="full_name"
                               value="{{ req.full_name or '' }}"
                               required
                               class="form-control">
                        <div class="form-help">يرجى إدخال الاسم الثلاثي كاملاً</div>
                    </div>
                    <div class="form-group">
                        <label for="personal_number" class="form-label">
                            الرقم الشخصي <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="text"
                               id="personal_number"
                               name="personal_number"
                               value="{{ req.personal_number or '' }}"
                               pattern="[0-9]{9}"
                               maxlength="9"
                               required
                               class="form-control">
                        <div class="form-help">9 أرقام بالضبط</div>
                    </div>
                    <div class="form-group">
                        <label for="phone_number" class="form-label">
                            رقم الهاتف
                        </label>
                        <input type="tel"
                               id="phone_number"
                               name="phone_number"
                               value="{{ req.phone_number or '' }}"
                               class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- Building Information Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-building" style="color: #f59e0b;"></i>
                    معلومات المبنى
                </h2>
            </div>
            <div class="section-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="building_name" class="form-label">
                            المبنى
                        </label>
                        <input type="text"
                               id="building_name"
                               name="building_name"
                               value="{{ req.building_name or '' }}"
                               class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="road_name" class="form-label">
                            الطريق
                        </label>
                        <input type="text"
                               id="road_name"
                               name="road_name"
                               value="{{ req.road_name or '' }}"
                               class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="building_number" class="form-label">
                            المجمع
                        </label>
                        <input type="text"
                               id="building_number"
                               name="building_number"
                               value="{{ req.building_number or '' }}"
                               class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="civil_defense_file_number" class="form-label">
                            رقم ملف الدفاع المدني
                        </label>
                        <input type="text"
                               id="civil_defense_file_number"
                               name="civil_defense_file_number"
                               value="{{ req.civil_defense_file_number or '' }}"
                               class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="building_permit_number" class="form-label">
                            رقم إجازة البناء
                        </label>
                        <input type="text"
                               id="building_permit_number"
                               name="building_permit_number"
                               value="{{ req.building_permit_number or '' }}"
                               class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- Services Required Section (Read Only) -->
        {% set has_services = req.licenses_section or req.fire_equipment_section or req.commercial_records_section or req.engineering_offices_section or req.hazardous_materials_section %}
        {% if has_services %}
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-cogs" style="color: #7c3aed;"></i>
                    الأقسام المطلوبة (للقراءة فقط)
                </h2>
            </div>
            <div class="section-content">
                <div style="background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
                    <div style="display: flex; align-items: flex-start; gap: 8px;">
                        <i class="fas fa-info-circle" style="color: #2563eb; margin-top: 2px;"></i>
                        <div>
                            <div style="font-weight: 600; color: #1e40af; font-size: 14px; margin-bottom: 4px;">ملاحظة مهمة</div>
                            <div style="color: #1e40af; font-size: 13px;">
                                هذه الأقسام تم تحديدها عند إنشاء الطلب ولا يمكن تعديلها. تحدد هذه الاختيارات فئات الملفات المتاحة للرفع.
                            </div>
                        </div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                    {% if req.licenses_section %}
                    <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px;">
                        <div style="width: 32px; height: 32px; background: #dbeafe; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-certificate" style="color: #2563eb; font-size: 14px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #111827; font-size: 14px; margin-bottom: 2px;">قسم التراخيص</div>
                            <div style="font-size: 12px; color: #6b7280;">إصدار وتجديد التراخيص المختلفة</div>
                        </div>
                        <span style="background: #d1fae5; color: #065f46; font-size: 11px; font-weight: 600; padding: 4px 8px; border-radius: 12px;">مطلوب</span>
                    </div>
                    {% endif %}

                    {% if req.fire_equipment_section %}
                    <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px;">
                        <div style="width: 32px; height: 32px; background: #fee2e2; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-fire-extinguisher" style="color: #dc2626; font-size: 14px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #111827; font-size: 14px; margin-bottom: 2px;">قسم معدات الإطفاء</div>
                            <div style="font-size: 12px; color: #6b7280;">فحص وصيانة معدات الإطفاء والسلامة</div>
                        </div>
                        <span style="background: #d1fae5; color: #065f46; font-size: 11px; font-weight: 600; padding: 4px 8px; border-radius: 12px;">مطلوب</span>
                    </div>
                    {% endif %}

                    {% if req.commercial_records_section %}
                    <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px;">
                        <div style="width: 32px; height: 32px; background: #d1fae5; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-file-contract" style="color: #10b981; font-size: 14px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #111827; font-size: 14px; margin-bottom: 2px;">قسم السجلات التجارية</div>
                            <div style="font-size: 12px; color: #6b7280;">إدارة السجلات والوثائق التجارية</div>
                        </div>
                        <span style="background: #d1fae5; color: #065f46; font-size: 11px; font-weight: 600; padding: 4px 8px; border-radius: 12px;">مطلوب</span>
                    </div>
                    {% endif %}

                    {% if req.engineering_offices_section %}
                    <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px;">
                        <div style="width: 32px; height: 32px; background: #fef3c7; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-drafting-compass" style="color: #f59e0b; font-size: 14px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #111827; font-size: 14px; margin-bottom: 2px;">قسم المكاتب الهندسية</div>
                            <div style="font-size: 12px; color: #6b7280;">الاستشارات والخدمات الهندسية</div>
                        </div>
                        <span style="background: #d1fae5; color: #065f46; font-size: 11px; font-weight: 600; padding: 4px 8px; border-radius: 12px;">مطلوب</span>
                    </div>
                    {% endif %}

                    {% if req.hazardous_materials_section %}
                    <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px;">
                        <div style="width: 32px; height: 32px; background: #fed7aa; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-exclamation-triangle" style="color: #ea580c; font-size: 14px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #111827; font-size: 14px; margin-bottom: 2px;">قسم المواد الخطرة</div>
                            <div style="font-size: 12px; color: #6b7280;">التعامل مع المواد الخطرة والكيميائية</div>
                        </div>
                        <span style="background: #d1fae5; color: #065f46; font-size: 11px; font-weight: 600; padding: 4px 8px; border-radius: 12px;">مطلوب</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Additional Information Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-info-circle" style="color: #6366f1;"></i>
                    معلومات إضافية
                </h2>
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label for="description" class="form-label">
                        وصف الطلب
                    </label>
                    <textarea id="description"
                              name="description"
                              rows="4"
                              placeholder="اكتب وصفاً تفصيلياً للطلب..."
                              class="form-control">{{ req.description or '' }}</textarea>
                    <div class="form-help">وصف تفصيلي لمحتوى الطلب ومتطلباته</div>
                </div>
            </div>
        </div>

        <!-- Status Controls Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-tasks" style="color: #3b82f6;"></i>
                    حالة الطلب
                </h2>
            </div>
            <div class="section-content">
                {% if current_user.role.value == 'ADMIN' %}
                <div style="background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
                    <div style="display: flex; align-items: flex-start; gap: 8px;">
                        <i class="fas fa-info-circle" style="color: #2563eb; margin-top: 2px;"></i>
                        <div style="color: #1e40af; font-size: 14px;">
                            <strong>ملاحظة:</strong> يمكنك تغيير حالة الطلب إلى أي حالة متاحة
                        </div>
                    </div>
                </div>
                {% else %}
                <div style="background: #ecfdf5; border: 1px solid #a7f3d0; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
                    <div style="display: flex; align-items: flex-start; gap: 8px;">
                        <i class="fas fa-check-circle" style="color: #059669; margin-top: 2px;"></i>
                        <div style="color: #065f46; font-size: 14px;">
                            <strong>معلومة:</strong> يمكنك تحديث حالة طلبك حسب التقدم المحرز
                        </div>
                    </div>
                </div>
                {% endif %}
                <div class="form-grid">
                    <div class="form-group">
                        <label for="status" class="form-label">
                            حالة الطلب
                        </label>
                        <select id="status" name="status" class="form-control">
                            {% for status_option in statuses %}
                            <option value="{{ status_option }}" {% if req.status.value == status_option %}selected{% endif %}>
                                {% if status_option == 'pending' %}معلق
                                {% elif status_option == 'in_progress' %}قيد المعالجة
                                {% elif status_option == 'completed' %}مكتمل
                                {% elif status_option == 'rejected' %}مرفوض
                                {% else %}{{ status_option }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-help">يمكنك تحديث حالة الطلب حسب التقدم المحرز</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            مقدم الطلب
                        </label>
                        <div style="padding: 10px 12px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px;">
                            <div style="font-size: 14px; color: #111827;">{{ req.user.email }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Request Timestamps Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-calendar" style="color: #8b5cf6;"></i>
                    التواريخ المهمة
                </h2>
            </div>
            <div class="section-content">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">تاريخ الإنشاء</div>
                        <div class="info-value">{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">آخر تحديث</div>
                        <div class="info-value">
                            {% if req.updated_at %}
                                {{ req.updated_at.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                لم يتم التحديث
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- File Management Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-folder-open" style="color: #10b981;"></i>
                    إدارة المرفقات
                    {% if req.files %}
                    <span style="background: #f3f4f6; color: #374151; font-size: 12px; font-weight: 500; padding: 4px 8px; border-radius: 12px; margin-right: 8px;">
                        {{ req.files|length }} ملف
                    </span>
                    {% endif %}
                </h2>
            </div>
            <div class="section-content">
                <!-- Existing Files Management -->
                {% if req.files %}
                <div style="margin-bottom: 32px;">
                    <div class="section-header-mobile" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="font-size: 16px; font-weight: 600; color: #111827; margin: 0;">الملفات المرفقة</h3>
                        <div class="section-subtitle" style="font-size: 12px; color: #6b7280;">يمكنك عرض، تحميل، أو حذف الملفات</div>
                    </div>
                    <div class="existing-files-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 16px;">
                        {% for file in req.files %}
                        <div class="existing-file" id="file-{{ file.id }}">
                            <div class="file-info-mobile">
                                <div class="file-header-mobile">
                                    <div class="file-icon
                                        {% if file.file_type.lower() == 'pdf' %}pdf
                                        {% elif file.file_type.lower() in ['jpg', 'jpeg', 'png', 'gif'] %}image
                                        {% elif file.file_type.lower() in ['doc', 'docx'] %}document
                                        {% else %}default{% endif %}">
                                        {% if file.file_type.lower() == 'pdf' %}
                                            <i class="fas fa-file-pdf"></i>
                                        {% elif file.file_type.lower() in ['jpg', 'jpeg', 'png', 'gif'] %}
                                            <i class="fas fa-file-image"></i>
                                        {% elif file.file_type.lower() in ['doc', 'docx'] %}
                                            <i class="fas fa-file-word"></i>
                                        {% else %}
                                            <i class="fas fa-file"></i>
                                        {% endif %}
                                    </div>
                                    <div class="file-details-mobile">
                                        <div class="file-name">{{ file.original_filename }}</div>
                                        <div class="file-meta-mobile">
                                            <span style="font-size: 11px; color: #6b7280;">{{ file.file_category }}</span>
                                            <span style="font-size: 11px; color: #6b7280;">{{ (file.file_size / 1024 / 1024)|round(2) }} MB</span>
                                            <span style="background: #f3f4f6; color: #374151; font-size: 10px; font-weight: 500; padding: 2px 6px; border-radius: 4px;">{{ file.file_type.upper() }}</span>
                                        </div>
                                        <div style="font-size: 11px; color: #6b7280; margin-top: 2px;">{{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') if file.uploaded_at else 'غير محدد' }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="file-actions">
                                <button type="button" onclick="viewFile({{ file.id }}, '{{ file.original_filename }}')" class="btn btn-outline" style="padding: 6px 12px; font-size: 11px;">
                                    <i class="fas fa-eye"></i>
                                    عرض
                                </button>
                                <button type="button" onclick="downloadFile({{ file.id }})" class="btn btn-primary" style="padding: 6px 12px; font-size: 11px;">
                                    <i class="fas fa-download"></i>
                                    تحميل
                                </button>
                                <button type="button" onclick="deleteFile({{ file.id }}, '{{ file.original_filename|replace("'", "\\'") }}')" style="background: #fee2e2; color: #991b1b; border: 1px solid #fecaca; padding: 6px 12px; border-radius: 6px; font-size: 11px; cursor: pointer;">
                                    <i class="fas fa-trash"></i>
                                    حذف
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="mobile-info-box" style="margin-top: 16px; background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px; padding: 12px;">
                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                            <i class="fas fa-info-circle" style="color: #2563eb; margin-top: 2px; flex-shrink: 0;"></i>
                            <div style="color: #1e40af; font-size: 13px; line-height: 1.4;">
                                يمكنك إدارة الملفات مباشرة من هذه الصفحة. لإضافة ملفات جديدة، استخدم قسم "إضافة ملفات جديدة" أدناه.
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div style="text-align: center; padding: 32px; margin-bottom: 32px;">
                    <i class="fas fa-file-upload" style="font-size: 48px; color: #9ca3af; margin-bottom: 16px;"></i>
                    <h4 style="font-size: 18px; font-weight: 600; color: #111827; margin: 0 0 8px 0;">لا توجد ملفات مرفقة</h4>
                    <p style="color: #6b7280; margin: 0;">لم يتم رفع أي ملفات مع هذا الطلب بعد</p>
                </div>
                {% endif %}

                <!-- File Upload Areas -->
                <div style="border-top: 1px solid #e5e7eb; padding-top: 24px;">
                    <div class="section-header-mobile" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="font-size: 16px; font-weight: 600; color: #111827; margin: 0;">إضافة ملفات جديدة</h3>
                        <div class="section-subtitle" style="font-size: 12px; color: #6b7280;">يمكنك رفع ملفات متعددة في فئات مختلفة</div>
                    </div>

                    <div class="upload-areas-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <!-- Architectural Plans -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-drafting-compass" style="color: #2563eb; margin-left: 4px;"></i>
                                المخططات المعمارية
                            </label>
                            <div class="upload-area" onclick="document.getElementById('architectural_plans').click()">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3 class="upload-text" style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">اختر ملفات أو اسحبها هنا</h3>
                                <p class="upload-hint" style="margin: 0; color: #6b7280; font-size: 12px;">PDF, JPG, PNG</p>
                                <input type="file" name="architectural_plans" id="architectural_plans" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                            </div>
                            <div id="architectural_plans_preview" class="file-preview"></div>
                        </div>

                        <!-- Electrical & Mechanical Plans -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-bolt" style="color: #f59e0b; margin-left: 4px;"></i>
                                المخططات الكهربائية والميكانيكية
                            </label>
                            <div class="upload-area" onclick="document.getElementById('electrical_mechanical_plans').click()">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3 class="upload-text" style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">اختر ملفات أو اسحبها هنا</h3>
                                <p class="upload-hint" style="margin: 0; color: #6b7280; font-size: 12px;">PDF, JPG, PNG</p>
                                <input type="file" name="electrical_mechanical_plans" id="electrical_mechanical_plans" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                            </div>
                            <div id="electrical_mechanical_plans_preview" class="file-preview"></div>
                        </div>

                        <!-- Inspection Department -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-search" style="color: #10b981; margin-left: 4px;"></i>
                                إدارة التفتيش
                            </label>
                            <div class="upload-area" onclick="document.getElementById('inspection_department').click()">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3 class="upload-text" style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">اختر ملفات أو اسحبها هنا</h3>
                                <p class="upload-hint" style="margin: 0; color: #6b7280; font-size: 12px;">PDF, JPG, PNG</p>
                                <input type="file" name="inspection_department" id="inspection_department" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                            </div>
                            <div id="inspection_department_preview" class="file-preview"></div>
                        </div>

                        <!-- Conditional File Sections -->
                        <div id="fire_equipment_files_section" class="form-group" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-fire-extinguisher" style="color: #dc2626; margin-left: 4px;"></i>
                                ملفات معدات الحريق
                            </label>
                            <div class="upload-area" onclick="document.getElementById('fire_equipment_files').click()">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">اختر ملفات أو اسحبها هنا</h3>
                                <p style="margin: 0; color: #6b7280; font-size: 12px;">PDF, JPG, PNG</p>
                                <input type="file" name="fire_equipment_files" id="fire_equipment_files" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                            </div>
                            <div id="fire_equipment_files_preview" class="file-preview"></div>
                        </div>

                        <div id="commercial_records_files_section" class="form-group" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-building" style="color: #7c3aed; margin-left: 4px;"></i>
                                ملفات السجلات التجارية
                            </label>
                            <div class="upload-area" onclick="document.getElementById('commercial_records_files').click()">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">اختر ملفات أو اسحبها هنا</h3>
                                <p style="margin: 0; color: #6b7280; font-size: 12px;">PDF, JPG, PNG</p>
                                <input type="file" name="commercial_records_files" id="commercial_records_files" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                            </div>
                            <div id="commercial_records_files_preview" class="file-preview"></div>
                        </div>

                        <div id="engineering_offices_files_section" class="form-group" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-hard-hat" style="color: #ea580c; margin-left: 4px;"></i>
                                ملفات المكاتب الهندسية
                            </label>
                            <div class="upload-area" onclick="document.getElementById('engineering_offices_files').click()">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">اختر ملفات أو اسحبها هنا</h3>
                                <p style="margin: 0; color: #6b7280; font-size: 12px;">PDF, JPG, PNG</p>
                                <input type="file" name="engineering_offices_files" id="engineering_offices_files" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                            </div>
                            <div id="engineering_offices_files_preview" class="file-preview"></div>
                        </div>

                        <div id="hazardous_materials_files_section" class="form-group" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-exclamation-triangle" style="color: #f59e0b; margin-left: 4px;"></i>
                                ملفات المواد الخطرة
                            </label>
                            <div class="upload-area" onclick="document.getElementById('hazardous_materials_files').click()">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">اختر ملفات أو اسحبها هنا</h3>
                                <p style="margin: 0; color: #6b7280; font-size: 12px;">PDF, JPG, PNG</p>
                                <input type="file" name="hazardous_materials_files" id="hazardous_materials_files" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                            </div>
                            <div id="hazardous_materials_files_preview" class="file-preview"></div>
                        </div>
                    </div>

                    <!-- Upload Instructions -->
                    <div style="margin-top: 24px; background: #ecfdf5; border: 1px solid #a7f3d0; border-radius: 6px; padding: 16px;">
                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                            <i class="fas fa-lightbulb" style="color: #059669; margin-top: 2px;"></i>
                            <div>
                                <h5 style="font-weight: 600; color: #065f46; font-size: 14px; margin: 0 0 8px 0;">نصائح لرفع الملفات:</h5>
                                <ul style="color: #065f46; font-size: 13px; margin: 0; padding-right: 16px;">
                                    <li>يمكنك رفع ملفات متعددة في كل فئة</li>
                                    <li>استخدم السحب والإفلات لسهولة الرفع</li>
                                    <li>الأنواع المدعومة: PDF, JPG, PNG</li>
                                    <li>يمكنك إزالة الملفات من المعاينة قبل الحفظ</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="section">
            <div class="section-content">
                <div class="form-actions-desktop" style="display: flex; justify-content: space-between; align-items: center; gap: 16px;">
                    <a href="/requests/{{ req.id }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                    <button type="submit" id="submitBtn" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </div>
                <div class="form-actions-mobile" style="display: none;">
                    <button type="submit" id="submitBtnMobile" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                    <a href="/requests/{{ req.id }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>





<script>
// Professional Edit Request JavaScript
const REQUEST_ID = {{ req.id }};

document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing professional edit request form...');

    // Initialize file uploads
    const fileInputs = [
        'architectural_plans',
        'electrical_mechanical_plans',
        'inspection_department',
        'fire_equipment_files',
        'commercial_records_files',
        'engineering_offices_files',
        'hazardous_materials_files'
    ];

    fileInputs.forEach(inputId => {
        initializeFileUpload(inputId, inputId + '_preview');
    });

    // Set up conditional sections based on existing request data
    const conditionalSections = [
        ['fire_equipment_section', 'fire_equipment_files_section'],
        ['commercial_records_section', 'commercial_records_files_section'],
        ['engineering_offices_section', 'engineering_offices_files_section'],
        ['hazardous_materials_section', 'hazardous_materials_files_section']
    ];

    conditionalSections.forEach(([checkboxId, sectionId]) => {
        // Show sections based on existing request data
        const section = document.getElementById(sectionId);
        if (section) {
            {% if req.fire_equipment_section %}
            if (sectionId === 'fire_equipment_files_section') section.style.display = 'block';
            {% endif %}
            {% if req.commercial_records_section %}
            if (sectionId === 'commercial_records_files_section') section.style.display = 'block';
            {% endif %}
            {% if req.engineering_offices_section %}
            if (sectionId === 'engineering_offices_files_section') section.style.display = 'block';
            {% endif %}
            {% if req.hazardous_materials_section %}
            if (sectionId === 'hazardous_materials_files_section') section.style.display = 'block';
            {% endif %}
        }
    });

    // Handle form submission for both desktop and mobile buttons
    const form = document.getElementById('editRequestForm');
    const submitBtnDesktop = document.getElementById('submitBtn');
    const submitBtnMobile = document.getElementById('submitBtnMobile');

    function validateForm() {
        const fullName = document.getElementById('full_name');
        const personalNumber = document.getElementById('personal_number');

        console.log('Validating form...'); // Debug log
        console.log('Full name:', fullName ? fullName.value : 'not found');
        console.log('Personal number:', personalNumber ? personalNumber.value : 'not found');

        if (!fullName || !fullName.value.trim()) {
            alert('يرجى إدخال الاسم الثلاثي');
            if (fullName) fullName.focus();
            return false;
        }

        if (!personalNumber || !personalNumber.value.trim()) {
            alert('يرجى إدخال الرقم الشخصي');
            if (personalNumber) personalNumber.focus();
            return false;
        }

        if (personalNumber.value.trim().length !== 9) {
            alert('الرقم الشخصي يجب أن يكون 9 أرقام بالضبط');
            personalNumber.focus();
            return false;
        }

        return true;
    }

    function handleFormSubmission(button) {
        console.log('Form submission triggered by button:', button.id); // Debug log

        if (!validateForm()) {
            return false;
        }

        // Show loading state
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

        // Re-enable button after 10 seconds to prevent permanent disable
        setTimeout(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        }, 10000);

        return true;
    }

    // Add click event listeners to submit buttons
    if (submitBtnDesktop) {
        submitBtnDesktop.addEventListener('click', function(e) {
            console.log('Desktop submit button clicked'); // Debug log
            e.preventDefault(); // Always prevent default first

            if (handleFormSubmission(this)) {
                console.log('Validation passed, submitting form...'); // Debug log
                form.submit();
            } else {
                console.log('Validation failed'); // Debug log
            }
        });
    }

    if (submitBtnMobile) {
        submitBtnMobile.addEventListener('click', function(e) {
            console.log('Mobile submit button clicked'); // Debug log
            e.preventDefault(); // Always prevent default first

            if (handleFormSubmission(this)) {
                console.log('Validation passed, submitting form...'); // Debug log
                form.submit();
            } else {
                console.log('Validation failed'); // Debug log
            }
        });
    }

    // Remove the form submit event listener to avoid conflicts
    // The button click handlers will handle form submission

    // Add debugging for form elements - only run on edit request page
    document.addEventListener('DOMContentLoaded', function() {
        // Check if we're on the edit request page by looking for the form
        const editForm = document.getElementById('editRequestForm');
        if (editForm) {
            console.log('DOM loaded, checking form elements...');
            console.log('Form:', editForm);
            console.log('Desktop button:', document.getElementById('submitBtn'));
            console.log('Mobile button:', document.getElementById('submitBtnMobile'));
            console.log('Full name field:', document.getElementById('full_name'));
            console.log('Personal number field:', document.getElementById('personal_number'));
        }
    });

    console.log('Professional edit request form initialized successfully');
});

// Professional file upload handling
function initializeFileUpload(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);
    const uploadArea = input ? input.parentElement : null;

    if (!input || !preview) return;

    input.addEventListener('change', function() {
        updateFilePreview(this, preview);
    });

    // Drag and drop functionality
    if (uploadArea) {
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = '#3b82f6';
            this.style.backgroundColor = '#eff6ff';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '#d1d5db';
            this.style.backgroundColor = '#f9fafb';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#d1d5db';
            this.style.backgroundColor = '#f9fafb';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const dt = new DataTransfer();
                Array.from(files).forEach(file => dt.items.add(file));
                input.files = dt.files;
                updateFilePreview(input, preview);
            }
        });
    }
}

function updateFilePreview(input, previewContainer) {
    previewContainer.innerHTML = '';

    if (input.files && input.files.length > 0) {
        Array.from(input.files).forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <div class="file-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="file-details">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                    </div>
                </div>
                <button type="button" onclick="removeFile('${input.id}', ${index})" class="btn btn-outline" style="padding: 4px 8px; font-size: 12px;">
                    <i class="fas fa-times"></i>
                </button>
            `;
            previewContainer.appendChild(fileItem);
        });
    }
}

function removeFile(inputId, index) {
    const input = document.getElementById(inputId);
    const dt = new DataTransfer();

    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    input.files = dt.files;
    updateFilePreview(input, document.getElementById(inputId + '_preview'));
}

// Professional file management functions
function viewFile(fileId, fileName) {
    window.open(`/files/view/${fileId}`, '_blank');
}

function downloadFile(fileId) {
    window.open(`/files/download/${fileId}`, '_blank');
}

function deleteFile(fileId, fileName) {
    if (confirm(`هل أنت متأكد من حذف الملف: ${fileName}؟\n\nلا يمكن التراجع عن هذا الإجراء.`)) {
        confirmDeleteFile(fileId);
    }
}

function confirmDeleteFile(fileId) {
    console.log('Deleting file:', fileId, 'from request:', REQUEST_ID);

    fetch(`/requests/${REQUEST_ID}/files/${fileId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            return response.text().then(text => {
                throw new Error(`HTTP error! status: ${response.status} - ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showAlert('تم حذف الملف بنجاح', 'success');
            // Remove file from UI
            const fileElement = document.getElementById(`file-${fileId}`);
            if (fileElement) {
                fileElement.style.opacity = '0';
                setTimeout(() => fileElement.remove(), 300);
            }
        } else {
            showAlert(`حدث خطأ في حذف الملف: ${data.error || 'خطأ غير معروف'}`, 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting file:', error);
        showAlert(`حدث خطأ في حذف الملف: ${error.message}`, 'error');
    });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.style.cssText = `
        position: fixed; top: 20px; right: 20px; z-index: 1000;
        padding: 16px; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        background: ${type === 'success' ? '#ecfdf5' : '#fef2f2'};
        color: ${type === 'success' ? '#065f46' : '#991b1b'};
        border: 1px solid ${type === 'success' ? '#a7f3d0' : '#fecaca'};
        font-size: 14px; max-width: 400px;
    `;
    alertDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="margin-right: 12px; background: none; border: none; cursor: pointer; color: inherit;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

console.log('Professional edit request JavaScript loaded successfully');
</script>

{% endblock %}