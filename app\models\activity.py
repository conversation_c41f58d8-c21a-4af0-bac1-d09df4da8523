from sqlalchemy import <PERSON>umn, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Text, Enum, J<PERSON><PERSON>
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum


class ActivityType(enum.Enum):
    LOGIN = "login"
    LOGOUT = "logout"
    REQUEST_CREATED = "request_created"
    REQUEST_UPDATED = "request_updated"
    REQUEST_COMPLETED = "request_completed"
    REQUEST_REJECTED = "request_rejected"
    FILE_UPLOAD = "file_upload"
    FILE_UPLOADED = "file_uploaded"
    FILE_DELETED = "file_deleted"
    COMMENT_ADD = "comment_add"
    STATUS_CHANGE = "status_change"
    PROFILE_UPDATED = "profile_updated"
    AVATAR_UPLOADED = "avatar_uploaded"
    PASSWORD_CHANGED = "password_changed"
    DATA_EXPORTED = "data_exported"
    SYSTEM_UPDATE = "system_update"
    # Cross-user request management activities
    CROSS_USER_REQUEST_VIEWED = "cross_user_request_viewed"
    CROSS_USER_REQUEST_EDITED = "cross_user_request_edited"
    CROSS_USER_REQUEST_STATUS_UPDATED = "cross_user_request_status_updated"
    CROSS_USER_FILE_ACCESSED = "cross_user_file_accessed"
    CROSS_USER_FILE_DELETED = "cross_user_file_deleted"

    def get_arabic_name(self):
        """Get Arabic translation for activity type"""
        arabic_names = {
            self.LOGIN: "تسجيل دخول",
            self.LOGOUT: "تسجيل خروج",
            self.REQUEST_CREATED: "إنشاء طلب",
            self.REQUEST_UPDATED: "تحديث طلب",
            self.REQUEST_COMPLETED: "إكمال طلب",
            self.REQUEST_REJECTED: "رفض طلب",
            self.FILE_UPLOAD: "رفع ملف",
            self.FILE_UPLOADED: "رفع ملف",
            self.FILE_DELETED: "حذف ملف",
            self.COMMENT_ADD: "إضافة تعليق",
            self.STATUS_CHANGE: "تغيير الحالة",
            self.PROFILE_UPDATED: "تحديث الملف الشخصي",
            self.AVATAR_UPLOADED: "رفع صورة شخصية",
            self.PASSWORD_CHANGED: "تغيير كلمة المرور",
            self.DATA_EXPORTED: "تصدير البيانات",
            self.SYSTEM_UPDATE: "تحديث النظام",
            self.CROSS_USER_REQUEST_VIEWED: "عرض طلب مستخدم آخر",
            self.CROSS_USER_REQUEST_EDITED: "تعديل طلب مستخدم آخر",
            self.CROSS_USER_REQUEST_STATUS_UPDATED: "تحديث حالة طلب مستخدم آخر",
            self.CROSS_USER_FILE_ACCESSED: "الوصول لملف مستخدم آخر",
            self.CROSS_USER_FILE_DELETED: "حذف ملف مستخدم آخر"
        }
        return arabic_names.get(self, self.value)


class Activity(Base):
    __tablename__ = "activities"

    id = Column(Integer, primary_key=True, index=True)
    activity_type = Column(Enum(ActivityType), nullable=False)
    description = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)  # Additional details as JSON
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="activities")

    def __repr__(self):
        return f"<Activity(type='{self.activity_type}', user_id={self.user_id})>"
